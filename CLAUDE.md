# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ipInsight is a high-performance IP geolocation service built in Go with a React dashboard. It aggregates data from multiple free IP databases and APIs to provide comprehensive geolocation information.

## Architecture

- **Backend**: Go API server using Gin framework with PostgreSQL and Redis
- **Frontend**: React/TypeScript dashboard with Material-UI components
- **Data Processing**: Modular data source parsers for CSV, MMDB, and API formats
- **Scheduling**: Automated data source updates using gocron
- **Deployment**: Docker containerized with docker-compose orchestration

Key architectural components:
- `cmd/ipInsight/main.go`: Application entry point
- `internal/api/`: Gin HTTP handlers and routing
- `internal/datasource/`: Data source parsers and update logic
- `internal/database/`: PostgreSQL interface and optimized queries
- `internal/cache/`: Redis caching layer
- `internal/service/`: Business logic and data fusion
- `dashboard/`: React frontend application

## Development Commands

### Backend (Go)
```bash
# Run the application locally
go run cmd/ipInsight/main.go

# Build the application
go build -o ipInsight cmd/ipInsight/main.go

# Run tests
go test ./...

# Run specific package tests
go test ./internal/datasource/...

# Format code
go fmt ./...

# Vet code
go vet ./...
```

### Frontend (React Dashboard)
```bash
cd dashboard

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Lint TypeScript
npm run lint

# Preview production build
npm run preview
```

### Docker Development
```bash
# Start full stack (recommended for development)
docker-compose up -d

# Rebuild and start
docker-compose up --build

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Testing API
```bash
# Run the included API test script
./test_api.sh

# Manual health check
curl http://localhost:8080/health

# Query single IP
curl http://localhost:8080/ip/*******

# Batch IP query
curl -X POST http://localhost:8080/ip/batch \
  -H "Content-Type: application/json" \
  -d '{"ips": ["*******", "*******"]}'
```

## Configuration

- Main config: `config/config.yaml` (copy from `config/config.example.yaml`)
- Database credentials and API keys are configured in the YAML file
- Environment variables can override config values
- Data sources require API keys/tokens for some providers (MaxMind, IP2Location, etc.)

## Data Sources

The system supports multiple IP geolocation data sources:
- **File Sources**: MaxMind (MMDB), IP2Location (CSV), DB-IP (MMDB), QQWry
- **API Sources**: ipinfo.io, IPGeolocation.io
- **Free Sources**: ipapi.is, IPLocate.io, IPInfoDB

Each data source has its own parser in `internal/datasource/` with corresponding test files.

## Database Schema

- Main IP data stored in PostgreSQL with CIDR indexing for range queries
- User authentication and session management tables
- Optimized queries for high-performance IP lookups
- See `docs/database_schema.md` for detailed schema information

## Key Development Notes

- Use `stretchr/testify` for unit tests
- Database queries use pgx/v5 driver with connection pooling
- Redis caching layer for frequently queried IPs
- JWT-based authentication for admin endpoints
- Rate limiting implemented for API endpoints
- Prometheus metrics available for monitoring

## Testing

Run unit tests for specific components:
```bash
# Test all datasource parsers
go test ./internal/datasource/...

# Test specific data source
go test ./internal/datasource/maxmind/

# Run with verbose output
go test -v ./internal/datasource/utils_test.go

# Benchmark tests
go test -bench=. ./scripts/benchmark_insert.go
```

The project includes comprehensive test files for all major data source parsers.