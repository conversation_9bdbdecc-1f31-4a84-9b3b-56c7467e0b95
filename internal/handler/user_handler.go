package handler

import (
	"net/http"
	"strconv"

	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UserHandler 用户管理处理器
type UserHandler struct {
	userRepo database.UserRepository
	logger   *zap.Logger
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userRepo database.UserRepository, logger *zap.Logger) *UserHandler {
	return &UserHandler{
		userRepo: userRepo,
		logger:   logger,
	}
}

// CreateUser 创建用户
// @Summary 创建新用户
// @Description 创建一个新的用户账户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body model.CreateUserRequest true "用户信息"
// @Success 201 {object} model.User
// @Failure 400 {object} gin.H
// @Failure 409 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/admin/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req model.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	// 获取当前用户ID（从JWT中间件设置）
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user, err := h.userRepo.CreateUser(c.Request.Context(), &req, currentUserID.(int64))
	if err != nil {
		h.logger.Error("Failed to create user", zap.Error(err), zap.String("username", req.Username))
		if err.Error() == "user already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": "Username or email already exists"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// 记录活动日志
	userID, ok := currentUserID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user, user_id type assertion failed"})
		return
	}
	h.userRepo.LogActivity(c.Request.Context(), &userID, "create_user", "user", strconv.FormatInt(user.ID, 10), nil, c.ClientIP(), c.GetHeader("User-Agent"), true, "")

	c.JSON(http.StatusCreated, user.SanitizeForResponse())
}

// GetUser 获取用户信息
// @Summary 获取用户信息
// @Description 根据用户ID获取用户详细信息
// @Tags 用户管理
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} model.User
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/admin/users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	user, err := h.userRepo.GetUserByID(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to get user", zap.Error(err), zap.Int64("user_id", id))
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user.SanitizeForResponse())
}

// UpdateUser 更新用户信息
// @Summary 更新用户信息
// @Description 更新用户的基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param user body model.UpdateUserRequest true "更新信息"
// @Success 200 {object} model.User
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/admin/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req model.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	currentUserID, _ := c.Get("user_id")
	user, err := h.userRepo.UpdateUser(c.Request.Context(), id, &req, currentUserID.(int64))
	if err != nil {
		h.logger.Error("Failed to update user", zap.Error(err), zap.Int64("user_id", id))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}
	userID, ok := currentUserID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user, user_id type assertion failed"})
		return
	}
	// 记录活动日志
	h.userRepo.LogActivity(c.Request.Context(), &userID, "update_user", "user", strconv.FormatInt(id, 10), nil, c.ClientIP(), c.GetHeader("User-Agent"), true, "")

	c.JSON(http.StatusOK, user.SanitizeForResponse())
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 软删除用户账户
// @Tags 用户管理
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/admin/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	currentUserID, _ := c.Get("user_id")

	// 不能删除自己
	if id == currentUserID.(int64) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete your own account"})
		return
	}

	err = h.userRepo.DeleteUser(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to delete user", zap.Error(err), zap.Int64("user_id", id))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	userID, ok := currentUserID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user, user_id type assertion failed"})
		return
	}
	// 记录活动日志
	h.userRepo.LogActivity(c.Request.Context(), &userID, "delete_user", "user", strconv.FormatInt(id, 10), nil, c.ClientIP(), c.GetHeader("User-Agent"), true, "")

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// ListUsers 获取用户列表
// @Summary 获取用户列表
// @Description 分页获取用户列表，支持按角色和状态筛选
// @Tags 用户管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param role query string false "角色筛选"
// @Param status query string false "状态筛选"
// @Success 200 {object} model.UserListResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/admin/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	role := c.Query("role")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	response, err := h.userRepo.ListUsers(c.Request.Context(), page, pageSize, role, status)
	if err != nil {
		h.logger.Error("Failed to list users", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve users"})
		return
	}

	// 清理敏感信息
	for i := range response.Users {
		response.Users[i] = *response.Users[i].SanitizeForResponse()
	}

	c.JSON(http.StatusOK, response)
}

// ChangePassword 修改密码
// @Summary 修改用户密码
// @Description 用户修改自己的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param password body model.ChangePasswordRequest true "密码信息"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/users/change-password [post]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	var req model.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.userRepo.ChangePassword(c.Request.Context(), currentUserID.(int64), req.CurrentPassword, req.NewPassword)
	if err != nil {
		h.logger.Error("Failed to change password", zap.Error(err), zap.Int64("user_id", currentUserID.(int64)))
		if err.Error() == "current password is incorrect" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Current password is incorrect"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to change password"})
		return
	}

	userID, ok := currentUserID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to change password, user_id type assertion failed"})
		return
	}
	// 记录活动日志
	h.userRepo.LogActivity(c.Request.Context(), &userID, "change_password", "user", strconv.FormatInt(currentUserID.(int64), 10), nil, c.ClientIP(), c.GetHeader("User-Agent"), true, "")

	c.JSON(http.StatusOK, gin.H{"message": "Password changed successfully"})
}

// GenerateAPIKey 生成API密钥
// @Summary 生成API密钥
// @Description 为用户生成新的API密钥
// @Tags 用户管理
// @Produce json
// @Success 200 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/users/api-key [post]
func (h *UserHandler) GenerateAPIKey(c *gin.Context) {
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	apiKey, err := h.userRepo.GenerateAPIKey(c.Request.Context(), currentUserID.(int64))
	if err != nil {
		h.logger.Error("Failed to generate API key", zap.Error(err), zap.Int64("user_id", currentUserID.(int64)))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key"})
		return
	}
	userID, ok := currentUserID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key, user_id type assertion failed"})
		return
	}
	// 记录活动日志
	h.userRepo.LogActivity(c.Request.Context(), &userID, "generate_api_key", "user", strconv.FormatInt(currentUserID.(int64), 10), nil, c.ClientIP(), c.GetHeader("User-Agent"), true, "")

	c.JSON(http.StatusOK, gin.H{
		"api_key": apiKey,
		"message": "API key generated successfully",
		"expires": "Never (until revoked)",
	})
}

// RevokeAPIKey 撤销API密钥
// @Summary 撤销API密钥
// @Description 撤销用户的API密钥
// @Tags 用户管理
// @Produce json
// @Success 200 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/users/api-key [delete]
func (h *UserHandler) RevokeAPIKey(c *gin.Context) {
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.userRepo.RevokeAPIKey(c.Request.Context(), currentUserID.(int64))
	if err != nil {
		h.logger.Error("Failed to revoke API key", zap.Error(err), zap.Int64("user_id", currentUserID.(int64)))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke API key"})
		return
	}
	userID, ok := currentUserID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke API key, user_id type assertion failed"})
		return
	}
	// 记录活动日志
	h.userRepo.LogActivity(c.Request.Context(), &userID, "revoke_api_key", "user", strconv.FormatInt(currentUserID.(int64), 10), nil, c.ClientIP(), c.GetHeader("User-Agent"), true, "")

	c.JSON(http.StatusOK, gin.H{"message": "API key revoked successfully"})
}
