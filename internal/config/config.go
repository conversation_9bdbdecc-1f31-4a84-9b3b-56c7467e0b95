package config

import (
	"fmt"
	"strings"

	"github.com/cosin2077/ipInsight/internal/errors"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type DBType string

const (
	PostgreSQL DBType = "postgresql"
	MySQL      DBType = "mysql"
	SQLite     DBType = "sqlite"
	MongoDB    DBType = "mongodb"
	MariaDB    DBType = "mariadb"
)

type Config struct {
	Server       ServerConfig       `mapstructure:"server"`
	Database     DatabaseConfig     `mapstructure:"database"`
	Cache        CacheConfig        `mapstructure:"cache"`
	Datasources  DatasourcesConfig  `mapstructure:"datasources"`
	Auth         AuthConfig         `mapstructure:"auth"`
	Startup      StartupConfig      `mapstructure:"startup"`
	IPCompletion IPCompletionConfig `mapstructure:"ip_completion"`
}

type ServerConfig struct {
	Port int `mapstructure:"port"`
}

type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
}

type CacheConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
}

type AuthConfig struct {
	JWTSecret    string            `mapstructure:"jwt_secret"`
	APIKeys      []string          `mapstructure:"api_keys"`
	TokenTTL     int               `mapstructure:"token_ttl"` // JWT token TTL in hours
	AdminUsers   []string          `mapstructure:"admin_users"`
	DefaultAdmin *DefaultAdminUser `mapstructure:"default_admin"` // 默认管理员用户配置
}

// DefaultAdminUser 默认管理员用户配置
type DefaultAdminUser struct {
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Email    string `mapstructure:"email"`
	FullName string `mapstructure:"full_name"`
	Enabled  bool   `mapstructure:"enabled"` // 是否启用默认管理员用户创建
}

type StartupConfig struct {
	AutoUpdateDatasources bool `mapstructure:"auto_update_datasources"` // 是否在启动时自动更新数据源
}

type DatasourcesConfig struct {
	Maxmind       DatasourceConfig `mapstructure:"maxmind"`
	IP2Location   DatasourceConfig `mapstructure:"ip2location"`
	DBIP          DatasourceConfig `mapstructure:"dbip"`
	IPInfo        APIConfig        `mapstructure:"ipinfo"`
	IPStack       APIConfig        `mapstructure:"ipstack"`
	IPGeolocation APIConfig        `mapstructure:"ipgeolocation"`
	IPInfoDB      DatasourceConfig `mapstructure:"ipinfodb"`
	IPAPI         DatasourceConfig `mapstructure:"ipapi"`
	IPLocate      DatasourceConfig `mapstructure:"iplocate"`
	QQWry         DatasourceConfig `mapstructure:"qqwry"`
}

type DatasourceConfig struct {
	URL      []string       `mapstructure:"url"`
	Schedule string         `mapstructure:"schedule"`
	Enabled  bool           `mapstructure:"enabled"`
	Database DatabaseConfig `mapstructure:"databaseConfig"`
}

type APIConfig struct {
	APIKey    string   `mapstructure:"api_key"`
	URL       []string `mapstructure:"url"`
	RateLimit int      `mapstructure:"rate_limit"`
}

// IPCompletionConfig IP地理信息补全配置
type IPCompletionConfig struct {
	Enabled                bool   `mapstructure:"enabled"`                  // 是否启用IP补全功能
	APIKey                 string `mapstructure:"api_key"`                  // ipapi.co API密钥
	BatchSize              int    `mapstructure:"batch_size"`               // 批处理大小
	ConcurrentLimit        int    `mapstructure:"concurrent_limit"`         // 并发限制
	EnableProxy            bool   `mapstructure:"enable_proxy"`             // 是否启用代理
	EnableCIDROptimization bool   `mapstructure:"enable_cidr_optimization"` // 是否启用CIDR优化
	ProxyConfigPath        string `mapstructure:"proxy_config_path"`        // 代理配置文件路径

	// 重试配置
	MaxRetries    int `mapstructure:"max_retries"`    // 最大重试次数
	InitialDelay  int `mapstructure:"initial_delay"`  // 初始延迟(毫秒)
	MaxDelay      int `mapstructure:"max_delay"`      // 最大延迟(毫秒)
	BackoffFactor int `mapstructure:"backoff_factor"` // 退避因子

	// 速率限制
	RequestsPerSecond int `mapstructure:"requests_per_second"` // 每秒请求数限制
}

func LoadConfig(logger *zap.Logger) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./config")

	// 设置默认值
	setDefaults()

	if err := viper.ReadInConfig(); err != nil {
		logger.Error("Failed to read config", zap.Error(err))
		return nil, errors.NewConfigError("Failed to read configuration file", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		logger.Error("Failed to unmarshal config", zap.Error(err))
		return nil, errors.NewConfigError("Failed to parse configuration", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		logger.Error("Configuration validation failed", zap.Error(err))
		return nil, err
	}

	logger.Info("Configuration loaded and validated successfully")
	return &config, nil
}

// setDefaults 设置配置默认值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.port", 8080)

	// 数据库默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.dbname", "ipinsight")

	// 缓存默认配置
	viper.SetDefault("cache.host", "localhost")
	viper.SetDefault("cache.port", 6379)

	// 认证默认配置
	viper.SetDefault("auth.token_ttl", 24) // 24小时

	// 启动配置默认值
	viper.SetDefault("startup.auto_update_datasources", false)

	// IP补全服务默认配置
	viper.SetDefault("ip_completion.enabled", false)
	viper.SetDefault("ip_completion.batch_size", 100)
	viper.SetDefault("ip_completion.concurrent_limit", 10)
	viper.SetDefault("ip_completion.requests_per_second", 10)
	viper.SetDefault("ip_completion.max_retries", 3)
	viper.SetDefault("ip_completion.initial_delay", 1000)
	viper.SetDefault("ip_completion.max_delay", 30000)
	viper.SetDefault("ip_completion.backoff_factor", 2)
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	var validationErrors []string

	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		validationErrors = append(validationErrors, "server.port must be between 1 and 65535")
	}

	// 验证数据库配置
	if config.Database.Host == "" {
		validationErrors = append(validationErrors, "database.host cannot be empty")
	}
	if config.Database.Port <= 0 || config.Database.Port > 65535 {
		validationErrors = append(validationErrors, "database.port must be between 1 and 65535")
	}
	if config.Database.User == "" {
		validationErrors = append(validationErrors, "database.user cannot be empty")
	}
	if config.Database.DBName == "" {
		validationErrors = append(validationErrors, "database.dbname cannot be empty")
	}

	// 验证缓存配置
	if config.Cache.Host == "" {
		validationErrors = append(validationErrors, "cache.host cannot be empty")
	}
	if config.Cache.Port <= 0 || config.Cache.Port > 65535 {
		validationErrors = append(validationErrors, "cache.port must be between 1 and 65535")
	}

	// 验证认证配置
	if config.Auth.JWTSecret == "" {
		validationErrors = append(validationErrors, "auth.jwt_secret cannot be empty")
	}
	if len(config.Auth.JWTSecret) < 32 {
		validationErrors = append(validationErrors, "auth.jwt_secret must be at least 32 characters long")
	}
	if config.Auth.TokenTTL <= 0 {
		validationErrors = append(validationErrors, "auth.token_ttl must be greater than 0")
	}

	// 验证IP补全配置（如果启用）
	if config.IPCompletion.Enabled {
		if config.IPCompletion.BatchSize <= 0 {
			validationErrors = append(validationErrors, "ip_completion.batch_size must be greater than 0")
		}
		if config.IPCompletion.ConcurrentLimit <= 0 {
			validationErrors = append(validationErrors, "ip_completion.concurrent_limit must be greater than 0")
		}
		if config.IPCompletion.RequestsPerSecond <= 0 {
			validationErrors = append(validationErrors, "ip_completion.requests_per_second must be greater than 0")
		}
	}

	// 如果有验证错误，返回错误
	if len(validationErrors) > 0 {
		return errors.NewConfigError(
			"Configuration validation failed",
			fmt.Errorf("validation errors: %s", strings.Join(validationErrors, "; ")),
		)
	}

	return nil
}
