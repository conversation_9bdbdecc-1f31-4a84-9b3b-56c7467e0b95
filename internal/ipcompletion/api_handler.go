package ipcompletion

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// APIHandler IP地理信息补全API处理器
type APIHandler struct {
	completionService *CompletionService
	logger            *zap.Logger

	// 运行状态
	isRunning  bool
	cancelFunc context.CancelFunc
}

// NewAPIHandler 创建API处理器
func NewAPIHandler(completionService *CompletionService, logger *zap.Logger) *APIHandler {
	return &APIHandler{
		completionService: completionService,
		logger:            logger,
		isRunning:         false,
	}
}

// StartCompletionRequest 启动补全请求结构
type StartCompletionRequest struct {
	BatchSize       *int  `json:"batch_size,omitempty"`
	ConcurrentLimit *int  `json:"concurrent_limit,omitempty"`
	EnableProxy     *bool `json:"enable_proxy,omitempty"`
	EnableCIDR      *bool `json:"enable_cidr_optimization,omitempty"`
}

// CompletionStatusResponse 补全状态响应
type CompletionStatusResponse struct {
	IsRunning         bool                   `json:"is_running"`
	Stats             map[string]interface{} `json:"stats"`
	ProxyStats        map[string]interface{} `json:"proxy_stats,omitempty"`
	LastError         string                 `json:"last_error,omitempty"`
	ConfiguredProxies int                    `json:"configured_proxies,omitempty"`
	HealthyProxies    int                    `json:"healthy_proxies,omitempty"`
}

// StartCompletion 启动IP地理信息补全
// @Summary 启动IP地理信息补全
// @Description 启动IP地理信息补全任务，支持自定义配置参数
// @Tags IP补全
// @Accept json
// @Produce json
// @Param request body StartCompletionRequest false "补全配置参数"
// @Success 200 {object} map[string]interface{} "启动成功"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 409 {object} map[string]interface{} "补全任务已在运行"
// @Failure 500 {object} map[string]interface{} "内部服务器错误"
// @Router /api/v1/admin/ip-completion/start [post]
func (h *APIHandler) StartCompletion(c *gin.Context) {
	if h.isRunning {
		c.JSON(http.StatusConflict, gin.H{
			"error":   "IP completion is already running",
			"message": "请等待当前补全任务完成后再启动新任务",
		})
		return
	}

	var req StartCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(),
		})
		return
	}

	// 应用请求中的配置参数
	if req.BatchSize != nil && *req.BatchSize > 0 {
		h.completionService.config.BatchSize = *req.BatchSize
	}
	if req.ConcurrentLimit != nil && *req.ConcurrentLimit > 0 {
		h.completionService.config.ConcurrentLimit = *req.ConcurrentLimit
	}
	if req.EnableProxy != nil {
		h.completionService.config.EnableProxy = *req.EnableProxy
	}
	if req.EnableCIDR != nil {
		h.completionService.config.EnableCIDROptimization = *req.EnableCIDR
	}

	// 创建可取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	h.cancelFunc = cancel
	h.isRunning = true

	// 异步启动补全任务
	go func() {
		defer func() {
			h.isRunning = false
			h.cancelFunc = nil
		}()

		h.logger.Info("Starting IP geolocation completion task",
			zap.Int("batch_size", h.completionService.config.BatchSize),
			zap.Int("concurrent_limit", h.completionService.config.ConcurrentLimit),
			zap.Bool("enable_proxy", h.completionService.config.EnableProxy),
			zap.Bool("enable_cidr", h.completionService.config.EnableCIDROptimization))

		if err := h.completionService.StartCompletion(ctx); err != nil {
			h.logger.Error("IP completion task failed", zap.Error(err))
		} else {
			h.logger.Info("IP completion task completed successfully")
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"message": "IP geolocation completion started successfully",
		"config": gin.H{
			"batch_size":               h.completionService.config.BatchSize,
			"concurrent_limit":         h.completionService.config.ConcurrentLimit,
			"enable_proxy":             h.completionService.config.EnableProxy,
			"enable_cidr_optimization": h.completionService.config.EnableCIDROptimization,
		},
		"timestamp": time.Now().Unix(),
	})
}

// StopCompletion 停止IP地理信息补全
// @Summary 停止IP地理信息补全
// @Description 停止正在运行的IP地理信息补全任务
// @Tags IP补全
// @Produce json
// @Success 200 {object} map[string]interface{} "停止成功"
// @Failure 404 {object} map[string]interface{} "没有运行中的任务"
// @Router /api/v1/admin/ip-completion/stop [post]
func (h *APIHandler) StopCompletion(c *gin.Context) {
	if !h.isRunning || h.cancelFunc == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "No running completion task",
			"message": "当前没有运行中的IP补全任务",
		})
		return
	}

	h.logger.Info("Stopping IP geolocation completion task")
	h.cancelFunc()

	c.JSON(http.StatusOK, gin.H{
		"message":   "IP geolocation completion stopped successfully",
		"timestamp": time.Now().Unix(),
	})
}

// GetCompletionStatus 获取补全状态
// @Summary 获取IP地理信息补全状态
// @Description 获取当前IP地理信息补全任务的运行状态和统计信息
// @Tags IP补全
// @Produce json
// @Success 200 {object} CompletionStatusResponse "状态信息"
// @Router /api/v1/admin/ip-completion/status [get]
func (h *APIHandler) GetCompletionStatus(c *gin.Context) {
	stats := h.completionService.GetStats()

	response := CompletionStatusResponse{
		IsRunning: h.isRunning,
		Stats:     stats,
	}

	// 增强统计信息
	if h.isRunning {
		if startTime, ok := stats["start_time"].(time.Time); ok && !startTime.IsZero() {
			elapsed := time.Since(startTime)
			response.Stats["elapsed_time"] = elapsed.String()
			response.Stats["elapsed_seconds"] = int64(elapsed.Seconds())

			// 计算处理速率
			if totalProcessed, ok := stats["total_processed"].(int64); ok && totalProcessed > 0 {
				processingRate := float64(totalProcessed) / elapsed.Seconds()
				response.Stats["processing_rate"] = processingRate
				response.Stats["processing_rate_per_minute"] = processingRate * 60

				// 计算成功率
				if totalCompleted, ok := stats["total_completed"].(int64); ok {
					successRate := float64(totalCompleted) / float64(totalProcessed) * 100
					response.Stats["success_rate"] = successRate
				}

				// 计算进度和预估完成时间
				if estimatedRemaining, ok := stats["estimated_remaining"].(int64); ok && estimatedRemaining > 0 {
					totalEstimated := totalProcessed + estimatedRemaining
					progress := float64(totalProcessed) / float64(totalEstimated) * 100
					response.Stats["progress"] = progress

					if processingRate > 0 {
						remainingSeconds := float64(estimatedRemaining) / processingRate
						estimatedCompletion := time.Now().Add(time.Duration(remainingSeconds) * time.Second)
						response.Stats["estimated_completion"] = estimatedCompletion.Format("2006-01-02 15:04:05")
						response.Stats["estimated_remaining_seconds"] = int64(remainingSeconds)
						response.Stats["estimated_remaining_time"] = time.Duration(remainingSeconds * float64(time.Second)).String()
					}
				}
			}
		}

		// 添加当前批次信息
		if currentBatch, ok := stats["current_batch"].(int); ok && currentBatch > 0 {
			response.Stats["current_batch_info"] = map[string]interface{}{
				"batch_number": currentBatch,
				"batch_size":   h.completionService.config.BatchSize,
			}
		}

		// 添加最后更新时间信息
		if lastUpdate, ok := stats["last_update_time"].(time.Time); ok && !lastUpdate.IsZero() {
			response.Stats["last_update"] = lastUpdate.Format("2006-01-02 15:04:05")
			response.Stats["seconds_since_last_update"] = int64(time.Since(lastUpdate).Seconds())
		}
	}

	// 添加代理统计信息
	if h.completionService.proxyManager != nil {
		proxyStats := h.completionService.proxyManager.GetStats()
		response.ProxyStats = proxyStats

		if totalProxies, ok := proxyStats["total_proxies"].(int); ok {
			response.ConfiguredProxies = totalProxies
		}
		if healthyProxies, ok := proxyStats["healthy_proxies"].(int); ok {
			response.HealthyProxies = healthyProxies
		}
	}

	// 添加配置信息
	response.Stats["configuration"] = map[string]interface{}{
		"api_base_url":             h.completionService.config.APIBaseURL,
		"batch_size":               h.completionService.config.BatchSize,
		"concurrent_limit":         h.completionService.config.ConcurrentLimit,
		"enable_proxy":             h.completionService.config.EnableProxy,
		"enable_cidr_optimization": h.completionService.config.EnableCIDROptimization,
		"request_timeout":          h.completionService.config.RequestTimeout.String(),
		"max_retries":              h.completionService.config.RetryConfig.MaxRetries,
		"initial_delay":            h.completionService.config.RetryConfig.InitialDelay.String(),
		"max_delay":                h.completionService.config.RetryConfig.MaxDelay.String(),
		"backoff_factor":           h.completionService.config.RetryConfig.BackoffFactor,
	}

	c.JSON(http.StatusOK, response)
}

// GetProxyList 获取代理列表
// @Summary 获取代理列表
// @Description 获取当前配置的代理列表及其健康状态
// @Tags IP补全
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Success 200 {object} map[string]interface{} "代理列表"
// @Failure 404 {object} map[string]interface{} "代理管理器未配置"
// @Router /api/v1/admin/ip-completion/proxies [get]
func (h *APIHandler) GetProxyList(c *gin.Context) {
	if h.completionService.proxyManager == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Proxy manager not configured",
			"message": "代理管理器未配置",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	proxies := h.completionService.proxyManager.GetProxyList()
	total := len(proxies)

	// 计算分页
	start := (page - 1) * size
	end := start + size
	if start >= total {
		start = total
		end = total
	} else if end > total {
		end = total
	}

	var pagedProxies []*ProxyInfo
	if start < total {
		pagedProxies = proxies[start:end]
	} else {
		pagedProxies = []*ProxyInfo{}
	}

	c.JSON(http.StatusOK, gin.H{
		"proxies": pagedProxies,
		"pagination": gin.H{
			"page":        page,
			"size":        size,
			"total":       total,
			"total_pages": (total + size - 1) / size,
		},
		"summary": gin.H{
			"total_proxies":   total,
			"healthy_proxies": h.countHealthyProxies(proxies),
		},
		"timestamp": time.Now().Unix(),
	})
}

// ReloadProxies 重新加载代理配置
// @Summary 重新加载代理配置
// @Description 从配置文件重新加载代理列表
// @Tags IP补全
// @Produce json
// @Success 200 {object} map[string]interface{} "重新加载成功"
// @Failure 404 {object} map[string]interface{} "代理管理器未配置"
// @Failure 500 {object} map[string]interface{} "重新加载失败"
// @Router /api/v1/admin/ip-completion/proxies/reload [post]
func (h *APIHandler) ReloadProxies(c *gin.Context) {
	if h.completionService.proxyManager == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Proxy manager not configured",
			"message": "代理管理器未配置",
		})
		return
	}

	if err := h.completionService.proxyManager.LoadProxies(); err != nil {
		h.logger.Error("Failed to reload proxies", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to reload proxies",
			"message": err.Error(),
		})
		return
	}

	stats := h.completionService.proxyManager.GetStats()

	c.JSON(http.StatusOK, gin.H{
		"message":   "Proxies reloaded successfully",
		"stats":     stats,
		"timestamp": time.Now().Unix(),
	})
}

// GetDatabaseStats 获取数据库完整性统计信息
// @Summary 获取数据库完整性统计信息
// @Description 获取数据库中IP记录的完整性统计信息，包括缺失字段的数量和完整性比率
// @Tags IP补全
// @Produce json
// @Success 200 {object} map[string]interface{} "统计信息"
// @Failure 500 {object} map[string]interface{} "获取统计信息失败"
// @Router /api/v1/admin/ip-completion/database/stats [get]
func (h *APIHandler) GetDatabaseStats(c *gin.Context) {
	stats, err := h.completionService.getIncompleteIPsStats(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get database statistics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get database statistics",
			"message": err.Error(),
		})
		return
	}

	// 添加时间戳
	stats["timestamp"] = time.Now().Unix()
	stats["timestamp_formatted"] = time.Now().Format("2006-01-02 15:04:05")

	c.JSON(http.StatusOK, gin.H{
		"message": "Database statistics retrieved successfully",
		"stats":   stats,
	})
}

// countHealthyProxies 计算健康代理数量
func (h *APIHandler) countHealthyProxies(proxies []*ProxyInfo) int {
	count := 0
	for _, proxy := range proxies {
		if proxy.IsHealthy {
			count++
		}
	}
	return count
}
