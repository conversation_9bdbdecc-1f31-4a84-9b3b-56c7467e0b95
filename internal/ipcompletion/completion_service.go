package ipcompletion

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
)

// IPAPIResponse ipapi.co API响应结构
type IPAPIResponse struct {
	IP                 string  `json:"ip"`
	Version            string  `json:"version"`
	City               string  `json:"city"`
	Region             string  `json:"region"`
	RegionCode         string  `json:"region_code"`
	Country            string  `json:"country"`
	CountryName        string  `json:"country_name"`
	CountryCode        string  `json:"country_code"`
	CountryCodeISO3    string  `json:"country_code_iso3"`
	CountryCapital     string  `json:"country_capital"`
	CountryTLD         string  `json:"country_tld"`
	ContinentCode      string  `json:"continent_code"`
	InEU               bool    `json:"in_eu"`
	Postal             string  `json:"postal"`
	Latitude           float64 `json:"latitude"`
	Longitude          float64 `json:"longitude"`
	Timezone           string  `json:"timezone"`
	UTCOffset          string  `json:"utc_offset"`
	CountryCallingCode string  `json:"country_calling_code"`
	Currency           string  `json:"currency"`
	CurrencyName       string  `json:"currency_name"`
	Languages          string  `json:"languages"`
	Country_Area       float64 `json:"country_area"`
	Country_Population int64   `json:"country_population"`
	ASN                string  `json:"asn"`
	Org                string  `json:"org"`
}

// IPAPIIsResponse api.ipapi.is API响应结构
type IPAPIIsResponse struct {
	IP       string `json:"ip"`
	Location struct {
		Country     string  `json:"country"`
		CountryCode string  `json:"country_code"`
		State       string  `json:"state"`
		City        string  `json:"city"`
		Latitude    float64 `json:"latitude"`
		Longitude   float64 `json:"longitude"`
		ZipCode     string  `json:"zip_code"`
		Timezone    string  `json:"timezone"`
	} `json:"location"`
	ASN struct {
		Number int    `json:"number"`
		Descr  string `json:"descr"`
	} `json:"asn"`
	Company struct {
		Name   string `json:"name"`
		Domain string `json:"domain"`
		Type   string `json:"type"`
	} `json:"company"`
}

// APIProvider API提供商类型
type APIProvider string

const (
	APIProviderIPAPICo APIProvider = "ipapi.co"
	APIProviderIPAPIIs APIProvider = "ipapi.is"
)

// APIConfig API配置
type APIConfig struct {
	Provider APIProvider `json:"provider"`
	BaseURL  string      `json:"base_url"`
	Enabled  bool        `json:"enabled"`
}

// CompletionConfig 补全服务配置
type CompletionConfig struct {
	// 向后兼容的单API配置
	APIBaseURL string `yaml:"api_base_url"`
	APIKey     string `yaml:"api_key"` // ipapi.co API密钥

	// 多API配置
	APIs []APIConfig `yaml:"apis"` // 多API配置列表

	BatchSize       int           `yaml:"batch_size"`
	ConcurrentLimit int           `yaml:"concurrent_limit"`
	RequestTimeout  time.Duration `yaml:"request_timeout"`
	ProxyConfigPath string        `yaml:"proxy_config_path"` // 修正字段名
	EnableProxy     bool          `yaml:"enable_proxy"`

	// CIDR优化配置
	EnableCIDROptimization bool `yaml:"enable_cidr_optimization"`
	MaxCIDRSize            int  `yaml:"max_cidr_size"`

	// 数据库配置
	DBBatchSize int           `yaml:"db_batch_size"`
	DBTimeout   time.Duration `yaml:"db_timeout"`

	// 速率限制
	RequestsPerSecond int `yaml:"requests_per_second"`

	// 重试配置
	MaxRetries    int           `yaml:"max_retries"`
	InitialDelay  time.Duration `yaml:"initial_delay"`
	MaxDelay      time.Duration `yaml:"max_delay"`
	BackoffFactor float64       `yaml:"backoff_factor"`

	// 内部使用的重试配置对象
	RetryConfig RetryConfig `yaml:"-"`
}

// DefaultCompletionConfig 默认配置
func DefaultCompletionConfig() CompletionConfig {
	config := CompletionConfig{
		// 向后兼容的单API配置
		APIBaseURL: "https://ipapi.co",
		APIKey:     "",

		// 多API配置（优先级从高到低）
		APIs: []APIConfig{
			{
				Provider: APIProviderIPAPICo,
				BaseURL:  "https://ipapi.co",
				Enabled:  true,
			},
			{
				Provider: APIProviderIPAPIIs,
				BaseURL:  "https://api.ipapi.is",
				Enabled:  true,
			},
		},

		BatchSize:              100,
		ConcurrentLimit:        10,
		RequestTimeout:         30 * time.Second,
		ProxyConfigPath:        "proxy/proxy.txt",
		EnableProxy:            false,
		EnableCIDROptimization: true,
		MaxCIDRSize:            24, // 最大/24网段
		DBBatchSize:            1000,
		DBTimeout:              60 * time.Second,
		RequestsPerSecond:      2,
		MaxRetries:             3,
		InitialDelay:           1 * time.Second,
		MaxDelay:               30 * time.Second,
		BackoffFactor:          2.0,
	}

	// 构建内部重试配置对象
	config.RetryConfig = DefaultRetryConfig()
	config.RetryConfig.MaxRetries = config.MaxRetries
	config.RetryConfig.InitialDelay = config.InitialDelay
	config.RetryConfig.MaxDelay = config.MaxDelay
	config.RetryConfig.BackoffFactor = config.BackoffFactor

	return config
}

// CompletionService IP地理信息补全服务
type CompletionService struct {
	config       CompletionConfig
	db           database.DatabaseInterface
	proxyManager *ProxyManager
	httpClient   *HTTPClientWithRetry
	logger       *zap.Logger

	// 统计信息
	stats struct {
		sync.RWMutex
		TotalProcessed     int64     `json:"total_processed"`
		TotalCompleted     int64     `json:"total_completed"`
		TotalFailed        int64     `json:"total_failed"`
		TotalSkipped       int64     `json:"total_skipped"`
		StartTime          time.Time `json:"start_time"`
		LastUpdateTime     time.Time `json:"last_update_time"`
		CurrentBatch       int       `json:"current_batch"`
		EstimatedRemaining int64     `json:"estimated_remaining"`
	}

	// CIDR缓存
	cidrCache map[string]*model.IPInfo
	cidrMutex sync.RWMutex
}

// NewCompletionService 创建补全服务
func NewCompletionService(config CompletionConfig, db database.DatabaseInterface, logger *zap.Logger) (*CompletionService, error) {
	cs := &CompletionService{
		config:    config,
		db:        db,
		logger:    logger,
		cidrCache: make(map[string]*model.IPInfo),
	}

	// 初始化代理管理器
	if config.EnableProxy && config.ProxyConfigPath != "" {
		pm := NewProxyManager(config.ProxyConfigPath, logger)
		if err := pm.LoadProxies(); err != nil {
			logger.Warn("Failed to load proxies, using direct connection", zap.Error(err))
		} else {
			cs.proxyManager = pm
		}
	}

	// 初始化HTTP客户端
	cs.httpClient = NewHTTPClientWithRetry(cs.proxyManager, logger)
	cs.httpClient.SetRetryConfig(config.RetryConfig)
	cs.httpClient.SetTimeout(config.RequestTimeout)

	return cs, nil
}

// StartCompletion 开始IP地理信息补全
func (cs *CompletionService) StartCompletion(ctx context.Context) error {
	startTime := time.Now()
	sessionID := fmt.Sprintf("completion_%d", startTime.Unix())

	cs.stats.Lock()
	cs.stats.StartTime = startTime
	cs.stats.TotalProcessed = 0
	cs.stats.TotalCompleted = 0
	cs.stats.TotalFailed = 0
	cs.stats.TotalSkipped = 0
	cs.stats.CurrentBatch = 0
	cs.stats.LastUpdateTime = startTime
	cs.stats.Unlock()

	cs.logger.Info("Starting IP geolocation completion",
		zap.String("session_id", sessionID),
		zap.Time("start_time", startTime),
		zap.String("api_base_url", cs.config.APIBaseURL),
		zap.Int("batch_size", cs.config.BatchSize),
		zap.Int("concurrent_limit", cs.config.ConcurrentLimit),
		zap.Bool("enable_proxy", cs.config.EnableProxy),
		zap.Bool("enable_cidr_optimization", cs.config.EnableCIDROptimization),
		zap.Duration("request_timeout", cs.config.RequestTimeout),
		zap.Int("max_retries", cs.config.RetryConfig.MaxRetries))

	// 获取数据库完整性统计信息
	cs.logger.Info("Analyzing database completion status")
	statsStart := time.Now()
	dbStats, err := cs.getIncompleteIPsStats(ctx)
	if err != nil {
		cs.logger.Warn("Failed to get database statistics, continuing with completion",
			zap.Error(err))
	} else {
		cs.logger.Info("Database completion analysis completed",
			zap.Duration("analysis_duration", time.Since(statsStart)),
			zap.Any("completion_stats", dbStats))
	}

	// 获取需要补全的IP记录
	cs.logger.Info("Fetching incomplete IP records from database")
	fetchStart := time.Now()
	incompleteIPs, err := cs.getIncompleteIPs(ctx)
	fetchDuration := time.Since(fetchStart)

	if err != nil {
		cs.logger.Error("Failed to get incomplete IPs",
			zap.Error(err),
			zap.String("session_id", sessionID),
			zap.Duration("elapsed", time.Since(startTime)))
		return fmt.Errorf("failed to get incomplete IPs: %w", err)
	}

	if len(incompleteIPs) == 0 {
		cs.logger.Info("No incomplete IP records found - completion not needed",
			zap.String("session_id", sessionID),
			zap.Duration("elapsed", time.Since(startTime)),
			zap.Any("db_stats", dbStats))
		return nil
	}

	cs.logger.Info("Found incomplete IP records",
		zap.String("session_id", sessionID),
		zap.Int("total_count", len(incompleteIPs)),
		zap.Duration("fetch_duration", fetchDuration),
		zap.Int("estimated_batches", (len(incompleteIPs)+cs.config.BatchSize-1)/cs.config.BatchSize))

	cs.stats.Lock()
	cs.stats.EstimatedRemaining = int64(len(incompleteIPs))
	cs.stats.Unlock()

	// 分批处理
	processingStart := time.Now()
	cs.logger.Info("Starting batch processing",
		zap.String("session_id", sessionID),
		zap.Int("total_ips", len(incompleteIPs)))

	err = cs.processBatches(ctx, incompleteIPs)

	// 记录完成统计
	totalDuration := time.Since(startTime)
	processingDuration := time.Since(processingStart)

	cs.stats.RLock()
	finalStats := map[string]interface{}{
		"session_id":          sessionID,
		"total_processed":     cs.stats.TotalProcessed,
		"total_completed":     cs.stats.TotalCompleted,
		"total_failed":        cs.stats.TotalFailed,
		"total_skipped":       cs.stats.TotalSkipped,
		"total_duration":      totalDuration,
		"processing_duration": processingDuration,
		"fetch_duration":      fetchDuration,
	}

	if cs.stats.TotalProcessed > 0 {
		finalStats["average_per_ip"] = float64(processingDuration.Nanoseconds()) / float64(cs.stats.TotalProcessed) / 1e6 // ms per IP
		finalStats["success_rate"] = float64(cs.stats.TotalCompleted) / float64(cs.stats.TotalProcessed) * 100
	}
	cs.stats.RUnlock()

	if err != nil {
		cs.logger.Error("IP geolocation completion failed",
			zap.Error(err),
			zap.Any("final_stats", finalStats))
		return err
	}

	cs.logger.Info("IP geolocation completion completed successfully",
		zap.Any("final_stats", finalStats))

	return nil
}

// getIncompleteIPs 获取需要补全的IP记录
func (cs *CompletionService) getIncompleteIPs(ctx context.Context) ([]string, error) {
	cs.logger.Info("Querying database for incomplete IP records")

	// 通过类型断言获取OptimizedDatabaseAdapter以访问连接池
	adapter, ok := cs.db.(*database.OptimizedDatabaseAdapter)
	if !ok {
		cs.logger.Error("Database interface is not OptimizedDatabaseAdapter, cannot access connection pool")
		return nil, fmt.Errorf("unsupported database type for batch queries")
	}

	pool := adapter.GetPool()
	if pool == nil {
		cs.logger.Error("Database connection pool is nil")
		return nil, fmt.Errorf("database connection pool not available")
	}

	// 查询缺少地理信息的IP记录
	// 优先查找关键地理信息字段为空的记录
	query := `
WITH incomplete_records AS (
    SELECT
        ip_range,
        id,
        -- 计算不完整程度评分（越高越需要补全）
        (CASE
            WHEN country_name IS NULL OR country_name = '' THEN 10
            ELSE 0
        END +
        CASE
            WHEN country_code IS NULL OR country_code = '' THEN 8
            ELSE 0
        END +
        CASE
            WHEN city IS NULL OR city = '' THEN 6
            ELSE 0
        END +
        CASE
            WHEN isp IS NULL OR isp = '' THEN 4
            ELSE 0
        END +
        CASE
            WHEN asn IS NULL OR asn = '' THEN 2
            ELSE 0
        END) AS incomplete_score
    FROM ip_ranges_new
    WHERE ip_range IS NOT NULL
        AND ip_version = 4  -- IPv4记录
        AND (
            country_name IS NULL OR country_name = '' OR
            country_code IS NULL OR country_code = '' OR
            city IS NULL OR city = '' OR
            isp IS NULL OR isp = '' OR
            asn IS NULL OR asn = ''
        )
)
SELECT DISTINCT
    host(network(ip_range::cidr)) AS ip_address,
    incomplete_score,
    id
FROM incomplete_records
WHERE incomplete_score > 0
ORDER BY incomplete_score DESC, id
LIMIT $1;
	`

	// 限制查询数量，避免一次性处理过多数据
	maxRecords := 1000
	if cs.config.BatchSize > 0 {
		maxRecords = cs.config.BatchSize * 10 // 获取10个批次的数据量
	}

	cs.logger.Debug("Executing incomplete IPs query",
		zap.String("query", query),
		zap.Int("max_records", maxRecords))

	rows, err := pool.Query(ctx, query, maxRecords)
	if err != nil {
		cs.logger.Error("Failed to query incomplete IPs", zap.Error(err))
		return nil, fmt.Errorf("failed to query incomplete IPs: %w", err)
	}
	defer rows.Close()

	var incompleteIPs []string
	var scannedCount, validCount, invalidCount int

	for rows.Next() {
		var ipAddress *string
		var incompleteScore int
		var recordID int64

		if err := rows.Scan(&ipAddress, &incompleteScore, &recordID); err != nil {
			cs.logger.Warn("Failed to scan IP record", zap.Error(err))
			continue
		}

		scannedCount++

		// 跳过空值并验证IP地址格式
		if ipAddress != nil && *ipAddress != "" {
			// 验证IP地址格式
			if cs.isValidIPAddress(*ipAddress) {
				incompleteIPs = append(incompleteIPs, *ipAddress)
				validCount++

				// 记录详细的扫描信息（仅在调试模式下）
				cs.logger.Debug("Found incomplete IP record",
					zap.String("ip", *ipAddress),
					zap.Int("incomplete_score", incompleteScore),
					zap.Int64("record_id", recordID))
			} else {
				cs.logger.Debug("Invalid IP address format",
					zap.String("ip", *ipAddress),
					zap.Int("incomplete_score", incompleteScore),
					zap.Int64("record_id", recordID))
				invalidCount++
			}
		}
	}

	if err := rows.Err(); err != nil {
		cs.logger.Error("Error iterating over query results", zap.Error(err))
		return nil, fmt.Errorf("error reading query results: %w", err)
	}

	cs.logger.Info("Found incomplete IP records",
		zap.Int("count", len(incompleteIPs)),
		zap.Int("scanned", scannedCount),
		zap.Int("valid", validCount),
		zap.Int("invalid", invalidCount),
		zap.Int("max_requested", maxRecords))

	// 如果没有找到不完整的记录，记录详细信息
	if len(incompleteIPs) == 0 {
		// 查询总记录数以提供更多上下文
		var totalRecords int64
		err := pool.QueryRow(ctx, "SELECT COUNT(*) FROM ip_ranges_new").Scan(&totalRecords)
		if err != nil {
			cs.logger.Warn("Failed to get total record count", zap.Error(err))
		} else {
			cs.logger.Info("No incomplete records found",
				zap.Int64("total_records_in_db", totalRecords))
		}
	}

	return incompleteIPs, nil
}

// isValidIPAddress 验证IP地址格式是否有效
func (cs *CompletionService) isValidIPAddress(ip string) bool {
	// 使用net包验证IP地址格式
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 确保是IPv4地址
	if parsedIP.To4() == nil {
		return false
	}

	// 排除一些特殊的IP地址范围
	// 排除私有地址、回环地址、多播地址等
	if parsedIP.IsLoopback() || parsedIP.IsMulticast() || parsedIP.IsPrivate() {
		cs.logger.Debug("Skipping special IP address",
			zap.String("ip", ip),
			zap.Bool("is_loopback", parsedIP.IsLoopback()),
			zap.Bool("is_multicast", parsedIP.IsMulticast()),
			zap.Bool("is_private", parsedIP.IsPrivate()))
		return false
	}

	return true
}

// getIncompleteIPsStats 获取不完整IP记录的统计信息
func (cs *CompletionService) getIncompleteIPsStats(ctx context.Context) (map[string]interface{}, error) {
	// 通过类型断言获取OptimizedDatabaseAdapter以访问连接池
	adapter, ok := cs.db.(*database.OptimizedDatabaseAdapter)
	if !ok {
		return nil, fmt.Errorf("unsupported database type for stats queries")
	}

	pool := adapter.GetPool()
	if pool == nil {
		return nil, fmt.Errorf("database connection pool not available")
	}

	stats := make(map[string]interface{})

	// 查询各种不完整情况的统计
	statsQuery := `
		SELECT
			COUNT(*) as total_records,
			COUNT(CASE WHEN country_name IS NULL OR country_name = '' THEN 1 END) as missing_country_name,
			COUNT(CASE WHEN country_code IS NULL OR country_code = '' THEN 1 END) as missing_country_code,
			COUNT(CASE WHEN city IS NULL OR city = '' THEN 1 END) as missing_city,
			COUNT(CASE WHEN isp IS NULL OR isp = '' THEN 1 END) as missing_isp,
			COUNT(CASE WHEN asn IS NULL OR asn = '' THEN 1 END) as missing_asn,
			COUNT(CASE WHEN
				(country_name IS NULL OR country_name = '') OR
				(country_code IS NULL OR country_code = '') OR
				(city IS NULL OR city = '') OR
				(isp IS NULL OR isp = '') OR
				(asn IS NULL OR asn = '')
			THEN 1 END) as incomplete_records
		FROM ip_ranges_new
		WHERE ip_version = 4
	`

	var totalRecords, missingCountryName, missingCountryCode, missingCity, missingISP, missingASN, incompleteRecords int64

	err := pool.QueryRow(ctx, statsQuery).Scan(
		&totalRecords,
		&missingCountryName,
		&missingCountryCode,
		&missingCity,
		&missingISP,
		&missingASN,
		&incompleteRecords,
	)

	if err != nil {
		cs.logger.Error("Failed to get incomplete IPs statistics", zap.Error(err))
		return nil, fmt.Errorf("failed to get statistics: %w", err)
	}

	stats["total_records"] = totalRecords
	stats["missing_country_name"] = missingCountryName
	stats["missing_country_code"] = missingCountryCode
	stats["missing_city"] = missingCity
	stats["missing_isp"] = missingISP
	stats["missing_asn"] = missingASN
	stats["incomplete_records"] = incompleteRecords

	// 计算完整性百分比
	if totalRecords > 0 {
		completionRate := float64(totalRecords-incompleteRecords) / float64(totalRecords) * 100
		stats["completion_rate"] = completionRate
		stats["incomplete_rate"] = 100 - completionRate
	}

	cs.logger.Debug("Database completion statistics",
		zap.Int64("total_records", totalRecords),
		zap.Int64("incomplete_records", incompleteRecords),
		zap.Any("stats", stats))

	return stats, nil
}

// processBatches 分批处理IP
func (cs *CompletionService) processBatches(ctx context.Context, ips []string) error {
	// 如果启用CIDR优化，先进行CIDR分组
	if cs.config.EnableCIDROptimization {
		ips = cs.optimizeWithCIDR(ips)
	}

	// 创建工作池
	semaphore := make(chan struct{}, cs.config.ConcurrentLimit)
	var wg sync.WaitGroup

	// 分批处理
	for i := 0; i < len(ips); i += cs.config.BatchSize {
		end := i + cs.config.BatchSize
		if end > len(ips) {
			end = len(ips)
		}

		batch := ips[i:end]

		wg.Add(1)
		go func(batchNum int, ipBatch []string) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			cs.stats.Lock()
			cs.stats.CurrentBatch = batchNum + 1
			cs.stats.Unlock()

			if err := cs.processBatch(ctx, batchNum, ipBatch); err != nil {
				cs.logger.Error("Failed to process batch",
					zap.Int("batch_num", batchNum+1),
					zap.Error(err))
			}
		}(i/cs.config.BatchSize, batch)

		// 检查上下文取消
		select {
		case <-ctx.Done():
			cs.logger.Info("Context cancelled, waiting for current batches to complete")
			wg.Wait()
			return ctx.Err()
		default:
		}
	}

	wg.Wait()

	cs.stats.RLock()
	finalStats := map[string]interface{}{
		"total_processed": cs.stats.TotalProcessed,
		"total_completed": cs.stats.TotalCompleted,
		"total_failed":    cs.stats.TotalFailed,
		"total_skipped":   cs.stats.TotalSkipped,
		"current_batch":   cs.stats.CurrentBatch,
	}

	if cs.stats.TotalProcessed > 0 {
		successRate := float64(cs.stats.TotalCompleted) / float64(cs.stats.TotalProcessed) * 100
		finalStats["success_rate"] = successRate
	}
	cs.stats.RUnlock()

	cs.logger.Info("Batch processing completed",
		zap.Any("final_stats", finalStats))

	return nil
}

// processBatch 处理单个批次
func (cs *CompletionService) processBatch(ctx context.Context, batchNum int, ips []string) error {
	batchStart := time.Now()
	batchID := fmt.Sprintf("batch_%d", batchNum+1)

	cs.logger.Info("Starting batch processing",
		zap.String("batch_id", batchID),
		zap.Int("batch_num", batchNum+1),
		zap.Int("batch_size", len(ips)))

	var completedIPs []model.IPInfo
	var batchStats struct {
		processed int
		completed int
		failed    int
		skipped   int
	}

	for i, ip := range ips {
		select {
		case <-ctx.Done():
			cs.logger.Warn("Batch processing cancelled",
				zap.String("batch_id", batchID),
				zap.Int("processed_in_batch", batchStats.processed),
				zap.Int("remaining_in_batch", len(ips)-i))
			return ctx.Err()
		default:
		}

		ipStart := time.Now()
		batchStats.processed++

		// 检查CIDR缓存
		if cs.config.EnableCIDROptimization {
			if cachedInfo := cs.getCachedCIDRInfo(ip); cachedInfo != nil {
				// 使用缓存的信息，但更新IP地址
				ipInfo := *cachedInfo
				ipInfo.IPRange.CIDR = ip + "/32"
				ipInfo.IPRange.StartIP = ip
				ipInfo.IPRange.EndIP = ip
				completedIPs = append(completedIPs, ipInfo)

				batchStats.skipped++
				cs.stats.Lock()
				cs.stats.TotalSkipped++
				cs.stats.TotalProcessed++
				cs.stats.Unlock()

				cs.logger.Debug("IP processed from CIDR cache",
					zap.String("batch_id", batchID),
					zap.String("ip", ip),
					zap.Duration("duration", time.Since(ipStart)))
				continue
			}
		}

		// 调用API获取地理信息
		ipInfo, err := cs.queryIPGeolocation(ctx, ip)
		if err != nil {
			batchStats.failed++
			cs.logger.Warn("Failed to query IP geolocation",
				zap.String("batch_id", batchID),
				zap.String("ip", ip),
				zap.Duration("duration", time.Since(ipStart)),
				zap.Error(err))

			cs.stats.Lock()
			cs.stats.TotalFailed++
			cs.stats.TotalProcessed++
			cs.stats.Unlock()
			continue
		}

		completedIPs = append(completedIPs, *ipInfo)
		batchStats.completed++

		cs.logger.Debug("IP geolocation completed successfully",
			zap.String("batch_id", batchID),
			zap.String("ip", ip),
			zap.String("country", ipInfo.Geolocation.Country.Name),
			zap.String("city", ipInfo.Geolocation.City),
			zap.Duration("duration", time.Since(ipStart)))

		// 如果启用CIDR优化，缓存结果
		if cs.config.EnableCIDROptimization {
			cs.cacheCIDRInfo(ip, ipInfo)
		}

		cs.stats.Lock()
		cs.stats.TotalCompleted++
		cs.stats.Unlock()

		// 添加小延迟避免过于频繁的请求
		time.Sleep(100 * time.Millisecond)
	}

	cs.stats.Lock()
	cs.stats.TotalCompleted += int64(batchStats.completed)
	cs.stats.LastUpdateTime = time.Now()
	cs.stats.Unlock()

	// 批量更新数据库
	dbStart := time.Now()
	if len(completedIPs) > 0 {
		cs.logger.Debug("Updating database with batch results",
			zap.String("batch_id", batchID),
			zap.Int("records_to_update", len(completedIPs)))

		if err := cs.updateDatabase(ctx, completedIPs); err != nil {
			cs.logger.Error("Failed to update database for batch",
				zap.String("batch_id", batchID),
				zap.Error(err))
			return fmt.Errorf("failed to update database: %w", err)
		}

		cs.logger.Debug("Database update completed",
			zap.String("batch_id", batchID),
			zap.Duration("db_duration", time.Since(dbStart)))
	}

	batchDuration := time.Since(batchStart)
	cs.logger.Info("Batch processing completed",
		zap.String("batch_id", batchID),
		zap.Int("batch_num", batchNum+1),
		zap.Duration("batch_duration", batchDuration),
		zap.Int("processed", batchStats.processed),
		zap.Int("completed", batchStats.completed),
		zap.Int("failed", batchStats.failed),
		zap.Int("skipped", batchStats.skipped),
		zap.Int("db_records", len(completedIPs)),
		zap.Float64("success_rate", float64(batchStats.completed)/float64(batchStats.processed)*100))

	return nil
}

// queryIPGeolocation 查询IP地理信息（多API支持）
func (cs *CompletionService) queryIPGeolocation(ctx context.Context, ip string) (*model.IPInfo, error) {
	// 获取启用的API列表
	apis := cs.getEnabledAPIs()
	if len(apis) == 0 {
		return nil, fmt.Errorf("no enabled APIs configured")
	}

	var lastErr error

	// 按优先级顺序尝试每个API
	for i, apiConfig := range apis {
		cs.logger.Debug("Trying API",
			zap.String("provider", string(apiConfig.Provider)),
			zap.String("ip", ip),
			zap.Int("attempt", i+1),
			zap.Int("total_apis", len(apis)))

		ipInfo, err := cs.queryWithAPI(ctx, ip, apiConfig)
		if err != nil {
			lastErr = err
			cs.logger.Warn("API request failed, trying next",
				zap.String("provider", string(apiConfig.Provider)),
				zap.String("ip", ip),
				zap.Error(err))
			continue
		}

		// 成功获取数据，记录使用的API并返回
		cs.logger.Debug("API request successful",
			zap.String("provider", string(apiConfig.Provider)),
			zap.String("ip", ip),
			zap.String("country", ipInfo.Geolocation.Country.Name),
			zap.String("city", ipInfo.Geolocation.City))

		return ipInfo, nil
	}

	// 所有API都失败
	return nil, fmt.Errorf("all APIs failed, last error: %w", lastErr)
}

// convertToIPInfo 将API响应转换为内部模型
func (cs *CompletionService) convertToIPInfo(resp IPAPIResponse) *model.IPInfo {
	ipInfo := &model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      resp.IP + "/32",
			StartIP:   resp.IP,
			EndIP:     resp.IP,
			IPVersion: "IPv4",
		},
		Geolocation: model.Geolocation{
			Continent: model.Continent{
				Code: resp.ContinentCode,
			},
			Country: model.Country{
				Code: resp.CountryCode,
				Name: resp.CountryName,
			},
			Region: model.Region{
				Code: resp.RegionCode,
				Name: resp.Region,
			},
			City:       resp.City,
			PostalCode: resp.Postal,
			Latitude:   &resp.Latitude,
			Longitude:  &resp.Longitude,
		},
		Network: model.Network{
			ASN:          resp.ASN,
			Organization: resp.Org,
		},
		Timezone: model.Timezone{
			Name:   resp.Timezone,
			Offset: resp.UTCOffset,
		},
		Extended: model.Extended{
			Currency: model.Currency{
				Code: resp.Currency,
				Name: resp.CurrencyName,
			},
			Languages:   strings.Split(resp.Languages, ","),
			CallingCode: resp.CountryCallingCode,
		},
		Metadata: model.Metadata{
			Source:      "ipapi.co",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(85), // ipapi.co的置信度设为85%
		},
	}

	return ipInfo
}

// convertIPAPIIsToIPInfo 将 api.ipapi.is API响应转换为内部模型
func (cs *CompletionService) convertIPAPIIsToIPInfo(resp IPAPIIsResponse) *model.IPInfo {
	ipInfo := &model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      resp.IP + "/32",
			StartIP:   resp.IP,
			EndIP:     resp.IP,
			IPVersion: "IPv4",
		},
		Geolocation: model.Geolocation{
			Country: model.Country{
				Code: resp.Location.CountryCode,
				Name: resp.Location.Country,
			},
			Region: model.Region{
				Name: resp.Location.State,
			},
			City:       resp.Location.City,
			PostalCode: resp.Location.ZipCode,
			Latitude:   &resp.Location.Latitude,
			Longitude:  &resp.Location.Longitude,
		},
		Network: model.Network{
			ASN:          fmt.Sprintf("AS%d", resp.ASN.Number),
			Organization: resp.Company.Name,
		},
		Timezone: model.Timezone{
			Name: resp.Location.Timezone,
		},
		Metadata: model.Metadata{
			Source:      "ipapi.is",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(80), // api.ipapi.is 通常有较高的准确性
		},
	}

	return ipInfo
}

// getEnabledAPIs 获取启用的API列表
func (cs *CompletionService) getEnabledAPIs() []APIConfig {
	var enabledAPIs []APIConfig

	// 如果配置了多API，使用多API配置
	if len(cs.config.APIs) > 0 {
		for _, api := range cs.config.APIs {
			if api.Enabled {
				enabledAPIs = append(enabledAPIs, api)
			}
		}
	} else {
		// 向后兼容：使用单API配置
		if cs.config.APIBaseURL != "" {
			enabledAPIs = append(enabledAPIs, APIConfig{
				Provider: APIProviderIPAPICo,
				BaseURL:  cs.config.APIBaseURL,
				Enabled:  true,
			})
		}
	}

	return enabledAPIs
}

// queryWithAPI 使用指定API查询IP地理信息
func (cs *CompletionService) queryWithAPI(ctx context.Context, ip string, apiConfig APIConfig) (*model.IPInfo, error) {
	switch apiConfig.Provider {
	case APIProviderIPAPICo:
		return cs.queryIPAPICo(ctx, ip, apiConfig.BaseURL)
	case APIProviderIPAPIIs:
		return cs.queryIPAPIIs(ctx, ip, apiConfig.BaseURL)
	default:
		return nil, fmt.Errorf("unsupported API provider: %s", apiConfig.Provider)
	}
}
func (cs *CompletionService) commonGet(ctx context.Context, url string) ([]byte, error) {

	resp, err := cs.httpClient.Get(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	return io.ReadAll(resp.Body)
}

// queryIPAPICo 使用 ipapi.co API查询IP地理信息
func (cs *CompletionService) queryIPAPICo(ctx context.Context, ip string, baseURL string) (*model.IPInfo, error) {
	url := fmt.Sprintf("%s/%s/json", baseURL, ip)

	body, err := cs.commonGet(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var apiResp IPAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 转换为内部模型
	return cs.convertToIPInfo(apiResp), nil
}

// queryIPAPIIs 使用 api.ipapi.is API查询IP地理信息
func (cs *CompletionService) queryIPAPIIs(ctx context.Context, ip string, baseURL string) (*model.IPInfo, error) {
	url := fmt.Sprintf("%s/?q=%s", baseURL, ip)

	body, err := cs.commonGet(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var apiResp IPAPIIsResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 转换为内部模型
	return cs.convertIPAPIIsToIPInfo(apiResp), nil
}

// optimizeWithCIDR CIDR优化：对相邻IP进行分组
func (cs *CompletionService) optimizeWithCIDR(ips []string) []string {
	if !cs.config.EnableCIDROptimization {
		return ips
	}

	// 简单的CIDR优化：移除同一/24网段中的重复查询
	cidrMap := make(map[string]bool)
	var optimizedIPs []string

	for _, ip := range ips {
		// 计算/24网段
		if parsedIP := net.ParseIP(ip); parsedIP != nil {
			ipv4 := parsedIP.To4()
			if ipv4 != nil {
				cidr := fmt.Sprintf("%d.%d.%d.0/24", ipv4[0], ipv4[1], ipv4[2])
				if !cidrMap[cidr] {
					cidrMap[cidr] = true
					optimizedIPs = append(optimizedIPs, ip)
				}
			}
		} else {
			// 如果IP解析失败，仍然保留
			optimizedIPs = append(optimizedIPs, ip)
		}
	}

	cs.logger.Info("CIDR optimization completed",
		zap.Int("original_count", len(ips)),
		zap.Int("optimized_count", len(optimizedIPs)),
		zap.Int("saved_requests", len(ips)-len(optimizedIPs)))

	return optimizedIPs
}

// getCachedCIDRInfo 获取CIDR缓存信息
func (cs *CompletionService) getCachedCIDRInfo(ip string) *model.IPInfo {
	if !cs.config.EnableCIDROptimization {
		return nil
	}

	cs.cidrMutex.RLock()
	defer cs.cidrMutex.RUnlock()

	// 检查同一/24网段是否有缓存
	if parsedIP := net.ParseIP(ip); parsedIP != nil {
		ipv4 := parsedIP.To4()
		if ipv4 != nil {
			cidr := fmt.Sprintf("%d.%d.%d.0/24", ipv4[0], ipv4[1], ipv4[2])
			return cs.cidrCache[cidr]
		}
	}

	return nil
}

// cacheCIDRInfo 缓存CIDR信息
func (cs *CompletionService) cacheCIDRInfo(ip string, ipInfo *model.IPInfo) {
	if !cs.config.EnableCIDROptimization {
		return
	}

	cs.cidrMutex.Lock()
	defer cs.cidrMutex.Unlock()

	// 缓存到/24网段
	if parsedIP := net.ParseIP(ip); parsedIP != nil {
		ipv4 := parsedIP.To4()
		if ipv4 != nil {
			cidr := fmt.Sprintf("%d.%d.%d.0/24", ipv4[0], ipv4[1], ipv4[2])
			cs.cidrCache[cidr] = ipInfo
		}
	}
}

// updateDatabase 批量更新数据库
func (cs *CompletionService) updateDatabase(ctx context.Context, ipInfos []model.IPInfo) error {
	if len(ipInfos) == 0 {
		return nil
	}

	cs.logger.Debug("Updating database",
		zap.Int("record_count", len(ipInfos)))

	// 使用数据库接口的批量更新方法
	_, err := cs.db.BatchUpsertIPs(ctx, ipInfos)
	if err != nil {
		return fmt.Errorf("batch upsert failed: %w", err)
	}

	return nil
}

// GetStats 获取统计信息
func (cs *CompletionService) GetStats() map[string]interface{} {
	cs.stats.RLock()
	defer cs.stats.RUnlock()

	stats := map[string]interface{}{
		"total_processed":     cs.stats.TotalProcessed,
		"total_completed":     cs.stats.TotalCompleted,
		"total_failed":        cs.stats.TotalFailed,
		"total_skipped":       cs.stats.TotalSkipped,
		"start_time":          cs.stats.StartTime,
		"last_update_time":    cs.stats.LastUpdateTime,
		"current_batch":       cs.stats.CurrentBatch,
		"estimated_remaining": cs.stats.EstimatedRemaining,
	}

	if !cs.stats.StartTime.IsZero() {
		duration := time.Since(cs.stats.StartTime)
		stats["duration"] = duration

		if cs.stats.TotalProcessed > 0 {
			rate := float64(cs.stats.TotalProcessed) / duration.Seconds()
			stats["processing_rate"] = rate

			if cs.stats.EstimatedRemaining > 0 {
				eta := time.Duration(float64(cs.stats.EstimatedRemaining)/rate) * time.Second
				stats["estimated_time_remaining"] = eta
			}
		}
	}

	// 添加HTTP客户端统计
	if cs.httpClient != nil {
		stats["http_client"] = cs.httpClient.GetStats()
	}

	return stats
}

// ptrInt 辅助函数：创建int指针
func ptrInt(i int) *int {
	return &i
}
