package ipcompletion

import (
	"bufio"
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/proxy"
)

// ProxyInfo 代理信息
type ProxyInfo struct {
	URL        string    `json:"url"`
	Type       string    `json:"type"` // http, https, socks4, socks5
	Host       string    `json:"host"`
	Port       string    `json:"port"`
	IsHealthy  bool      `json:"is_healthy"`
	LastCheck  time.Time `json:"last_check"`
	ErrorCount int       `json:"error_count"`
	LastError  string    `json:"last_error,omitempty"`
}

// ProxyManager 代理管理器
type ProxyManager struct {
	proxies       []*ProxyInfo
	currentIndex  int
	mutex         sync.RWMutex
	logger        *zap.Logger
	healthChecker *http.Client

	// 配置参数
	proxyFile          string
	healthCheckURL     string
	healthCheckTimeout time.Duration
	maxErrorCount      int
	checkInterval      time.Duration

	// 状态
	lastReload     time.Time
	totalProxies   int
	healthyProxies int

	// 黑名单管理
	blacklist    map[string]time.Time // 代理URL -> 黑名单到期时间
	blacklistTTL time.Duration        // 黑名单持续时间（10分钟）
}

// NewProxyManager 创建代理管理器
func NewProxyManager(proxyFile string, logger *zap.Logger) *ProxyManager {
	pm := &ProxyManager{
		proxies:            make([]*ProxyInfo, 0),
		currentIndex:       0,
		logger:             logger,
		proxyFile:          proxyFile,
		healthCheckURL:     "https://httpbin.org/ip", // 用于健康检查的URL
		healthCheckTimeout: 10 * time.Second,
		maxErrorCount:      3,
		checkInterval:      5 * time.Minute,
		blacklist:          make(map[string]time.Time),
		blacklistTTL:       10 * time.Minute, // 10分钟黑名单
		healthChecker: &http.Client{
			Timeout: 10 * time.Second,
		},
	}

	return pm
}

// AddToBlacklist 将代理添加到黑名单
func (pm *ProxyManager) AddToBlacklist(proxyURL string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	expireTime := time.Now().Add(pm.blacklistTTL)
	pm.blacklist[proxyURL] = expireTime

	pm.logger.Warn("Proxy added to blacklist",
		zap.String("proxy", proxyURL),
		zap.Time("expire_time", expireTime),
		zap.Duration("ttl", pm.blacklistTTL))
}

// IsBlacklisted 检查代理是否在黑名单中（外部调用，带锁）
func (pm *ProxyManager) IsBlacklisted(proxyURL string) bool {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return pm.isBlacklistedUnsafe(proxyURL)
}

// isBlacklistedUnsafe 检查代理是否在黑名单中（内部调用，不加锁）
func (pm *ProxyManager) isBlacklistedUnsafe(proxyURL string) bool {
	expireTime, exists := pm.blacklist[proxyURL]
	if !exists {
		return false
	}

	// 检查是否过期
	if time.Now().After(expireTime) {
		// 直接删除过期条目（已经在锁内）
		delete(pm.blacklist, proxyURL)
		pm.logger.Debug("Removed expired proxy from blacklist", zap.String("proxy", proxyURL))
		return false
	}

	return true
}

// CleanExpiredBlacklist 清理过期的黑名单条目（外部调用，带锁）
func (pm *ProxyManager) CleanExpiredBlacklist() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.cleanExpiredBlacklistUnsafe()
}

// cleanExpiredBlacklistUnsafe 清理过期的黑名单条目（内部调用，不加锁）
func (pm *ProxyManager) cleanExpiredBlacklistUnsafe() {
	now := time.Now()
	cleaned := 0

	for proxyURL, expireTime := range pm.blacklist {
		if now.After(expireTime) {
			delete(pm.blacklist, proxyURL)
			cleaned++
		}
	}

	if cleaned > 0 {
		pm.logger.Debug("Cleaned expired blacklist entries", zap.Int("count", cleaned))
	}
}

// GetAvailableProxy 获取可用代理（排除黑名单）
func (pm *ProxyManager) GetAvailableProxy() (*ProxyInfo, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if len(pm.proxies) == 0 {
		return nil, fmt.Errorf("no proxies configured")
	}

	// 清理过期的黑名单（使用不加锁的内部方法）
	pm.cleanExpiredBlacklistUnsafe()

	// 寻找可用的代理
	startIndex := pm.currentIndex
	for i := 0; i < len(pm.proxies); i++ {
		index := (startIndex + i) % len(pm.proxies)
		proxy := pm.proxies[index]

		// 检查是否在黑名单中（使用不加锁的内部方法）
		if !pm.isBlacklistedUnsafe(proxy.URL) && proxy.IsHealthy {
			pm.currentIndex = (index + 1) % len(pm.proxies)
			return proxy, nil
		}
	}

	// 如果所有代理都在黑名单中，返回错误
	return nil, fmt.Errorf("all proxies are blacklisted or unhealthy")
}

// LoadProxies 从文件加载代理配置
func (pm *ProxyManager) LoadProxies() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	file, err := os.Open(pm.proxyFile)
	if err != nil {
		return fmt.Errorf("failed to open proxy file %s: %w", pm.proxyFile, err)
	}
	defer file.Close()

	var newProxies []*ProxyInfo
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		proxyInfo, err := pm.parseProxyURL(line)
		if err != nil {
			pm.logger.Warn("Invalid proxy configuration",
				zap.String("line", line),
				zap.Int("line_number", lineNum),
				zap.Error(err))
			continue
		}

		newProxies = append(newProxies, proxyInfo)
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading proxy file: %w", err)
	}

	pm.proxies = newProxies
	pm.totalProxies = len(newProxies)
	pm.lastReload = time.Now()

	pm.logger.Info("Loaded proxy configurations",
		zap.Int("total_proxies", pm.totalProxies),
		zap.String("proxy_file", pm.proxyFile))

	// 启动健康检查
	// go pm.startHealthCheck()

	return nil
}

// parseProxyURL 解析代理URL
func (pm *ProxyManager) parseProxyURL(proxyURL string) (*ProxyInfo, error) {
	u, err := url.Parse(proxyURL)
	if err != nil {
		return nil, fmt.Errorf("invalid URL format: %w", err)
	}

	proxyType := strings.ToLower(u.Scheme)
	if proxyType == "" {
		return nil, fmt.Errorf("missing proxy type in URL")
	}

	// 验证支持的代理类型
	switch proxyType {
	case "http", "https", "socks4", "socks5":
		// 支持的类型
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxyType)
	}

	host := u.Hostname()
	port := u.Port()
	if host == "" {
		return nil, fmt.Errorf("missing host in proxy URL")
	}
	if port == "" {
		// 设置默认端口
		switch proxyType {
		case "http", "https":
			port = "8080"
		case "socks4", "socks5":
			port = "1080"
		}
	}

	return &ProxyInfo{
		URL:       proxyURL,
		Type:      proxyType,
		Host:      host,
		Port:      port,
		IsHealthy: true, // 初始假设健康
		LastCheck: time.Now(),
	}, nil
}

// GetNextProxy 获取下一个可用代理
func (pm *ProxyManager) GetNextProxy() (*ProxyInfo, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if len(pm.proxies) == 0 {
		return nil, fmt.Errorf("no proxies available")
	}

	// 寻找健康的代理
	startIndex := pm.currentIndex
	for i := 0; i < len(pm.proxies); i++ {
		index := (startIndex + i) % len(pm.proxies)
		proxy := pm.proxies[index]

		if proxy.IsHealthy && proxy.ErrorCount < pm.maxErrorCount {
			pm.currentIndex = (index + 1) % len(pm.proxies)
			return proxy, nil
		}
	}

	// 如果没有健康的代理，返回错误计数最少的
	var bestProxy *ProxyInfo
	minErrors := int(^uint(0) >> 1) // 最大int值

	for _, proxy := range pm.proxies {
		if proxy.ErrorCount < minErrors {
			minErrors = proxy.ErrorCount
			bestProxy = proxy
		}
	}

	if bestProxy != nil {
		pm.logger.Warn("No healthy proxies available, using best available",
			zap.String("proxy", bestProxy.URL),
			zap.Int("error_count", bestProxy.ErrorCount))
		return bestProxy, nil
	}

	return nil, fmt.Errorf("no usable proxies available")
}

// GetNextProxyExcluding 获取下一个可用代理，排除指定的代理URL
func (pm *ProxyManager) GetNextProxyExcluding(excludeURL string) (*ProxyInfo, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if len(pm.proxies) == 0 {
		return nil, fmt.Errorf("no proxies available")
	}

	if len(pm.proxies) == 1 && pm.proxies[0].URL == excludeURL {
		return nil, fmt.Errorf("only one proxy available and it's excluded")
	}

	// 寻找健康的代理，排除指定的URL
	startIndex := pm.currentIndex
	for i := 0; i < len(pm.proxies); i++ {
		index := (startIndex + i) % len(pm.proxies)
		proxy := pm.proxies[index]

		// 跳过被排除的代理
		if proxy.URL == excludeURL {
			continue
		}

		if proxy.IsHealthy && proxy.ErrorCount < pm.maxErrorCount {
			pm.currentIndex = (index + 1) % len(pm.proxies)
			pm.logger.Debug("Selected alternative proxy",
				zap.String("proxy", proxy.URL),
				zap.String("excluded", excludeURL))
			return proxy, nil
		}
	}

	// 如果没有健康的代理，返回错误计数最少的（排除指定URL）
	var bestProxy *ProxyInfo
	minErrors := int(^uint(0) >> 1) // 最大int值

	for _, proxy := range pm.proxies {
		if proxy.URL != excludeURL && proxy.ErrorCount < minErrors {
			minErrors = proxy.ErrorCount
			bestProxy = proxy
		}
	}

	if bestProxy != nil {
		pm.logger.Warn("No healthy alternative proxies available, using best available",
			zap.String("proxy", bestProxy.URL),
			zap.String("excluded", excludeURL),
			zap.Int("error_count", bestProxy.ErrorCount))
		return bestProxy, nil
	}

	return nil, fmt.Errorf("no usable alternative proxies available")
}

// MarkProxyError 标记代理错误
func (pm *ProxyManager) MarkProxyError(proxyURL string, err error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	for _, proxy := range pm.proxies {
		if proxy.URL == proxyURL {
			proxy.ErrorCount++
			proxy.LastError = err.Error()

			// 检查是否是429错误，如果是则立即加入黑名单
			if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "Too Many Requests") {
				pm.blacklist[proxyURL] = time.Now().Add(pm.blacklistTTL)
				pm.logger.Warn("Proxy added to blacklist due to 429 error",
					zap.String("proxy", proxyURL),
					zap.String("error", err.Error()),
					zap.Duration("blacklist_ttl", pm.blacklistTTL))
			}

			if proxy.ErrorCount >= pm.maxErrorCount {
				proxy.IsHealthy = false
				pm.logger.Warn("Proxy marked as unhealthy",
					zap.String("proxy", proxyURL),
					zap.Int("error_count", proxy.ErrorCount),
					zap.String("last_error", err.Error()))
			}
			break
		}
	}
}

// MarkProxySuccess 标记代理成功
func (pm *ProxyManager) MarkProxySuccess(proxyURL string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	for _, proxy := range pm.proxies {
		if proxy.URL == proxyURL {
			proxy.ErrorCount = 0
			proxy.IsHealthy = true
			proxy.LastError = ""
			break
		}
	}
}

// CreateHTTPClient 为指定代理创建HTTP客户端
func (pm *ProxyManager) CreateHTTPClient(proxyInfo *ProxyInfo, timeout time.Duration) (*http.Client, error) {
	if proxyInfo == nil {
		// 返回无代理的客户端
		return &http.Client{
			Timeout: timeout,
		}, nil
	}

	switch proxyInfo.Type {
	case "http", "https":
		return pm.createHTTPProxyClient(proxyInfo, timeout)
	case "socks4", "socks5":
		return pm.createSOCKSProxyClient(proxyInfo, timeout)
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxyInfo.Type)
	}
}

// createHTTPProxyClient 创建HTTP代理客户端
func (pm *ProxyManager) createHTTPProxyClient(proxyInfo *ProxyInfo, timeout time.Duration) (*http.Client, error) {
	proxyURL, err := url.Parse(proxyInfo.URL)
	if err != nil {
		return nil, fmt.Errorf("invalid proxy URL: %w", err)
	}

	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		TLSHandshakeTimeout: 10 * time.Second,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}, nil
}

// createSOCKSProxyClient 创建SOCKS代理客户端
func (pm *ProxyManager) createSOCKSProxyClient(proxyInfo *ProxyInfo, timeout time.Duration) (*http.Client, error) {
	var dialer proxy.Dialer
	var err error

	address := net.JoinHostPort(proxyInfo.Host, proxyInfo.Port)

	switch proxyInfo.Type {
	case "socks4":
		dialer, err = proxy.SOCKS5("tcp", address, nil, proxy.Direct)
	case "socks5":
		dialer, err = proxy.SOCKS5("tcp", address, nil, proxy.Direct)
	default:
		return nil, fmt.Errorf("unsupported SOCKS type: %s", proxyInfo.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create SOCKS dialer: %w", err)
	}

	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return dialer.Dial(network, addr)
		},
		TLSHandshakeTimeout: 10 * time.Second,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}, nil
}

// startHealthCheck 启动代理健康检查
func (pm *ProxyManager) startHealthCheck() {
	ticker := time.NewTicker(pm.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (pm *ProxyManager) performHealthCheck() {
	pm.mutex.RLock()
	proxies := make([]*ProxyInfo, len(pm.proxies))
	copy(proxies, pm.proxies)
	pm.mutex.RUnlock()

	var wg sync.WaitGroup
	healthyCount := 0

	for _, proxy := range proxies {
		wg.Add(1)
		go func(p *ProxyInfo) {
			defer wg.Done()

			isHealthy := pm.checkProxyHealth(p)

			pm.mutex.Lock()
			p.IsHealthy = isHealthy
			p.LastCheck = time.Now()
			if isHealthy {
				p.ErrorCount = 0
				p.LastError = ""
			}
			pm.mutex.Unlock()

			if isHealthy {
				healthyCount++
			}
		}(proxy)
	}

	wg.Wait()

	pm.mutex.Lock()
	pm.healthyProxies = healthyCount
	pm.mutex.Unlock()

	pm.logger.Debug("Health check completed",
		zap.Int("total_proxies", len(proxies)),
		zap.Int("healthy_proxies", healthyCount))
}

// checkProxyHealth 检查单个代理的健康状态
func (pm *ProxyManager) checkProxyHealth(proxy *ProxyInfo) bool {
	client, err := pm.CreateHTTPClient(proxy, pm.healthCheckTimeout)
	if err != nil {
		pm.logger.Debug("Failed to create client for health check",
			zap.String("proxy", proxy.URL),
			zap.Error(err))
		return false
	}

	ctx, cancel := context.WithTimeout(context.Background(), pm.healthCheckTimeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", pm.healthCheckURL, nil)
	if err != nil {
		return false
	}

	resp, err := client.Do(req)
	if err != nil {
		pm.logger.Debug("Health check failed",
			zap.String("proxy", proxy.URL),
			zap.Error(err))
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// GetStats 获取代理管理器统计信息
func (pm *ProxyManager) GetStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	return map[string]interface{}{
		"total_proxies":   pm.totalProxies,
		"healthy_proxies": pm.healthyProxies,
		"last_reload":     pm.lastReload,
		"current_index":   pm.currentIndex,
	}
}

// GetProxyList 获取代理列表（用于监控）
func (pm *ProxyManager) GetProxyList() []*ProxyInfo {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 返回副本以避免并发问题
	result := make([]*ProxyInfo, len(pm.proxies))
	for i, proxy := range pm.proxies {
		result[i] = &ProxyInfo{
			URL:        proxy.URL,
			Type:       proxy.Type,
			Host:       proxy.Host,
			Port:       proxy.Port,
			IsHealthy:  proxy.IsHealthy,
			LastCheck:  proxy.LastCheck,
			ErrorCount: proxy.ErrorCount,
			LastError:  proxy.LastError,
		}
	}

	return result
}
