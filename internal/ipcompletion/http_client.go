package ipcompletion

import (
	"context"
	"fmt"
	"io"
	"math"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries      int           `json:"max_retries"`
	InitialDelay    time.Duration `json:"initial_delay"`
	MaxDelay        time.Duration `json:"max_delay"`
	BackoffFactor   float64       `json:"backoff_factor"`
	RetryableErrors []int         `json:"retryable_errors"` // HTTP状态码
}

// DefaultRetryConfig 默认重试配置
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		RetryableErrors: []int{
			http.StatusTooManyRequests,     // 429
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
		},
	}
}

// HTTPClientWithRetry 带重试机制的HTTP客户端
type HTTPClientWithRetry struct {
	proxyManager *ProxyManager
	retryConfig  RetryConfig
	logger       *zap.Logger
	timeout      time.Duration
}

// NewHTTPClientWithRetry 创建带重试机制的HTTP客户端
func NewHTTPClientWithRetry(proxyManager *ProxyManager, logger *zap.Logger) *HTTPClientWithRetry {
	return &HTTPClientWithRetry{
		proxyManager: proxyManager,
		retryConfig:  DefaultRetryConfig(),
		logger:       logger,
		timeout:      30 * time.Second,
	}
}

// SetRetryConfig 设置重试配置
func (c *HTTPClientWithRetry) SetRetryConfig(config RetryConfig) {
	c.retryConfig = config
}

// SetTimeout 设置请求超时时间
func (c *HTTPClientWithRetry) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
}

// DoRequest 执行HTTP请求，带重试和代理轮换
// 逻辑：先直连 -> 429后启用代理 -> 代理429后黑名单10分钟 -> 无可用代理时退出
func (c *HTTPClientWithRetry) DoRequest(ctx context.Context, method, url string, body io.Reader) (*http.Response, error) {
	var lastErr error
	var currentProxy *ProxyInfo
	var useProxy bool = false // 是否启用代理模式

	for attempt := 0; attempt <= c.retryConfig.MaxRetries; attempt++ {
		// 代理选择逻辑
		c.logger.Info("Attempting request",
			zap.String("url", url),
			zap.Bool("use_proxy", useProxy),
			zap.Int("attempt", attempt+1))

		if useProxy {
			if c.proxyManager == nil {
				c.logger.Error("Proxy mode requested but no proxy manager configured",
					zap.String("url", url),
					zap.Int("attempt", attempt+1))
				return nil, fmt.Errorf("proxy mode requested but no proxy manager configured")
			}

			// 获取可用代理（排除黑名单）
			proxy, err := c.proxyManager.GetAvailableProxy()
			if err != nil {
				c.logger.Error("No available proxies, terminating IP completion",
					zap.Error(err),
					zap.String("url", url))
				return nil, fmt.Errorf("no available proxies for IP completion: %w", err)
			}
			currentProxy = proxy
			c.logger.Debug("Using proxy for request",
				zap.String("proxy", proxy.URL),
				zap.String("url", url),
				zap.Int("attempt", attempt+1))
		} else {
			// 直连模式
			currentProxy = nil
			if attempt == 0 || (attempt > 0 && !useProxy) {
				c.logger.Debug("Using direct connection",
					zap.String("url", url),
					zap.Bool("use_proxy", useProxy),
					zap.Int("attempt", attempt+1))
			}
		}

		// 创建HTTP客户端
		client, err := c.createClient(currentProxy)
		if err != nil {
			lastErr = fmt.Errorf("failed to create HTTP client: %w", err)
			c.logger.Warn("Failed to create HTTP client",
				zap.Int("attempt", attempt+1),
				zap.Error(err))
			continue
		}

		// 创建请求
		req, err := http.NewRequestWithContext(ctx, method, url, body)
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}

		// 设置User-Agent
		req.Header.Set("User-Agent", "ipInsight/1.0 IP Geolocation Completion Service")

		// 记录请求详情
		c.logger.Debug("Making HTTP request",
			zap.String("method", method),
			zap.String("url", url),
			zap.String("proxy", c.getProxyURL(currentProxy)),
			zap.Bool("use_proxy", useProxy),
			zap.Int("attempt", attempt+1))

		// 执行请求
		resp, err := client.Do(req)
		if err != nil {
			lastErr = err

			// 标记代理错误
			if currentProxy != nil {
				c.proxyManager.MarkProxyError(currentProxy.URL, err)
			}

			c.logger.Warn("HTTP request failed",
				zap.String("url", url),
				zap.String("proxy", c.getProxyURL(currentProxy)),
				zap.Int("attempt", attempt+1),
				zap.Error(err))

			// 如果不是最后一次尝试，等待后重试
			if attempt < c.retryConfig.MaxRetries {
				delay := c.calculateDelay(attempt)
				c.logger.Debug("Retrying after delay",
					zap.Duration("delay", delay),
					zap.Int("next_attempt", attempt+2))

				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(delay):
					continue
				}
			}
			continue
		}

		// 检查HTTP状态码
		if c.shouldRetry(resp.StatusCode) {
			resp.Body.Close() // 关闭响应体

			lastErr = fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)

			// 特殊处理429错误（速率限制）
			if resp.StatusCode == http.StatusTooManyRequests {
				if currentProxy != nil {
					// 代理遇到429，加入黑名单
					c.proxyManager.MarkProxyError(currentProxy.URL, lastErr)
					c.logger.Warn("Proxy rate limited, added to blacklist",
						zap.String("url", url),
						zap.String("failed_proxy", c.getProxyURL(currentProxy)),
						zap.Int("status_code", resp.StatusCode),
						zap.Int("attempt", attempt+1))
				} else {
					// 直连遇到429，启用代理模式
					useProxy = true
					c.logger.Warn("Direct connection rate limited, switching to proxy mode",
						zap.String("url", url),
						zap.Int("status_code", resp.StatusCode),
						zap.Int("attempt", attempt+1))
				}
			}

			// 如果不是最后一次尝试，等待后重试
			if attempt < c.retryConfig.MaxRetries {
				delay := c.calculateDelay(attempt)
				c.logger.Debug("Retrying after HTTP error",
					zap.Int("status_code", resp.StatusCode),
					zap.Duration("delay", delay),
					zap.Int("next_attempt", attempt+2))

				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(delay):
					c.logger.Debug("Retry delay expired, attempting next request")
					continue
				}
			}
			continue
		}

		// 请求成功，标记代理成功
		if currentProxy != nil {
			c.proxyManager.MarkProxySuccess(currentProxy.URL)
		}

		c.logger.Debug("HTTP request successful",
			zap.String("url", url),
			zap.String("proxy", c.getProxyURL(currentProxy)),
			zap.Int("status_code", resp.StatusCode),
			zap.Int("attempt", attempt+1))

		return resp, nil
	}

	return nil, fmt.Errorf("request failed after %d attempts: %w", c.retryConfig.MaxRetries+1, lastErr)
}

// createClient 创建HTTP客户端
func (c *HTTPClientWithRetry) createClient(proxyInfo *ProxyInfo) (*http.Client, error) {
	if c.proxyManager != nil {
		return c.proxyManager.CreateHTTPClient(proxyInfo, c.timeout)
	}

	// 无代理客户端
	return &http.Client{
		Timeout: c.timeout,
	}, nil
}

// shouldRetry 判断是否应该重试
func (c *HTTPClientWithRetry) shouldRetry(statusCode int) bool {
	for _, code := range c.retryConfig.RetryableErrors {
		if statusCode == code {
			return true
		}
	}
	return false
}

// calculateDelay 计算重试延迟时间（指数退避）
func (c *HTTPClientWithRetry) calculateDelay(attempt int) time.Duration {
	delay := float64(c.retryConfig.InitialDelay) * math.Pow(c.retryConfig.BackoffFactor, float64(attempt))

	if delay > float64(c.retryConfig.MaxDelay) {
		delay = float64(c.retryConfig.MaxDelay)
	}

	return time.Duration(delay / 10)
}

// getProxyURL 获取代理URL（用于日志）
func (c *HTTPClientWithRetry) getProxyURL(proxy *ProxyInfo) string {
	if proxy == nil {
		return "direct"
	}
	return proxy.URL
}

// Get 执行GET请求
func (c *HTTPClientWithRetry) Get(ctx context.Context, url string) (*http.Response, error) {
	return c.DoRequest(ctx, "GET", url, nil)
}

// Post 执行POST请求
func (c *HTTPClientWithRetry) Post(ctx context.Context, url string, body io.Reader) (*http.Response, error) {
	return c.DoRequest(ctx, "POST", url, body)
}

// GetStats 获取客户端统计信息
func (c *HTTPClientWithRetry) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"retry_config": c.retryConfig,
		"timeout":      c.timeout,
	}

	if c.proxyManager != nil {
		stats["proxy_stats"] = c.proxyManager.GetStats()
	}

	return stats
}
