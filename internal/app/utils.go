package app

import (
	"context"
	"fmt"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5"
	"github.com/joho/godotenv"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	shutdownTimeout = 5 * time.Second
	quickTimeout    = 500 * time.Millisecond
)

// CreateAPIDocsData 创建API文档数据
func CreateAPIDocsData(port int) gin.H {
	return gin.H{
		"title":    "ipInsight API Documentation",
		"version":  "1.0.0",
		"base_url": fmt.Sprintf("http://localhost:%d", port),
		"endpoints": []gin.H{
			{
				"method":      "GET",
				"path":        "/api/v1/ip/{ip}",
				"description": "Query IP information for a single IP address",
				"parameters": gin.H{
					"ip": "IP address to query (IPv4 or IPv6)",
				},
				"example": fmt.Sprintf("curl http://localhost:%d/api/v1/ip/*******", port),
			},
			{
				"method":      "POST",
				"path":        "/api/v1/ip/batch",
				"description": "Query IP information for multiple IP addresses",
				"body": gin.H{
					"ips": []string{"*******", "*******"},
				},
				"example": fmt.Sprintf(`curl -X POST http://localhost:%d/api/v1/ip/batch -H "Content-Type: application/json" -d '{"ips":["*******","*******"]}'`, port),
			},
			{
				"method":      "GET",
				"path":        "/health",
				"description": "Health check endpoint",
				"example":     fmt.Sprintf("curl http://localhost:%d/health", port),
			},
			{
				"method":      "POST",
				"path":        "/api/v1/auth/login",
				"description": "Admin authentication",
				"body": gin.H{
					"username": "admin",
					"password": "your_password",
				},
			},
			{
				"method":      "POST",
				"path":        "/api/v1/admin/fetch",
				"description": "Manual data source update (requires authentication)",
				"headers": gin.H{
					"Authorization": "Bearer {jwt_token}",
				},
				"body": gin.H{
					"sources": []string{"maxmind", "ip2location"},
					"force":   false,
				},
			},
		},
		"authentication": gin.H{
			"type":           "JWT Bearer Token or API Key",
			"header":         "Authorization: Bearer {token}",
			"api_key_header": "X-API-Key: {api_key}",
		},
	}
}

// QuickCheckServices 快速检查必要的服务是否可用
func QuickCheckServices(cfg *config.Config) error {
	ctx, cancel := context.WithTimeout(context.Background(), quickTimeout)
	defer cancel()

	// 快速检查 PostgreSQL
	pgConn := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?connect_timeout=1",
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.DBName,
	)
	if _, err := pgx.Connect(ctx, pgConn); err != nil {
		return fmt.Errorf("postgreSQL 服务未就绪: %w", err)
	}

	// 快速检查 Redis
	redisClient := redis.NewClient(&redis.Options{
		Addr:        fmt.Sprintf("%s:%d", cfg.Cache.Host, cfg.Cache.Port),
		Password:    cfg.Cache.Password,
		DB:          0,
		DialTimeout: quickTimeout,
	})
	defer redisClient.Close()

	if err := redisClient.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("redis 服务未就绪: %w", err)
	}
	fmt.Println("✅PostgreSQL 服务已就绪")
	fmt.Println("✅Redis 服务已就绪")
	return nil
}

// LoadEnvFile 加载 .env 文件中的环境变量
func LoadEnvFile() error {
	// 尝试加载 .env 文件
	if err := godotenv.Load(); err != nil {
		return fmt.Errorf("failed to load .env file: %w", err)
	}
	fmt.Println("✅.env 文件已加载")
	return nil
}

// CreateDefaultAdminUser 创建默认管理员用户（如果配置启用且用户不存在）
func CreateDefaultAdminUser(userService service.UserService, authConfig *config.AuthConfig, logger *zap.Logger) error {
	// 检查是否启用默认管理员用户创建
	if authConfig.DefaultAdmin == nil || !authConfig.DefaultAdmin.Enabled {
		logger.Info("Default admin user creation is disabled")
		return nil
	}

	defaultAdmin := authConfig.DefaultAdmin

	// 验证配置完整性
	if defaultAdmin.Username == "" || defaultAdmin.Password == "" {
		logger.Warn("Default admin user configuration is incomplete, skipping creation",
			zap.String("username", defaultAdmin.Username),
			zap.Bool("password_set", defaultAdmin.Password != ""))
		return nil
	}

	ctx := context.Background()

	// 检查用户是否已存在
	existingUser, err := userService.GetUserByUsername(ctx, defaultAdmin.Username)
	if err == nil && existingUser != nil {
		logger.Info("Default admin user already exists, skipping creation",
			zap.String("username", defaultAdmin.Username),
			zap.Int64("user_id", existingUser.ID))
		return nil
	}

	// 创建默认管理员用户请求
	createReq := &model.CreateUserRequest{
		Username: defaultAdmin.Username,
		Email:    defaultAdmin.Email,
		Password: defaultAdmin.Password,
		Role:     string(model.RoleAdmin), // 设置为管理员角色
		FullName: defaultAdmin.FullName,
	}

	// 如果没有设置邮箱，使用默认值
	if createReq.Email == "" {
		createReq.Email = fmt.Sprintf("%<EMAIL>", defaultAdmin.Username)
	}

	// 如果没有设置全名，使用默认值
	if createReq.FullName == "" {
		createReq.FullName = "默认管理员"
	}

	// 创建用户（系统创建，createdBy设为0）
	createdUser, err := userService.CreateUser(ctx, createReq, 0)
	if err != nil {
		return fmt.Errorf("failed to create default admin user: %w", err)
	}

	emailStr := ""
	if createdUser.Email != nil {
		emailStr = *createdUser.Email
	}

	logger.Info("Default admin user created successfully",
		zap.Int64("user_id", createdUser.ID),
		zap.String("username", createdUser.Username),
		zap.String("email", emailStr),
		zap.String("role", string(createdUser.Role)))

	// 安全提醒
	if defaultAdmin.Password == "admin123" {
		logger.Warn("⚠️  SECURITY WARNING: Default admin user is using the default password 'admin123'")
		logger.Warn("⚠️  Please change the password immediately after first login for security reasons")
		logger.Warn("⚠️  You can change the password in the configuration file or through the admin interface")
	}

	return nil
}

// GetShutdownTimeout 获取关闭超时时间
func GetShutdownTimeout() time.Duration {
	return shutdownTimeout
}
