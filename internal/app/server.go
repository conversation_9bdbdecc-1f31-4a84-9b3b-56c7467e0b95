package app

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/api"
	"github.com/cosin2077/ipInsight/internal/auth"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/errors"
	"github.com/cosin2077/ipInsight/internal/ipcompletion"
	"github.com/cosin2077/ipInsight/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// initializeHTTPServer 初始化HTTP服务器
func (app *App) initializeHTTPServer(svc *service.Service) error {
	router := gin.Default()

	// 添加错误处理中间件
	router.Use(errors.ErrorHandlerMiddleware(app.logger))

	// 设置静态文件服务
	app.setupStaticRoutes(router)

	// 设置API路由
	app.setupAPIRoutes(router, svc)

	// 设置404处理
	app.setup404Handler(router)

	app.server = &http.Server{
		Addr:    fmt.Sprintf("127.0.0.1:%d", app.config.Server.Port),
		Handler: router,
	}

	app.logger.Info("HTTP server initialized successfully")
	return nil
}

// setupStaticRoutes 设置静态文件路由
func (app *App) setupStaticRoutes(router *gin.Engine) {
	// 静态文件服务 - 服务前端看板
	router.Static("/assets", "./dashboard/dist/assets")
	router.StaticFile("/favicon.ico", "./dashboard/dist/vite.svg")

	// 前端看板路由
	router.GET("/dashboard", func(c *gin.Context) {
		c.File("./dashboard/dist/index.html")
	})
	router.GET("/dashboard/*any", func(c *gin.Context) {
		c.File("./dashboard/dist/index.html")
	})
}

// setupAPIRoutes 设置API路由
func (app *App) setupAPIRoutes(router *gin.Engine, svc *service.Service) {
	// 添加根路径和文档路由
	app.setupWelcomeRoutes(router, svc)

	// 初始化用户服务和认证
	userService := app.initializeUserService()
	authService := auth.NewAuthService(&app.config.Auth, app.logger, userService)

	// 创建默认管理员用户
	if err := CreateDefaultAdminUser(userService, &app.config.Auth, app.logger); err != nil {
		app.logger.Error("Failed to create default admin user", zap.Error(err))
	}

	// 初始化IP补全服务
	completionHandler := app.initializeIPCompletionService()

	// 注册API路由
	api.NewAPI(svc, authService, completionHandler).RegisterRoutes(router)
}

// setupWelcomeRoutes 设置欢迎页面和文档路由
func (app *App) setupWelcomeRoutes(router *gin.Engine, svc *service.Service) {
	port := app.config.Server.Port

	// 根路径欢迎页面
	router.GET("/", func(c *gin.Context) {
		health := svc.GetHealthStatus(c.Request.Context())
		stats := svc.GetQueryStats()

		welcomeData := gin.H{
			"service":     "ipInsight",
			"version":     "1.0.0",
			"description": "High-performance IP geolocation service with multi-source data aggregation",
			"timestamp":   time.Now().Unix(),
			"health":      health,
			"stats":       stats,
			"endpoints": gin.H{
				"dashboard":    "/dashboard",
				"health":       "/health",
				"api_docs":     "/docs",
				"query_single": "/api/v1/ip/{ip}",
				"query_batch":  "/api/v1/ip/batch",
				"admin_login":  "/api/v1/auth/login",
				"admin_status": "/api/v1/admin/status",
				"admin_fetch":  "/api/v1/admin/fetch",
				"metrics":      "/api/v1/metrics",
			},
			"examples": gin.H{
				"single_query": fmt.Sprintf("curl http://localhost:%d/api/v1/ip/*******", port),
				"batch_query":  fmt.Sprintf(`curl -X POST http://localhost:%d/api/v1/ip/batch -H "Content-Type: application/json" -d '{"ips":["*******","*******"]}'`, port),
				"health_check": fmt.Sprintf("curl http://localhost:%d/health", port),
			},
			"documentation": gin.H{
				"github":    "https://github.com/cosin2077/ipInsight",
				"api_docs":  fmt.Sprintf("http://localhost:%d/docs", port),
				"dashboard": fmt.Sprintf("http://localhost:%d/dashboard", port),
			},
		}

		c.JSON(http.StatusOK, welcomeData)
	})

	// API文档页面
	router.GET("/docs", func(c *gin.Context) {
		docsData := CreateAPIDocsData(port)
		c.JSON(http.StatusOK, docsData)
	})

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
}

// initializeUserService 初始化用户服务
func (app *App) initializeUserService() service.UserService {
	app.logger.Info("Initializing user repository and service...")

	if adapter, ok := app.db.(*database.OptimizedDatabaseAdapter); ok {
		app.logger.Info("Successfully cast database to OptimizedDatabaseAdapter")
		pool := adapter.GetPool()
		if pool != nil {
			app.logger.Info("Database connection pool is valid, creating user repository")
			userRepo := database.NewPostgreSQLUserRepository(pool, app.logger)
			userService := service.NewUserService(userRepo, app.logger)
			app.logger.Info("User repository and service created successfully")
			return userService
		} else {
			app.logger.Error("Database connection pool is nil")
		}
	} else {
		app.logger.Error("Failed to cast database to OptimizedDatabaseAdapter")
	}

	return nil
}

// initializeIPCompletionService 初始化IP补全服务
func (app *App) initializeIPCompletionService() *ipcompletion.APIHandler {
	if !app.config.IPCompletion.Enabled {
		return nil
	}

	app.logger.Info("Initializing IP geolocation completion service")

	// 转换配置
	completionConfig := ipcompletion.CompletionConfig{
		APIBaseURL:             "https://ipapi.co",
		APIKey:                 app.config.IPCompletion.APIKey,
		BatchSize:              app.config.IPCompletion.BatchSize,
		ConcurrentLimit:        app.config.IPCompletion.ConcurrentLimit,
		RequestTimeout:         30 * time.Second,
		ProxyConfigPath:        app.config.IPCompletion.ProxyConfigPath,
		EnableProxy:            app.config.IPCompletion.EnableProxy,
		EnableCIDROptimization: app.config.IPCompletion.EnableCIDROptimization,
		MaxCIDRSize:            24,
		DBBatchSize:            100,
		DBTimeout:              30 * time.Second,
		RequestsPerSecond:      app.config.IPCompletion.RequestsPerSecond,
		MaxRetries:             app.config.IPCompletion.MaxRetries,
		InitialDelay:           time.Duration(app.config.IPCompletion.InitialDelay) * time.Millisecond,
		MaxDelay:               time.Duration(app.config.IPCompletion.MaxDelay) * time.Millisecond,
		BackoffFactor:          float64(app.config.IPCompletion.BackoffFactor),
	}

	// 构建内部重试配置对象
	completionConfig.RetryConfig = ipcompletion.RetryConfig{
		MaxRetries:      completionConfig.MaxRetries,
		InitialDelay:    completionConfig.InitialDelay,
		MaxDelay:        completionConfig.MaxDelay,
		BackoffFactor:   completionConfig.BackoffFactor,
		RetryableErrors: []int{429, 500, 502, 503, 504},
	}

	// 创建补全服务
	completionService, err := ipcompletion.NewCompletionService(
		completionConfig,
		app.db,
		app.logger,
	)
	if err != nil {
		app.logger.Error("Failed to create completion service", zap.Error(err))
		return nil
	}

	// 创建API处理器
	completionHandler := ipcompletion.NewAPIHandler(completionService, app.logger)
	app.logger.Info("IP geolocation completion service initialized successfully")
	return completionHandler
}

// setup404Handler 设置404处理器
func (app *App) setup404Handler(router *gin.Engine) {
	port := app.config.Server.Port

	router.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method

		// 提供友好的错误信息和建议
		suggestions := []string{}

		// 根据请求路径提供建议
		if strings.Contains(path, "/ip/") {
			suggestions = append(suggestions, "/api/v1/ip/{ip} - Query single IP")
			suggestions = append(suggestions, "/api/v1/ip/batch - Batch query IPs")
		}
		if strings.Contains(path, "/admin") {
			suggestions = append(suggestions, "/api/v1/admin/status - System status")
			suggestions = append(suggestions, "/api/v1/admin/fetch - Manual data fetch")
		}
		if strings.Contains(path, "/api") {
			suggestions = append(suggestions, "/api/v1/ip/{ip} - Query IP information")
			suggestions = append(suggestions, "/docs - API documentation")
		}
		if strings.Contains(path, "/ip-completion") {
			suggestions = append(suggestions, "/api/v1/admin/ip-completion/status - Query ip-completion status")
			suggestions = append(suggestions, "/api/v1/admin/ip-completion/start - start ip-completion")
		}

		// 默认建议
		if len(suggestions) == 0 {
			suggestions = []string{
				"/ - Service welcome page",
				"/docs - API documentation",
				"/health - Health check",
				"/api/v1/ip/{ip} - Query IP information",
			}
		}

		errorResponse := gin.H{
			"error":         "Not Found",
			"message":       fmt.Sprintf("The requested endpoint '%s %s' was not found", method, path),
			"suggestions":   suggestions,
			"documentation": fmt.Sprintf("http://localhost:%d/docs", port),
			"timestamp":     time.Now().Unix(),
		}

		app.logger.Debug("404 Not Found",
			zap.String("method", method),
			zap.String("path", path),
			zap.String("user_agent", c.GetHeader("User-Agent")),
			zap.String("remote_addr", c.ClientIP()))

		c.JSON(http.StatusNotFound, errorResponse)
	})
}
