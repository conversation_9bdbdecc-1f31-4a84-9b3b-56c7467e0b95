package app

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/cosin2077/ipInsight/internal/cache"
	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/scheduler"
	"go.uber.org/zap"
)

// App 应用程序主结构体
type App struct {
	logger        *zap.Logger
	config        *config.Config
	db            database.DatabaseInterface
	cache         *cache.Cache
	scheduler     *scheduler.Scheduler
	server        *http.Server
	datasources   []datasource.Datasource
	updateManager *datasource.UpdateManager
}

// New 创建新的应用实例
func NewApp() *App {
	return &App{}
}

// Initialize 初始化应用程序
func (app *App) Initialize() error {
	var err error

	LoadEnvFile()
	// 初始化日志
	if err = app.initializeLogger(); err != nil {
		return fmt.Errorf("failed to initialize logger: %w", err)
	}

	// 加载配置
	if err = app.loadConfiguration(); err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	// 初始化数据库
	if err = app.initializeDatabase(); err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// 初始化数据源
	if err = app.initializeDatasources(); err != nil {
		return fmt.Errorf("failed to initialize datasources: %w", err)
	}

	// 初始化缓存
	app.initializeCache()

	// 初始化服务层
	svc := app.initializeService()

	// 初始化更新管理器
	if err = app.initializeUpdateManager(svc); err != nil {
		return fmt.Errorf("failed to initialize update manager: %w", err)
	}

	// 初始化调度器
	app.initializeScheduler()

	// 初始化HTTP服务器
	if err = app.initializeHTTPServer(svc); err != nil {
		return fmt.Errorf("failed to initialize HTTP server: %w", err)
	}

	return nil
}

// Run 运行应用程序
func (app *App) Run(port int, forceUpdate bool) error {
	// 设置端口（如果指定）
	if port != 0 {
		app.config.Server.Port = port
	}

	// 快速检查服务依赖
	if err := QuickCheckServices(app.config); err != nil {
		app.logger.Error("Service dependency check failed", zap.Error(err))
		fmt.Printf("❌ %v\n", err)
		fmt.Println("请确保 PostgreSQL 和 Redis 服务正在运行")
		return err
	}

	// 处理启动时的数据源更新
	app.handleStartupDataSourceUpdate()

	// 启动调度器
	app.StartScheduler(forceUpdate)

	// 启动HTTP服务器
	app.StartHTTPServer()

	return nil
}

// StartScheduler 启动调度器
func (app *App) StartScheduler(force bool) {
	app.logger.Info("Starting scheduler", zap.Bool("force", force))
	app.scheduler.Start(force, app.datasources)
}

// StartHTTPServer 启动HTTP服务器
func (app *App) StartHTTPServer() {
	serverPort := fmt.Sprintf(":%d", app.config.Server.Port)
	app.logger.Info("Starting server",
		zap.String("url", fmt.Sprintf("http://localhost%s", serverPort)),
	)

	go func() {
		if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()
}

// Shutdown 优雅关闭应用程序
func (app *App) Shutdown(ctx context.Context) error {
	app.logger.Info("Shutting down server...")

	// 关闭HTTP服务器
	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Error("Server forced to shutdown", zap.Error(err))
		return err
	}

	app.logger.Info("Server shutdown completed")
	return nil
}

// Cleanup 清理资源
func (app *App) Cleanup() {
	if app.db != nil {
		app.db.Close()
		app.logger.Info("Database closed")
	}

	if app.logger != nil {
		app.logger.Sync()
	}
}

// GetConfig 获取配置
func (app *App) GetConfig() *config.Config {
	return app.config
}

// GetLogger 获取日志器
func (app *App) GetLogger() *zap.Logger {
	return app.logger
}

// handleStartupDataSourceUpdate 处理启动时的数据源更新
func (app *App) handleStartupDataSourceUpdate() {
	if !app.config.Startup.AutoUpdateDatasources {
		app.logger.Info("Automatic datasource update on startup is disabled for faster startup")
		app.logger.Info("Use /api/v1/admin/fetch API to manually update datasources when needed")
		app.logger.Info("To enable auto-update on startup, set 'startup.auto_update_datasources: true' in config.yaml")
		return
	}

	app.logger.Info("Starting automatic datasource update on startup (enabled in config)")
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()

		results, err := app.updateManager.UpdateAllDatasources(ctx)
		if err != nil {
			app.logger.Error("Automatic datasource update failed", zap.Error(err))
			return
		}

		successCount := 0
		for _, result := range results {
			if result.Success {
				successCount++
			}
		}
		app.logger.Info("Automatic datasource update completed",
			zap.Int("total", len(results)),
			zap.Int("success", successCount))
	}()
}
