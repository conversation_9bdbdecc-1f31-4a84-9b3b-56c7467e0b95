package app

import (
	"context"
	"path/filepath"

	"github.com/cosin2077/ipInsight/internal/cache"
	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/datasource/api/ipgeolocation"
	"github.com/cosin2077/ipInsight/internal/datasource/api/ipinfo"
	"github.com/cosin2077/ipInsight/internal/datasource/dbip"
	"github.com/cosin2077/ipInsight/internal/datasource/ip2location"
	"github.com/cosin2077/ipInsight/internal/datasource/ipapi"
	"github.com/cosin2077/ipInsight/internal/datasource/iplocate"
	"github.com/cosin2077/ipInsight/internal/datasource/ipstack"
	"github.com/cosin2077/ipInsight/internal/datasource/maxmind"
	"github.com/cosin2077/ipInsight/internal/datasource/qqwry"
	"github.com/cosin2077/ipInsight/internal/scheduler"
	"github.com/cosin2077/ipInsight/internal/service"
	"github.com/cosin2077/ipInsight/internal/utils"
)

// APIDatasources API数据源集合
type APIDatasources struct {
	ipInfo        *ipinfo.IPInfo
	ipGeolocation *ipgeolocation.IPGeolocation
	ipStack       *ipstack.IPStack
}

// initializeLogger 初始化日志系统
func (app *App) initializeLogger() error {
	logger, err := utils.GetLogger()
	if err != nil {
		return err
	}
	app.logger = logger
	return nil
}

// loadConfiguration 加载配置
func (app *App) loadConfiguration() error {
	app.logger.Info("Loading configuration...")
	cfg, err := config.LoadConfig(app.logger)
	if err != nil {
		return err
	}
	app.config = cfg
	return nil
}

// initializeDatabase 初始化数据库
func (app *App) initializeDatabase() error {
	app.logger.Info("Initializing optimized database...")

	optimizedDB, err := database.NewOptimizedDatabaseAdapter(app.config.Database, app.logger)
	if err != nil {
		return err
	}
	app.db = optimizedDB

	// 初始化数据库表
	if err := app.db.Initialize(context.Background()); err != nil {
		return err
	}

	app.logger.Info("Optimized database initialized successfully")
	return nil
}

// initializeCache 初始化缓存
func (app *App) initializeCache() {
	app.logger.Info("Initializing cache...")
	app.cache = cache.NewCache(app.config.Cache, app.logger)
}

// initializeDatasources 初始化数据源
func (app *App) initializeDatasources() error {
	app.logger.Info("Initializing data sources...")

	app.datasources = []datasource.Datasource{
		maxmind.NewMaxmind(app.config.Datasources.Maxmind, app.logger, app.db),
		ip2location.NewIP2Location(app.config.Datasources.IP2Location, app.logger, app.db),
		dbip.NewDBIP(app.config.Datasources.DBIP, app.logger, app.db),
		ipapi.NewIPAPI(app.config.Datasources.IPAPI, app.logger, app.db),
		iplocate.NewIPLocate(app.config.Datasources.IPLocate, app.logger, app.db),
		qqwry.NewQQWryDatasource(&app.config.Datasources.QQWry, filepath.Join("data", "qqwry"), app.logger, app.db),
	}

	return nil
}

// initializeService 初始化服务层
func (app *App) initializeService() *service.Service {
	apiDataSources := app.initializeAPIDatasources()
	svc := service.NewService(
		app.db,
		app.cache,
		apiDataSources.ipInfo,
		apiDataSources.ipGeolocation,
		apiDataSources.ipStack,
		app.logger,
		app.config,
	)
	svc.SetDatasources(app.datasources)
	return svc
}

// initializeAPIDatasources 初始化API数据源
func (app *App) initializeAPIDatasources() *APIDatasources {
	return &APIDatasources{
		ipInfo:        ipinfo.NewIPInfo(app.config.Datasources.IPInfo, app.logger),
		ipGeolocation: ipgeolocation.NewIPGeolocation(app.config.Datasources.IPGeolocation, app.logger),
		ipStack:       ipstack.NewIPStack(app.config.Datasources.IPStack, app.logger),
	}
}

// initializeUpdateManager 初始化更新管理器
func (app *App) initializeUpdateManager(svc *service.Service) error {
	fileProcessor := datasource.NewFileProcessor(app.logger)
	fusionEngine := datasource.NewDataFusionEngine(app.logger)

	app.updateManager = datasource.NewUpdateManager(
		app.datasources,
		app.db,
		fileProcessor,
		fusionEngine,
		app.logger,
		app.config,
	)

	svc.SetUpdateManager(app.updateManager)
	app.updateManager.SetAPIValidator(svc.GetAPIValidator())

	app.logger.Info("Update manager initialized successfully")
	return nil
}

// initializeScheduler 初始化调度器
func (app *App) initializeScheduler() {
	app.scheduler = scheduler.NewScheduler(app.logger, app.config)
	app.scheduler.RegisterDatasources(app.config, app.datasources)
}
