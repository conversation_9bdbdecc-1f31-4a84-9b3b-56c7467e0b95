package auth

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

type AuthService struct {
	config      *config.AuthConfig
	logger      *zap.Logger
	userService service.UserService
}

type Claims struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResponse struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
}

func NewAuthService(cfg *config.AuthConfig, logger *zap.Logger, userService service.UserService) *AuthService {
	return &AuthService{
		config:      cfg,
		logger:      logger,
		userService: userService,
	}
}

// GenerateJWT 生成JWT token
func (a *AuthService) GenerateJWT(userID int64, username, role string) (string, int64, error) {
	expirationTime := time.Now().Add(time.Duration(a.config.TokenTTL) * time.Hour)

	claims := &Claims{
		UserID:   userID,
		Username: username,
		Role:     role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "ipinsight",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(a.config.JWTSecret))
	if err != nil {
		return "", 0, err
	}

	// 创建会话记录
	if a.userService != nil {
		// 创建一个带超时的context，而不是传递nil
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		a.userService.LogUserActivity(ctx, &userID, "jwt_created", "auth", "token", nil, "", "", true, "")
	}

	return tokenString, expirationTime.Unix(), nil
}

// generateTokenHash 生成token哈希
func (a *AuthService) generateTokenHash(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// ValidateJWT 验证JWT token
func (a *AuthService) ValidateJWT(tokenString string) (*Claims, error) {
	claims := &Claims{}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(a.config.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	return claims, nil
}

// ValidateAPIKey 验证API Key
func (a *AuthService) ValidateAPIKey(apiKey string) bool {
	for _, key := range a.config.APIKeys {
		if key == apiKey {
			return true
		}
	}
	return false
}

// ValidateUser 验证用户凭据
func (a *AuthService) ValidateUser(username, password string) (*Claims, error) {
	// 如果有用户服务，使用数据库验证
	if a.userService != nil {
		// 创建一个带超时的context，而不是传递nil
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		user, err := a.userService.AuthenticateUser(ctx, username, password)
		if err != nil {
			return nil, err
		}

		return &Claims{
			UserID:   user.ID,
			Username: user.Username,
			Role:     user.Role,
		}, nil
	}

	// 回退到配置文件验证（向后兼容）
	for _, user := range a.config.AdminUsers {
		if user == username && password == "admin123" {
			return &Claims{
				UserID:   0, // 配置文件用户没有ID
				Username: username,
				Role:     "admin",
			}, nil
		}
	}

	return nil, errors.New("invalid username or password")
}

// JWTAuthMiddleware JWT认证中间件
func (a *AuthService) JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// 检查Bearer token格式
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		claims, err := a.ValidateJWT(tokenString)
		if err != nil {
			a.logger.Warn("JWT validation failed", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// 将用户信息存储到context中
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Next()
	}
}

// APIKeyAuthMiddleware API Key认证中间件
func (a *AuthService) APIKeyAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 支持多种API Key传递方式
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			apiKey = c.GetHeader("Authorization")
			if strings.HasPrefix(apiKey, "Bearer ") {
				apiKey = strings.TrimPrefix(apiKey, "Bearer ")
			}
		}
		if apiKey == "" {
			apiKey = c.Query("api_key")
		}

		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key required"})
			c.Abort()
			return
		}

		if !a.ValidateAPIKey(apiKey) {
			a.logger.Warn("Invalid API key", zap.String("api_key", apiKey))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
			c.Abort()
			return
		}

		// 设置用户信息
		c.Set("username", "api_user")
		c.Set("role", "admin")
		c.Next()
	}
}

// FlexibleAuthMiddleware 灵活认证中间件（支持JWT或API Key）
func (a *AuthService) FlexibleAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		apiKey := c.GetHeader("X-API-Key")

		// 优先尝试JWT认证
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if claims, err := a.ValidateJWT(tokenString); err == nil {
				c.Set("user_id", claims.UserID)
				c.Set("username", claims.Username)
				c.Set("role", claims.Role)
				c.Set("auth_method", "jwt")
				c.Next()
				return
			}
		}

		// 尝试API Key认证
		if apiKey == "" {
			apiKey = c.Query("api_key")
		}
		if apiKey != "" {
			// 首先尝试数据库中的用户API Key
			if a.userService != nil {
				if user, err := a.userService.AuthenticateAPIKey(c.Request.Context(), apiKey); err == nil {
					c.Set("user_id", user.ID)
					c.Set("username", user.Username)
					c.Set("role", user.Role)
					c.Set("auth_method", "user_api_key")
					c.Next()
					return
				}
			}

			// 回退到配置文件API Key
			if a.ValidateAPIKey(apiKey) {
				c.Set("user_id", int64(0))
				c.Set("username", "api_user")
				c.Set("role", "admin")
				c.Set("auth_method", "config_api_key")
				c.Next()
				return
			}
		}

		// 认证失败
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required. Please provide a valid JWT token or API key",
		})
		c.Abort()
	}
}
