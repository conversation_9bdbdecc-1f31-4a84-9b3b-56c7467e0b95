package database

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

// UserRepository 用户数据访问接口
type UserRepository interface {
	// 用户CRUD操作
	CreateUser(ctx context.Context, req *model.CreateUserRequest, createdBy int64) (*model.User, error)
	GetUserByID(ctx context.Context, id int64) (*model.User, error)
	GetUserByUsername(ctx context.Context, username string) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	GetUserByAPIKey(ctx context.Context, apiKey string) (*model.User, error)
	UpdateUser(ctx context.Context, id int64, req *model.UpdateUserRequest, updatedBy int64) (*model.User, error)
	DeleteUser(ctx context.Context, id int64) error
	ListUsers(ctx context.Context, page, pageSize int, role, status string) (*model.UserListResponse, error)

	// 密码相关
	VerifyPassword(ctx context.Context, username, password string) (*model.User, error)
	ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error
	GeneratePasswordResetToken(ctx context.Context, email string) (string, error)
	ResetPassword(ctx context.Context, token, newPassword string) error

	// API Key管理
	GenerateAPIKey(ctx context.Context, userID int64) (string, error)
	RevokeAPIKey(ctx context.Context, userID int64) error

	// 会话管理
	CreateSession(ctx context.Context, userID int64, tokenHash, ipAddress, userAgent string, expiresAt time.Time) error
	GetSession(ctx context.Context, tokenHash string) (*model.UserSession, error)
	DeleteSession(ctx context.Context, tokenHash string) error
	DeleteUserSessions(ctx context.Context, userID int64) error
	CleanupExpiredSessions(ctx context.Context) (int, error)

	// 用户状态管理
	LockUser(ctx context.Context, userID int64, duration time.Duration) error
	UnlockUser(ctx context.Context, userID int64) error
	UpdateLoginInfo(ctx context.Context, userID int64, ipAddress string, success bool) error

	// 权限管理
	GrantPermission(ctx context.Context, userID int64, permission, resource string, grantedBy int64, expiresAt *time.Time) error
	RevokePermission(ctx context.Context, userID int64, permission, resource string) error
	HasPermission(ctx context.Context, userID int64, permission, resource string) (bool, error)
	GetUserPermissions(ctx context.Context, userID int64) ([]model.UserPermission, error)

	// 活动日志
	LogActivity(ctx context.Context, userID *int64, action, resource, resourceID string, details map[string]interface{}, ipAddress, userAgent string, success bool, errorMessage string) error
	GetUserActivityLogs(ctx context.Context, userID int64, page, pageSize int) ([]model.UserActivityLog, int64, error)

	// 统计信息
	GetUserSummaries(ctx context.Context) ([]model.UserSummary, error)
	GetUserActivitySummaries(ctx context.Context) ([]model.UserActivitySummary, error)
	GetUserCount(ctx context.Context) (int64, error)
	GetActiveUserCount(ctx context.Context) (int64, error)
}

// PostgreSQLUserRepository PostgreSQL用户仓库实现
type PostgreSQLUserRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLUserRepository 创建PostgreSQL用户仓库
func NewPostgreSQLUserRepository(pool *pgxpool.Pool, logger *zap.Logger) UserRepository {
	return &PostgreSQLUserRepository{
		pool:   pool,
		logger: logger,
	}
}

// CreateUser 创建用户
func (r *PostgreSQLUserRepository) CreateUser(ctx context.Context, req *model.CreateUserRequest, createdBy int64) (*model.User, error) {
	// 生成密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	user := &model.User{}
	query := `
		INSERT INTO users (username, email, password_hash, role, full_name, phone, created_by, status)
		VALUES ($1, $2, $3, $4, $5, $6, $7, 'active')
		RETURNING id, username, email, role, status, full_name, phone, email_verified, phone_verified,
		          two_factor_enabled, login_count, failed_login_attempts, created_at, updated_at, created_by
	`

	err = r.pool.QueryRow(ctx, query, req.Username, req.Email, string(passwordHash), req.Role, req.FullName, req.Phone, createdBy).Scan(
		&user.ID, &user.Username, &user.Email, &user.Role, &user.Status, &user.FullName, &user.Phone,
		&user.EmailVerified, &user.PhoneVerified, &user.TwoFactorEnabled, &user.LoginCount,
		&user.FailedLoginAttempts, &user.CreatedAt, &user.UpdatedAt, &user.CreatedBy,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	r.logger.Info("User created successfully", zap.String("username", user.Username), zap.Int64("id", user.ID))
	return user, nil
}

// GetUserByUsername 根据用户名获取用户
func (r *PostgreSQLUserRepository) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	// 添加调试日志
	if r.pool == nil {
		r.logger.Error("Database connection pool is nil in GetUserByUsername",
			zap.String("username", username))
		return nil, fmt.Errorf("database connection pool is nil")
	}

	r.logger.Debug("Getting user by username",
		zap.String("username", username),
		zap.Bool("pool_valid", r.pool != nil))

	user := &model.User{}
	query := `
		SELECT id, username, email, password_hash, role, status, full_name, avatar_url, phone,
		       email_verified, phone_verified, two_factor_enabled, two_factor_secret,
		       last_login_at, last_login_ip, login_count, failed_login_attempts, locked_until,
		       password_changed_at, api_key, api_key_created_at, api_key_last_used_at,
		       created_at, updated_at, created_by, updated_by
		FROM users
		WHERE username = $1 AND status != 'deleted'
	`

	err := r.pool.QueryRow(ctx, query, username).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &user.Role, &user.Status,
		&user.FullName, &user.AvatarURL, &user.Phone, &user.EmailVerified, &user.PhoneVerified,
		&user.TwoFactorEnabled, &user.TwoFactorSecret, &user.LastLoginAt, &user.LastLoginIP,
		&user.LoginCount, &user.FailedLoginAttempts, &user.LockedUntil, &user.PasswordChangedAt,
		&user.APIKey, &user.APIKeyCreatedAt, &user.APIKeyLastUsedAt, &user.CreatedAt, &user.UpdatedAt,
		&user.CreatedBy, &user.UpdatedBy,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found: %s", username)
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// VerifyPassword 验证用户密码
func (r *PostgreSQLUserRepository) VerifyPassword(ctx context.Context, username, password string) (*model.User, error) {
	user, err := r.GetUserByUsername(ctx, username)
	if err != nil {
		r.logger.Error("Failed to get user by username",
			zap.String("username", username),
			zap.Error(err))
		return nil, err
	}

	r.logger.Debug("User found for password verification",
		zap.String("username", username),
		zap.Int64("user_id", user.ID),
		zap.String("role", string(user.Role)),
		zap.String("status", string(user.Status)))

	// 检查用户状态
	if !user.CanLogin() {
		r.logger.Warn("User cannot login due to status",
			zap.String("username", username),
			zap.String("status", string(user.Status)))
		return nil, fmt.Errorf("user account is locked or inactive")
	}

	// 验证密码
	r.logger.Debug("Verifying password",
		zap.String("username", username),
		zap.String("password_hash", user.PasswordHash))

	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		r.logger.Warn("Password verification failed",
			zap.String("username", username),
			zap.Error(err))
		// 记录失败的登录尝试
		r.UpdateLoginInfo(ctx, user.ID, "", false)
		return nil, fmt.Errorf("invalid password")
	}

	r.logger.Info("Password verification successful",
		zap.String("username", username))
	return user, nil
}

// UpdateLoginInfo 更新登录信息
func (r *PostgreSQLUserRepository) UpdateLoginInfo(ctx context.Context, userID int64, ipAddress string, success bool) error {
	if success {
		query := `
			UPDATE users 
			SET last_login_at = NOW(), 
			    last_login_ip = $2, 
			    login_count = login_count + 1,
			    failed_login_attempts = 0,
			    updated_at = NOW()
			WHERE id = $1
		`
		_, err := r.pool.Exec(ctx, query, userID, ipAddress)
		return err
	} else {
		query := `
			UPDATE users 
			SET failed_login_attempts = failed_login_attempts + 1,
			    updated_at = NOW()
			WHERE id = $1
		`
		_, err := r.pool.Exec(ctx, query, userID)

		// 如果失败次数过多，锁定用户
		if err == nil {
			var attempts int
			checkQuery := `SELECT failed_login_attempts FROM users WHERE id = $1`
			r.pool.QueryRow(ctx, checkQuery, userID).Scan(&attempts)

			if attempts >= 5 {
				r.LockUser(ctx, userID, time.Hour) // 锁定1小时
			}
		}

		return err
	}
}

// GenerateAPIKey 生成API密钥
func (r *PostgreSQLUserRepository) GenerateAPIKey(ctx context.Context, userID int64) (string, error) {
	// 生成32字节的随机密钥
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	apiKey := hex.EncodeToString(bytes)

	query := `
		UPDATE users 
		SET api_key = $2, 
		    api_key_created_at = NOW(),
		    updated_at = NOW()
		WHERE id = $1
	`

	_, err := r.pool.Exec(ctx, query, userID, apiKey)
	if err != nil {
		return "", fmt.Errorf("failed to update API key: %w", err)
	}

	return apiKey, nil
}

// GetUserByAPIKey 根据API密钥获取用户
func (r *PostgreSQLUserRepository) GetUserByAPIKey(ctx context.Context, apiKey string) (*model.User, error) {
	user := &model.User{}
	query := `
		SELECT id, username, email, role, status, full_name, avatar_url, phone,
		       email_verified, phone_verified, two_factor_enabled,
		       last_login_at, last_login_ip, login_count, failed_login_attempts, locked_until,
		       api_key_created_at, api_key_last_used_at, created_at, updated_at
		FROM users 
		WHERE api_key = $1 AND status = 'active'
	`

	err := r.pool.QueryRow(ctx, query, apiKey).Scan(
		&user.ID, &user.Username, &user.Email, &user.Role, &user.Status,
		&user.FullName, &user.AvatarURL, &user.Phone, &user.EmailVerified, &user.PhoneVerified,
		&user.TwoFactorEnabled, &user.LastLoginAt, &user.LastLoginIP, &user.LoginCount,
		&user.FailedLoginAttempts, &user.LockedUntil, &user.APIKeyCreatedAt, &user.APIKeyLastUsedAt,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("invalid API key")
		}
		return nil, fmt.Errorf("failed to get user by API key: %w", err)
	}

	// 更新API密钥最后使用时间
	updateQuery := `UPDATE users SET api_key_last_used_at = NOW() WHERE id = $1`
	r.pool.Exec(ctx, updateQuery, user.ID)

	return user, nil
}

// LockUser 锁定用户
func (r *PostgreSQLUserRepository) LockUser(ctx context.Context, userID int64, duration time.Duration) error {
	query := `
		UPDATE users 
		SET locked_until = NOW() + $2::interval,
		    updated_at = NOW()
		WHERE id = $1
	`

	_, err := r.pool.Exec(ctx, query, userID, duration)
	return err
}

// UnlockUser 解锁用户
func (r *PostgreSQLUserRepository) UnlockUser(ctx context.Context, userID int64) error {
	query := `
		UPDATE users 
		SET locked_until = NULL,
		    failed_login_attempts = 0,
		    updated_at = NOW()
		WHERE id = $1
	`

	_, err := r.pool.Exec(ctx, query, userID)
	return err
}

// LogActivity 记录用户活动
func (r *PostgreSQLUserRepository) LogActivity(ctx context.Context, userID *int64, action, resource, resourceID string, details map[string]interface{}, ipAddress, userAgent string, success bool, errorMessage string) error {
	query := `
		INSERT INTO user_activity_logs (user_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	var detailsJSON sql.NullString
	if details != nil {
		// 这里应该将details转换为JSON字符串
		// 为简化，暂时存储为空
		detailsJSON = sql.NullString{Valid: false}
	}

	_, err := r.pool.Exec(ctx, query, userID, action, resource, resourceID, detailsJSON, ipAddress, userAgent, success, errorMessage)
	return err
}

// GetUserByID 根据ID获取用户
func (r *PostgreSQLUserRepository) GetUserByID(ctx context.Context, id int64) (*model.User, error) {
	user := &model.User{}
	query := `
		SELECT id, username, email, password_hash, role, status, full_name, avatar_url, phone,
		       email_verified, phone_verified, two_factor_enabled, two_factor_secret,
		       last_login_at, last_login_ip, login_count, failed_login_attempts, locked_until,
		       password_changed_at, api_key, api_key_created_at, api_key_last_used_at,
		       created_at, updated_at, created_by, updated_by
		FROM users
		WHERE id = $1 AND status != 'deleted'
	`

	err := r.pool.QueryRow(ctx, query, id).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &user.Role, &user.Status,
		&user.FullName, &user.AvatarURL, &user.Phone, &user.EmailVerified, &user.PhoneVerified,
		&user.TwoFactorEnabled, &user.TwoFactorSecret, &user.LastLoginAt, &user.LastLoginIP,
		&user.LoginCount, &user.FailedLoginAttempts, &user.LockedUntil, &user.PasswordChangedAt,
		&user.APIKey, &user.APIKeyCreatedAt, &user.APIKeyLastUsedAt, &user.CreatedAt, &user.UpdatedAt,
		&user.CreatedBy, &user.UpdatedBy,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found with ID: %d", id)
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// GetUserByEmail 根据邮箱获取用户
func (r *PostgreSQLUserRepository) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	user := &model.User{}
	query := `
		SELECT id, username, email, password_hash, role, status, full_name, avatar_url, phone,
		       email_verified, phone_verified, two_factor_enabled, two_factor_secret,
		       last_login_at, last_login_ip, login_count, failed_login_attempts, locked_until,
		       password_changed_at, api_key, api_key_created_at, api_key_last_used_at,
		       created_at, updated_at, created_by, updated_by
		FROM users
		WHERE email = $1 AND status != 'deleted'
	`

	err := r.pool.QueryRow(ctx, query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &user.Role, &user.Status,
		&user.FullName, &user.AvatarURL, &user.Phone, &user.EmailVerified, &user.PhoneVerified,
		&user.TwoFactorEnabled, &user.TwoFactorSecret, &user.LastLoginAt, &user.LastLoginIP,
		&user.LoginCount, &user.FailedLoginAttempts, &user.LockedUntil, &user.PasswordChangedAt,
		&user.APIKey, &user.APIKeyCreatedAt, &user.APIKeyLastUsedAt, &user.CreatedAt, &user.UpdatedAt,
		&user.CreatedBy, &user.UpdatedBy,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found with email: %s", email)
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// UpdateUser 更新用户信息
func (r *PostgreSQLUserRepository) UpdateUser(ctx context.Context, id int64, req *model.UpdateUserRequest, updatedBy int64) (*model.User, error) {
	query := `
		UPDATE users
		SET email = COALESCE($2, email),
		    role = COALESCE($3, role),
		    status = COALESCE($4, status),
		    full_name = COALESCE($5, full_name),
		    phone = COALESCE($6, phone),
		    updated_by = $7,
		    updated_at = NOW()
		WHERE id = $1
		RETURNING id, username, email, role, status, full_name, phone, updated_at
	`

	user := &model.User{}
	err := r.pool.QueryRow(ctx, query, id, req.Email, req.Role, req.Status, req.FullName, req.Phone, updatedBy).Scan(
		&user.ID, &user.Username, &user.Email, &user.Role, &user.Status, &user.FullName, &user.Phone, &user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found with ID: %d", id)
		}
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

// DeleteUser 删除用户（软删除）
func (r *PostgreSQLUserRepository) DeleteUser(ctx context.Context, id int64) error {
	query := `UPDATE users SET status = 'deleted', updated_at = NOW() WHERE id = $1`
	result, err := r.pool.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("user not found with ID: %d", id)
	}

	return nil
}

// ListUsers 获取用户列表
func (r *PostgreSQLUserRepository) ListUsers(ctx context.Context, page, pageSize int, role, status string) (*model.UserListResponse, error) {
	offset := (page - 1) * pageSize

	// 构建查询条件
	whereClause := "WHERE status != 'deleted'"
	args := []interface{}{}
	argIndex := 1

	if role != "" {
		whereClause += fmt.Sprintf(" AND role = $%d", argIndex)
		args = append(args, role)
		argIndex++
	}

	if status != "" {
		whereClause += fmt.Sprintf(" AND status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM users %s", whereClause)
	var total int64
	err := r.pool.QueryRow(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// 获取用户列表
	query := fmt.Sprintf(`
		SELECT id, username, email, role, status, full_name, phone, email_verified, phone_verified,
		       two_factor_enabled, last_login_at, last_login_ip, login_count, failed_login_attempts,
		       locked_until, api_key_created_at, api_key_last_used_at, created_at, updated_at
		FROM users %s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, pageSize, offset)

	rows, err := r.pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query users: %w", err)
	}
	defer rows.Close()

	var users []model.User
	for rows.Next() {
		var user model.User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.Role, &user.Status, &user.FullName, &user.Phone,
			&user.EmailVerified, &user.PhoneVerified, &user.TwoFactorEnabled, &user.LastLoginAt,
			&user.LastLoginIP, &user.LoginCount, &user.FailedLoginAttempts, &user.LockedUntil,
			&user.APIKeyCreatedAt, &user.APIKeyLastUsedAt, &user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &model.UserListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// ChangePassword 修改密码
func (r *PostgreSQLUserRepository) ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error {
	// 首先验证当前密码
	user, err := r.GetUserByID(ctx, userID)
	if err != nil {
		return err
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(currentPassword))
	if err != nil {
		return fmt.Errorf("current password is incorrect")
	}

	// 生成新密码哈希
	newPasswordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// 更新密码
	query := `
		UPDATE users
		SET password_hash = $2,
		    password_changed_at = NOW(),
		    updated_at = NOW()
		WHERE id = $1
	`

	_, err = r.pool.Exec(ctx, query, userID, string(newPasswordHash))
	if err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// RevokeAPIKey 撤销API密钥
func (r *PostgreSQLUserRepository) RevokeAPIKey(ctx context.Context, userID int64) error {
	query := `
		UPDATE users
		SET api_key = NULL,
		    api_key_created_at = NULL,
		    api_key_last_used_at = NULL,
		    updated_at = NOW()
		WHERE id = $1
	`

	_, err := r.pool.Exec(ctx, query, userID)
	return err
}

// CreateSession 创建用户会话
func (r *PostgreSQLUserRepository) CreateSession(ctx context.Context, userID int64, tokenHash, ipAddress, userAgent string, expiresAt time.Time) error {
	query := `
		INSERT INTO user_sessions (user_id, token_hash, device_info, ip_address, user_agent, expires_at)
		VALUES ($1, $2, NULL, $3, $4, $5)
	`

	_, err := r.pool.Exec(ctx, query, userID, tokenHash, ipAddress, userAgent, expiresAt)
	return err
}

// GetSession 获取会话
func (r *PostgreSQLUserRepository) GetSession(ctx context.Context, tokenHash string) (*model.UserSession, error) {
	session := &model.UserSession{}
	query := `
		SELECT id, user_id, token_hash, device_info, ip_address, user_agent, expires_at, created_at, last_used_at
		FROM user_sessions
		WHERE token_hash = $1 AND expires_at > NOW()
	`

	err := r.pool.QueryRow(ctx, query, tokenHash).Scan(
		&session.ID, &session.UserID, &session.TokenHash, &session.DeviceInfo,
		&session.IPAddress, &session.UserAgent, &session.ExpiresAt, &session.CreatedAt, &session.LastUsedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("session not found or expired")
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// 更新最后使用时间
	updateQuery := `UPDATE user_sessions SET last_used_at = NOW() WHERE id = $1`
	r.pool.Exec(ctx, updateQuery, session.ID)

	return session, nil
}

// DeleteSession 删除会话
func (r *PostgreSQLUserRepository) DeleteSession(ctx context.Context, tokenHash string) error {
	query := `DELETE FROM user_sessions WHERE token_hash = $1`
	_, err := r.pool.Exec(ctx, query, tokenHash)
	return err
}

// DeleteUserSessions 删除用户的所有会话
func (r *PostgreSQLUserRepository) DeleteUserSessions(ctx context.Context, userID int64) error {
	query := `DELETE FROM user_sessions WHERE user_id = $1`
	_, err := r.pool.Exec(ctx, query, userID)
	return err
}

// CleanupExpiredSessions 清理过期会话
func (r *PostgreSQLUserRepository) CleanupExpiredSessions(ctx context.Context) (int, error) {
	query := `DELETE FROM user_sessions WHERE expires_at < NOW()`
	result, err := r.pool.Exec(ctx, query)
	if err != nil {
		return 0, err
	}
	return int(result.RowsAffected()), nil
}

// HasPermission 检查用户权限
func (r *PostgreSQLUserRepository) HasPermission(ctx context.Context, userID int64, permission, resource string) (bool, error) {
	query := `SELECT user_has_permission($1, $2, $3)`
	var hasPermission bool
	err := r.pool.QueryRow(ctx, query, userID, permission, resource).Scan(&hasPermission)
	return hasPermission, err
}

// GetUserCount 获取用户总数
func (r *PostgreSQLUserRepository) GetUserCount(ctx context.Context) (int64, error) {
	var count int64
	query := `SELECT COUNT(*) FROM users WHERE status != 'deleted'`
	err := r.pool.QueryRow(ctx, query).Scan(&count)
	return count, err
}

// GetActiveUserCount 获取活跃用户数
func (r *PostgreSQLUserRepository) GetActiveUserCount(ctx context.Context) (int64, error) {
	var count int64
	query := `SELECT COUNT(*) FROM users WHERE status = 'active'`
	err := r.pool.QueryRow(ctx, query).Scan(&count)
	return count, err
}

// GeneratePasswordResetToken 生成密码重置令牌
func (r *PostgreSQLUserRepository) GeneratePasswordResetToken(ctx context.Context, email string) (string, error) {
	// 生成随机令牌
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	token := hex.EncodeToString(bytes)
	expiresAt := time.Now().Add(time.Hour) // 1小时后过期

	query := `
		UPDATE users
		SET password_reset_token = $2,
		    password_reset_expires = $3,
		    updated_at = NOW()
		WHERE email = $1 AND status = 'active'
	`

	result, err := r.pool.Exec(ctx, query, email, token, expiresAt)
	if err != nil {
		return "", fmt.Errorf("failed to set password reset token: %w", err)
	}

	if result.RowsAffected() == 0 {
		return "", fmt.Errorf("user not found with email: %s", email)
	}

	return token, nil
}

// ResetPassword 重置密码
func (r *PostgreSQLUserRepository) ResetPassword(ctx context.Context, token, newPassword string) error {
	// 验证令牌
	var userID int64
	checkQuery := `
		SELECT id FROM users
		WHERE password_reset_token = $1
		AND password_reset_expires > NOW()
		AND status = 'active'
	`

	err := r.pool.QueryRow(ctx, checkQuery, token).Scan(&userID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return fmt.Errorf("invalid or expired reset token")
		}
		return fmt.Errorf("failed to verify reset token: %w", err)
	}

	// 生成新密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// 更新密码并清除重置令牌
	updateQuery := `
		UPDATE users
		SET password_hash = $2,
		    password_reset_token = NULL,
		    password_reset_expires = NULL,
		    password_changed_at = NOW(),
		    updated_at = NOW()
		WHERE id = $1
	`

	_, err = r.pool.Exec(ctx, updateQuery, userID, string(passwordHash))
	if err != nil {
		return fmt.Errorf("failed to reset password: %w", err)
	}

	return nil
}

// GrantPermission 授予权限
func (r *PostgreSQLUserRepository) GrantPermission(ctx context.Context, userID int64, permission, resource string, grantedBy int64, expiresAt *time.Time) error {
	query := `
		INSERT INTO user_permissions (user_id, permission, resource, granted_by, expires_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (user_id, permission, resource)
		DO UPDATE SET
		    granted_by = EXCLUDED.granted_by,
		    granted_at = NOW(),
		    expires_at = EXCLUDED.expires_at
	`

	_, err := r.pool.Exec(ctx, query, userID, permission, resource, grantedBy, expiresAt)
	return err
}

// RevokePermission 撤销权限
func (r *PostgreSQLUserRepository) RevokePermission(ctx context.Context, userID int64, permission, resource string) error {
	query := `DELETE FROM user_permissions WHERE user_id = $1 AND permission = $2 AND resource = $3`
	_, err := r.pool.Exec(ctx, query, userID, permission, resource)
	return err
}

// GetUserPermissions 获取用户权限列表
func (r *PostgreSQLUserRepository) GetUserPermissions(ctx context.Context, userID int64) ([]model.UserPermission, error) {
	query := `
		SELECT id, user_id, permission, resource, granted_at, granted_by, expires_at
		FROM user_permissions
		WHERE user_id = $1 AND (expires_at IS NULL OR expires_at > NOW())
		ORDER BY granted_at DESC
	`

	rows, err := r.pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %w", err)
	}
	defer rows.Close()

	var permissions []model.UserPermission
	for rows.Next() {
		var perm model.UserPermission
		err := rows.Scan(
			&perm.ID, &perm.UserID, &perm.Permission, &perm.Resource,
			&perm.GrantedAt, &perm.GrantedBy, &perm.ExpiresAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan permission: %w", err)
		}
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

// GetUserActivityLogs 获取用户活动日志
func (r *PostgreSQLUserRepository) GetUserActivityLogs(ctx context.Context, userID int64, page, pageSize int) ([]model.UserActivityLog, int64, error) {
	offset := (page - 1) * pageSize

	// 获取总数
	countQuery := `SELECT COUNT(*) FROM user_activity_logs WHERE user_id = $1`
	var total int64
	err := r.pool.QueryRow(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count activity logs: %w", err)
	}

	// 获取日志列表
	query := `
		SELECT id, user_id, action, resource, resource_id, details, ip_address, user_agent, success, error_message, created_at
		FROM user_activity_logs
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.pool.Query(ctx, query, userID, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query activity logs: %w", err)
	}
	defer rows.Close()

	var logs []model.UserActivityLog
	for rows.Next() {
		var log model.UserActivityLog
		err := rows.Scan(
			&log.ID, &log.UserID, &log.Action, &log.Resource, &log.ResourceID,
			&log.Details, &log.IPAddress, &log.UserAgent, &log.Success, &log.ErrorMessage, &log.CreatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan activity log: %w", err)
		}
		logs = append(logs, log)
	}

	return logs, total, nil
}

// GetUserSummaries 获取用户摘要信息
func (r *PostgreSQLUserRepository) GetUserSummaries(ctx context.Context) ([]model.UserSummary, error) {
	query := `
		SELECT id, username, email, role, role_display_name, status, full_name,
		       last_login_at, login_count, created_at, is_locked, has_api_key
		FROM user_summary
		ORDER BY created_at DESC
	`

	rows, err := r.pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query user summaries: %w", err)
	}
	defer rows.Close()

	var summaries []model.UserSummary
	for rows.Next() {
		var summary model.UserSummary
		err := rows.Scan(
			&summary.ID, &summary.Username, &summary.Email, &summary.Role, &summary.RoleDisplayName,
			&summary.Status, &summary.FullName, &summary.LastLoginAt, &summary.LoginCount,
			&summary.CreatedAt, &summary.IsLocked, &summary.HasAPIKey,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user summary: %w", err)
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// GetUserActivitySummaries 获取用户活动摘要
func (r *PostgreSQLUserRepository) GetUserActivitySummaries(ctx context.Context) ([]model.UserActivitySummary, error) {
	query := `
		SELECT username, successful_actions, failed_actions, last_activity, active_days
		FROM user_activity_summary
		ORDER BY last_activity DESC
	`

	rows, err := r.pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query user activity summaries: %w", err)
	}
	defer rows.Close()

	var summaries []model.UserActivitySummary
	for rows.Next() {
		var summary model.UserActivitySummary
		err := rows.Scan(
			&summary.Username, &summary.SuccessfulActions, &summary.FailedActions,
			&summary.LastActivity, &summary.ActiveDays,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan activity summary: %w", err)
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}
