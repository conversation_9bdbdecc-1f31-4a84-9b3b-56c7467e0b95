package api

import (
	"time"

	"github.com/cosin2077/ipInsight/internal/auth"
	"github.com/cosin2077/ipInsight/internal/ipcompletion"
	"github.com/cosin2077/ipInsight/internal/monitoring"
	"github.com/cosin2077/ipInsight/internal/service"
	"github.com/cosin2077/ipInsight/internal/validation"
	"github.com/prometheus/client_golang/prometheus"
	"golang.org/x/time/rate"
)

type API struct {
	service           *service.Service
	limiter           *rate.Limiter
	auth              *auth.AuthService
	completionHandler *ipcompletion.APIHandler
	ipValidator       *validation.IPValidator
	monitor           *monitoring.Monitor
	metrics           struct {
		requests *prometheus.CounterVec
		latency  *prometheus.HistogramVec
	}
}

func NewAPI(service *service.Service, authService *auth.AuthService, completionHandler *ipcompletion.APIHandler) *API {
	api := &API{
		service:           service,
		limiter:           rate.NewLimiter(rate.Every(time.Second/10), 20),
		auth:              authService,
		completionHandler: completionHandler,
		ipValidator:       validation.NewIPValidator(),
		monitor:           service.GetMonitor(), // 从service获取监控器
	}
	// 初始化 Prometheus 指标
	api.metrics.requests = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "ipinsight_api_requests_total",
			Help: "Total number of API requests",
		},
		[]string{"endpoint", "status"},
	)
	api.metrics.latency = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "ipinsight_api_latency_seconds",
			Help:    "API request latency in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"endpoint"},
	)
	prometheus.MustRegister(api.metrics.requests, api.metrics.latency)
	return api
}

// 其余方法不变
