package api

import (
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// RegisterRoutes 注册所有API路由
func (a *API) RegisterRoutes(router *gin.Engine) {
	// 设置基础路由
	a.setupBaseRoutes(router)

	// 设置API路由组
	a.setupAPIRoutes(router)
}

// setupBaseRoutes 设置基础路由
func (a *API) setupBaseRoutes(router *gin.Engine) {
	// Prometheus 指标端点
	router.GET("/api/v1/metrics", gin.WrapH(promhttp.Handler()))
}

// setupAPIRoutes 设置API路由组
func (a *API) setupAPIRoutes(router *gin.Engine) {
	// API v1 路由组
	v1 := router.Group("/api/v1")

	// 添加监控中间件
	if a.monitor != nil {
		v1.Use(a.MonitoringMiddleware(a.monitor))
	}

	// IP查询路由
	a.setupIPRoutes(v1)

	// 认证路由
	a.setupAuthRoutes(v1)

	// 管理员路由（需要认证）
	a.setupAdminRoutes(v1)
}

// setupIPRoutes 设置IP查询相关路由
func (a *API) setupIPRoutes(rg *gin.RouterGroup) {
	// 单个IP查询
	rg.GET("/ip/:ip", a.QueryIP)

	// 批量IP查询
	rg.POST("/ip/batch", a.BatchQueryIP)
}

// setupAuthRoutes 设置认证相关路由
func (a *API) setupAuthRoutes(rg *gin.RouterGroup) {
	auth := rg.Group("/auth")
	{
		auth.POST("/login", a.Login)
	}
}

// setupAdminRoutes 设置管理员路由
func (a *API) setupAdminRoutes(rg *gin.RouterGroup) {
	admin := rg.Group("/admin")
	admin.Use(a.auth.FlexibleAuthMiddleware()) // 应用认证中间件
	{
		// 系统状态和健康检查
		admin.GET("/status", a.GetSystemStatus)
		admin.GET("/health", a.GetHealth)

		// 统计信息
		admin.GET("/stats", a.GetQueryStats)
		admin.GET("/hot-ips", a.GetHotIPs)
		admin.GET("/hot-ip-stats", a.GetHotIPStats)
		admin.GET("/batch-stats", a.GetBatchStats)
		admin.GET("/metrics", a.GetMetrics)
		admin.GET("/summary", a.GetSummary)

		// 数据源管理
		admin.GET("/datasources", a.GetDataSources)
		admin.POST("/fetch", a.ManualFetchData)
		admin.POST("/validate", a.ValidateDatasources)

		// IP补全服务路由（如果启用）
		a.setupIPCompletionRoutes(admin)
	}
}

// setupIPCompletionRoutes 设置IP补全服务路由
func (a *API) setupIPCompletionRoutes(rg *gin.RouterGroup) {
	if a.completionHandler == nil {
		return // IP补全服务未启用
	}

	completion := rg.Group("/ip-completion")
	{
		completion.POST("/start", a.completionHandler.StartCompletion)
		completion.POST("/stop", a.completionHandler.StopCompletion)
		completion.GET("/status", a.completionHandler.GetCompletionStatus)
		completion.GET("/proxies", a.completionHandler.GetProxyList)
		completion.POST("/proxies/reload", a.completionHandler.ReloadProxies)
		completion.GET("/database/stats", a.completionHandler.GetDatabaseStats)
	}
}
