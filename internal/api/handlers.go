package api

import (
	"net/http"
	"sort"
	"time"

	"github.com/cosin2077/ipInsight/internal/auth"
	"github.com/cosin2077/ipInsight/internal/errors"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/gin-gonic/gin"
)

// Login 用户登录接口
func (a *API) Login(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		errors.HandleAppError(c, errors.ErrInvalidRequest.WithDetails("Invalid JSON format"))
		return
	}

	// 验证用户凭据
	claims, err := a.auth.ValidateUser(req.Username, req.Password)
	if err != nil {
		errors.HandleAppError(c, errors.NewAuthError("Invalid username or password", err))
		return
	}

	// 生成JWT token
	token, expiresAt, err := a.auth.GenerateJWT(claims.UserID, claims.Username, claims.Role)
	if err != nil {
		errors.HandleError(c, errors.NewAuthError("Failed to generate token", err))
		return
	}

	c.JSON(http.StatusOK, auth.LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
	})
}

// QueryIP 单个IP查询
func (a *API) QueryIP(c *gin.Context) {
	// 限流检查
	if err := a.limiter.Wait(c); err != nil {
		errors.HandleAppError(c, errors.ErrRateLimit)
		return
	}

	// 获取并验证IP参数
	ip := c.Param("ip")
	if err := a.ipValidator.ValidateIP(ip); err != nil {
		errors.HandleError(c, err)
		return
	}

	// 查询IP信息
	ipInfo, err := a.service.GetIPInfo(c, ip)
	if err != nil {
		if err == model.ErrIPNotFound {
			errors.HandleAppError(c, errors.ErrIPNotFound)
		} else {
			errors.HandleError(c, errors.NewDatabaseError("Failed to query IP information", err))
		}
		return
	}

	c.JSON(http.StatusOK, ipInfo)
}

// BatchQueryIP 批量IP查询
func (a *API) BatchQueryIP(c *gin.Context) {
	// 限流检查
	if err := a.limiter.Wait(c); err != nil {
		errors.HandleAppError(c, errors.ErrRateLimit)
		return
	}

	// 解析请求体
	var request struct {
		IPs []string `json:"ips"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		errors.HandleAppError(c, errors.ErrInvalidRequest.WithDetails("Invalid JSON format"))
		return
	}

	// 验证IP列表
	if err := a.ipValidator.ValidateIPList(request.IPs); err != nil {
		errors.HandleError(c, err)
		return
	}

	// 批量查询IP信息
	results, err := a.service.BatchGetIPInfo(c, request.IPs)
	if err != nil {
		errors.HandleError(c, errors.NewDatabaseError("Failed to batch query IP information", err))
		return
	}

	// 转换为API响应格式
	response := make(map[string]interface{})
	for _, ip := range request.IPs {
		if ipInfo, exists := results[ip]; exists {
			response[ip] = ipInfo
		} else {
			response[ip] = gin.H{"error": "No data found"}
		}
	}

	c.JSON(http.StatusOK, response)
}

// GetSystemStatus 获取系统状态
func (a *API) GetSystemStatus(c *gin.Context) {
	username, _ := c.Get("username")
	authMethod, _ := c.Get("auth_method")

	status := gin.H{
		"status":      "ok",
		"timestamp":   time.Now().Unix(),
		"user":        username,
		"auth_method": authMethod,
		"version":     "1.0.0",
	}
	response := gin.H{
		"timestamp": time.Now().Unix(),
		"status":    status,
	}

	c.JSON(http.StatusOK, response)
}

// GetQueryStats 获取查询统计信息
func (a *API) GetQueryStats(c *gin.Context) {
	stats := a.service.GetQueryStats()

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"stats":     stats,
	}

	c.JSON(http.StatusOK, response)
}

// GetHotIPs 获取热门IP列表
func (a *API) GetHotIPs(c *gin.Context) {
	hotIPs := a.service.GetHotIPs()

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"hot_ips":   hotIPs,
	}

	c.JSON(http.StatusOK, response)
}

// GetHotIPStats 获取热门IP统计信息
func (a *API) GetHotIPStats(c *gin.Context) {
	stats := a.service.GetHotIPStats()

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"stats":     stats,
	}

	c.JSON(http.StatusOK, response)
}

// GetBatchStats 获取批量操作统计信息
func (a *API) GetBatchStats(c *gin.Context) {
	stats := a.service.GetBatchStats()

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"stats":     stats,
	}

	c.JSON(http.StatusOK, response)
}

// GetMetrics 获取监控指标
func (a *API) GetMetrics(c *gin.Context) {
	metrics := a.service.GetMonitoringMetrics()

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"metrics":   metrics,
	}

	c.JSON(http.StatusOK, response)
}

// GetHealth 获取健康状态
func (a *API) GetHealth(c *gin.Context) {
	health := a.service.GetHealthStatus(c.Request.Context())

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"health":    health,
	}

	c.JSON(http.StatusOK, response)
}

// GetSummary 获取监控摘要
func (a *API) GetSummary(c *gin.Context) {
	summary := a.service.GetMonitoringSummary()

	response := gin.H{
		"timestamp": time.Now().Unix(),
		"summary":   summary,
	}

	c.JSON(http.StatusOK, response)
}

// GetDataSources 获取数据源状态列表
func (a *API) GetDataSources(c *gin.Context) {
	dataSources := a.service.GetDataSourcesStatus()

	// 简单排序：按状态优先级和名称排序
	sort.Slice(dataSources, func(i, j int) bool {
		// 如果状态相同，按名称排序
		return dataSources[i].Name < dataSources[j].Name
	})

	response := gin.H{
		"timestamp":   time.Now().Unix(),
		"datasources": dataSources,
	}

	c.JSON(http.StatusOK, response)
}

// ManualFetchData 手动获取数据源数据
func (a *API) ManualFetchData(c *gin.Context) {
	var req struct {
		Sources []string `json:"sources"`
		Force   bool     `json:"force"` // 强制下载参数
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request"})
		return
	}
	result, err := a.service.ManualFetchAndUpdate(c.Request.Context(), req.Sources, req.Force)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "success", "updated_sources": result, "force": req.Force})
}

// ValidateDatasources 验证数据源配置
func (a *API) ValidateDatasources(c *gin.Context) {
	var req struct {
		Sources []string `json:"sources"` // 可选，指定要验证的数据源
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request"})
		return
	}

	results, err := a.service.ValidateDatasources(c.Request.Context(), req.Sources)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 统计验证结果
	validCount := 0
	invalidCount := 0
	for _, result := range results {
		if result.Valid {
			validCount++
		} else {
			invalidCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"status":       "success",
		"total":        len(results),
		"valid":        validCount,
		"invalid":      invalidCount,
		"results":      results,
		"validated_at": time.Now(),
	})
}
