package api

import (
	"net/http"
	"time"

	"github.com/cosin2077/ipInsight/internal/monitoring"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MetricsMiddleware Prometheus指标中间件
func (a *API) MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path

		// 处理请求
		c.Next()

		// 记录指标
		duration := time.Since(start).Seconds()
		status := c.Writer.Status()

		// 更新请求计数器
		a.metrics.requests.WithLabelValues(
			path,
			http.StatusText(status),
		).Inc()

		// 更新延迟直方图
		a.metrics.latency.WithLabelValues(path).Observe(duration)
	}
}

// LoggingMiddleware 请求日志中间件
func (a *API) LoggingMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")

		// 处理请求
		c.Next()

		// 记录日志
		duration := time.Since(start)
		status := c.Writer.Status()
		size := c.Writer.Size()

		fields := []zap.Field{
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", status),
			zap.Duration("duration", duration),
			zap.Int("size", size),
			zap.String("client_ip", clientIP),
			zap.String("user_agent", userAgent),
		}

		// 根据状态码选择日志级别
		switch {
		case status >= 500:
			logger.Error("Server error", fields...)
		case status >= 400:
			logger.Warn("Client error", fields...)
		case status >= 300:
			logger.Info("Redirect", fields...)
		default:
			logger.Debug("Request completed", fields...)
		}
	}
}

// MonitoringMiddleware 监控中间件
func (a *API) MonitoringMiddleware(monitor *monitoring.Monitor) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// 处理请求
		c.Next()

		// 记录监控指标
		duration := time.Since(start)
		status := c.Writer.Status()
		success := status < 400

		// 记录API请求
		monitor.RecordAPIRequest(method, path, duration, success)
	}
}

// RateLimitMiddleware 限流中间件
func (a *API) RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := a.limiter.Wait(c.Request.Context()); err != nil {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Rate limit exceeded",
				"message": "Too many requests, please try again later",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// CORSMiddleware CORS中间件
func (a *API) CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func (a *API) SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}
