package scheduler

import (
	"context"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/go-co-op/gocron"
	"go.uber.org/zap"
)

type Scheduler struct {
	scheduler *gocron.Scheduler
	logger    *zap.Logger
	config    *config.Config
}

func NewScheduler(logger *zap.Logger, config *config.Config) *Scheduler {
	return &Scheduler{
		scheduler: gocron.NewScheduler(time.UTC),
		logger:    logger,
		config:    config,
	}
}
func DoRealJob(ds datasource.Datasource, logger *zap.Logger) ([]model.IPInfo, error) {
	logger.Info("Updating datasource", zap.String("name", ds.Name()))
	ctx := context.Background()
	if err := ds.Fetch(ctx); err != nil {
		logger.Error("Failed to fetch", zap.Error(err))
		return nil, err
	}
	data, err := ds.Parse(ctx)
	if err != nil {
		logger.Error("Failed to parse", zap.Error(err))
		return nil, err
	}
	// database insert
	ds.Update(ctx, data)
	return data, err
}
func (s *Scheduler) UpdateDatasources(config *config.Config, datasources []datasource.Datasource) {
	// 收集所有数据源的结果，按数据源名聚合
	allDataMap := make(map[string][]model.IPInfo)
	var mu sync.Mutex

	for _, ds := range datasources {
		s.logger.Info("run datasource", zap.String("datasource Name", ds.Name()))
		data, _ := DoRealJob(ds, s.logger)
		mu.Lock()
		allDataMap[ds.Name()] = data
		mu.Unlock()
	}
	// 全局融合并入库（用 maxmind 的 Update）
	for _, ds := range datasources {
		if ds.Name() == "maxmind" {
			// 将map转换为slice
			var allData []model.IPInfo
			for _, dataList := range allDataMap {
				allData = append(allData, dataList...)
			}
			// 使用标准的Update方法
			ds.Update(context.Background(), allData)
		}
	}
}
func (s *Scheduler) RegisterDatasources(config *config.Config, datasources []datasource.Datasource) {
	// 收集所有数据源的结果，按数据源名聚合
	allDataMap := make(map[string][]model.IPInfo)
	var mu sync.Mutex

	for _, ds := range datasources {
		var schedule string
		switch ds.Name() {
		case "maxmind":
			schedule = config.Datasources.Maxmind.Schedule
		case "ip2location":
			schedule = config.Datasources.IP2Location.Schedule
		case "dbip":
			schedule = config.Datasources.DBIP.Schedule
		// case "ipinfodb":
		// 	schedule = config.Datasources.IPInfoDB.Schedule
		case "ipapi":
			schedule = config.Datasources.IPAPI.Schedule
		case "iplocate":
			schedule = config.Datasources.IPLocate.Schedule
		case "qqwry":
			schedule = config.Datasources.QQWry.Schedule
		default:
			continue
		}

		s.scheduler.Cron(schedule).Do(func(ds datasource.Datasource) {
			data, _ := DoRealJob(ds, s.logger)
			mu.Lock()
			allDataMap[ds.Name()] = data
			mu.Unlock()
		}, ds)
	}

	// 每周合并和更新数据库（全局融合）
	s.scheduler.Cron("0 0 * * 0").Do(func() {
		s.logger.Info("Merging and updating database (global fusion)")
		mu.Lock()
		defer mu.Unlock()
		for _, ds := range datasources {
			if ds.Name() == "maxmind" {
				// 将map转换为slice
				var allData []model.IPInfo
				for _, dataList := range allDataMap {
					allData = append(allData, dataList...)
				}
				// 使用标准的Update方法
				ds.Update(context.Background(), allData)
			}
		}
		// 清空数据
		for k := range allDataMap {
			allDataMap[k] = nil
		}
	})
}

func (s *Scheduler) Start(force bool, datasources []datasource.Datasource) {
	s.scheduler.StartAsync()
	if force {
		s.logger.Info("starting to UpdateDatasources")
		s.UpdateDatasources(s.config, datasources)
	}
}
