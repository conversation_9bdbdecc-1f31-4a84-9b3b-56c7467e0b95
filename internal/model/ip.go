package model

import (
	"errors"
)

// IPInfo 是 IP 地址信息的核心结构体，包含所有可能的字段，适用于 GeoLite2 和其他 IP 数据库
type IPInfo struct {
	IPRange     IPRange     `json:"ip_range"`    // IP 范围信息（CIDR、版本等）
	Geolocation Geolocation `json:"geolocation"` // 地理位置信息（国家、城市、经纬度等）
	Network     Network     `json:"network"`     // 网络信息（ISP、ASN 等）
	Timezone    Timezone    `json:"timezone"`    // 时区信息
	Security    Security    `json:"security"`    // 安全信息（代理、威胁等级等）
	Metadata    Metadata    `json:"metadata"`    // 元数据（来源、更新时间等）
	Extended    Extended    `json:"extended"`    // 扩展字段（货币、语言、自定义等）
}

// IPRange 描述 IP 地址范围和版本
type IPRange struct {
	// 基础IP范围信息
	CIDR      string `json:"cidr"`       // CIDR 表示（如 "*******/24"）
	StartIP   string `json:"start_ip"`   // 起始 IP 地址
	EndIP     string `json:"end_ip"`     // 结束 IP 地址
	IPVersion string `json:"ip_version"` // IP 版本（"IPv4" 或 "IPv6"）
	Netmask   string `json:"netmask"`    // 子网掩码（可选，部分数据库提供）

	// 查询优化字段 ⭐ 新增
	StartIPInt *int64 `json:"start_ip_int,omitempty"` // 起始IP整数表示（用于快速范围查询）
	EndIPInt   *int64 `json:"end_ip_int,omitempty"`   // 结束IP整数表示
	PrefixLen  *int    `json:"prefix_len,omitempty"`   // CIDR前缀长度

	// 网络特征 ⭐ 新增
	IsAnycast   *bool `json:"is_anycast,omitempty"`   // 是否为任播网络
	IsMulticast *bool `json:"is_multicast,omitempty"` // 是否为组播网络
}

// Geolocation 描述地理位置信息，覆盖 GeoLite2-City/Country 和其他数据库
type Geolocation struct {
	Continent      Continent     `json:"continent"`       // 大陆信息
	Country        Country       `json:"country"`         // 国家信息
	Region         Region        `json:"region"`          // 行政区划（州/省）
	Subdivisions   []Subdivision `json:"subdivisions"`    // 多级行政区划（支持多层）
	City           string        `json:"city"`            // 城市名称
	PostalCode     string        `json:"postal_code"`     // 邮政编码
	Latitude       *float64      `json:"latitude"`        // 纬度（可选）
	Longitude      *float64      `json:"longitude"`       // 经度（可选）
	Elevation      *float64      `json:"elevation"`       // 海拔（可选，部分数据库提供）
	AccuracyRadius *int          `json:"accuracy_radius"` // 定位精度半径（公里，GeoLite2 提供）
	GeonameID      *int          `json:"geoname_id"`      // GeoNames ID（GeoLite2 提供）
}

// Continent 描述大陆信息
type Continent struct {
	Code string `json:"code"` // 大陆代码（如 "NA"）
	Name string `json:"name"` // 大陆名称（英文或其他语言）
}

// Country 描述国家信息
type Country struct {
	Code              string `json:"code"`                 // ISO 3166-1 代码（如 "US"）
	Name              string `json:"name"`                 // 国家名称
	IsInEuropeanUnion *bool  `json:"is_in_european_union"` // 是否欧盟成员国（GeoLite2 提供）
	GeonameID         *int   `json:"geoname_id"`           // GeoNames ID
}

// Region 描述一级行政区划（州/省）
type Region struct {
	Code      string `json:"code"`       // 行政区划代码（如 "CA"）
	Name      string `json:"name"`       // 行政区划名称
	GeonameID *int   `json:"geoname_id"` // GeoNames ID
}

// Subdivision 描述多级行政区划（支持州、县等）
type Subdivision struct {
	Code      string `json:"code"`       // 行政区划代码
	Name      string `json:"name"`       // 行政区划名称
	GeonameID *int   `json:"geoname_id"` // GeoNames ID
}

// Network 描述网络相关信息，覆盖 GeoLite2-ASN 和其他数据库
type Network struct {
	// 基础网络信息
	ISP                string `json:"isp"`                  // 互联网服务提供商
	ASN                string `json:"asn"`                  // 自治系统编号
	Organization       string `json:"organization"`         // 组织名称
	Domain             string `json:"domain"`               // 域名（部分数据库提供）
	AutonomousSystemID *int   `json:"autonomous_system_id"` // ASN ID（GeoLite2 提供）

	// 连接类型和使用类型 ⭐ 重要新增字段
	ConnectionType string `json:"connection_type"` // Cable/DSL, Cellular, Corporate, Satellite
	UsageType      string `json:"usage_type"`      // Commercial, Residential, Government, Military, University, Library

	// 移动网络信息
	MCC     string `json:"mcc"`     // 移动国家代码
	MNC     string `json:"mnc"`     // 移动网络代码
	Carrier string `json:"carrier"` // 运营商（IP2Location 提供）

	// 托管和云服务信息
	HostingProvider string `json:"hosting_provider"` // 托管提供商（IPinfo 提供）
	CloudProvider   string `json:"cloud_provider"`   // 云服务商 (AWS, GCP, Azure, etc.)

	// 网络特征标识 ⭐ 新增
	IsBusinessIP    *bool  `json:"is_business_ip"`    // 是否为商业IP
	IsResidentialIP *bool  `json:"is_residential_ip"` // 是否为住宅IP
	IsEducationIP   *bool  `json:"is_education_ip"`   // 是否为教育机构IP
	IsGovernmentIP  *bool  `json:"is_government_ip"`  // 是否为政府IP
	IsMobileIP      *bool  `json:"is_mobile_ip"`      // 是否为移动网络IP
	NetworkSpeed    string `json:"network_speed"`     // 网络速度等级 (Broadband, Dialup, etc.)
}

// Timezone 描述时区信息
type Timezone struct {
	Name   string `json:"name"`   // 时区名称（如 "America/Los_Angeles"）
	Offset string `json:"offset"` // 时区偏移（如 "-08:00"）
}

// Security 描述安全相关信息
type Security struct {
	// 代理检测
	IsProxy    *bool  `json:"is_proxy"`    // 是否为代理
	ProxyType  string `json:"proxy_type"`  // HTTP, SOCKS4, SOCKS5, Transparent, Elite
	ProxyLevel string `json:"proxy_level"` // Elite, Anonymous, Transparent

	// VPN检测
	IsVPN       *bool  `json:"is_vpn"`       // 是否为VPN
	VPNProvider string `json:"vpn_provider"` // VPN服务商名称

	// 匿名网络
	IsTor        *bool `json:"is_tor"`        // 是否为 Tor 节点
	IsAnonymizer *bool `json:"is_anonymizer"` // 是否为匿名化服务
	IsAnonymous  *bool `json:"is_anonymous"`  // 是否匿名

	// 数据中心和托管
	IsDataCenter *bool `json:"is_data_center"` // 是否为数据中心
	IsHosting    *bool `json:"is_hosting"`     // 是否为托管服务器
	IsCloudIP    *bool `json:"is_cloud_ip"`    // 是否为云服务IP

	// 威胁情报 ⭐ 重要扩展
	ThreatLevel string   `json:"threat_level"` // Low, Medium, High, Critical
	ThreatScore *int     `json:"threat_score"` // 威胁分数 0-100
	ThreatTypes []string `json:"threat_types"` // Malware, Phishing, Spam, Botnet, Scanner

	// 恶意行为检测 ⭐ 新增
	IsMalicious *bool   `json:"is_malicious"` // 是否为恶意IP
	IsBot       *bool   `json:"is_bot"`       // 是否为机器人
	IsSpammer   *bool   `json:"is_spammer"`   // 是否为垃圾邮件发送者
	IsAttacker  *bool   `json:"is_attacker"`  // 是否为攻击者
	IsScanner   *bool   `json:"is_scanner"`   // 是否为扫描器
	LastSeen    *string `json:"last_seen"`    // 最后发现时间 (RFC3339格式)

	// 高级安全特征 ⭐ 新增
	IsResidentialProxy *bool  `json:"is_residential_proxy"` // 住宅代理
	IsMobileProxy      *bool  `json:"is_mobile_proxy"`      // 移动代理
	ReputationScore    *int   `json:"reputation_score"`     // 信誉分数 0-100
	RiskLevel          string `json:"risk_level"`           // 风险等级 (Low, Medium, High, Critical)

	// 阻断列表信息
	IsBlacklisted    *bool    `json:"is_blacklisted"`    // 是否在黑名单中
	BlacklistSources []string `json:"blacklist_sources"` // 黑名单来源
}

// Metadata 描述元数据信息
type Metadata struct {
	Source        string  `json:"source"`         // 数据来源（如 "maxmind"、"ip2location"）
	LastUpdated   string  `json:"last_updated"`   // 最后更新时间（RFC3339 格式）
	Confidence    *int    `json:"confidence"`     // 可信度（0-100）
	SourceVersion string  `json:"source_version"` // 数据源版本（扩展字段）
	Accuracy      *string `json:"accuracy"`       // 数据精度（部分数据库提供）
}

// Extended 描述扩展字段，支持其他数据库和自定义数据
type Extended struct {
	// 经济和文化信息
	Currency    Currency `json:"currency"`     // 货币信息
	Languages   []string `json:"languages"`    // 语言列表
	CallingCode string   `json:"calling_code"` // 国家电话代码
	Flag        string   `json:"flag"`         // 国家旗帜（emoji 或 URL）

	// 人口统计信息
	Population *int `json:"population"` // 城市人口（DB-IP 提供）

	// 业务相关信息 ⭐ 新增
	WeatherCode   string `json:"weather_code"`   // 天气代码
	MarketSegment string `json:"market_segment"` // 市场细分

	// 自定义字段（使用any替代interface{}）
	CustomFields map[string]any `json:"custom_fields"` // 自定义字段（支持任意键值对）
}

// Currency 描述货币信息
type Currency struct {
	Code string `json:"code"` // 货币代码（如 "USD"）
	Name string `json:"name"` // 货币名称（如 "US Dollar"）
}

var (
	ErrIPNotFound         = errors.New("ip not found")
	ErrDataSourceNotFound = errors.New("data source not found")
)
