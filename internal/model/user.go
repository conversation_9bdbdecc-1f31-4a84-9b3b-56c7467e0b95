package model

import (
	"time"
)

// User 用户模型
type User struct {
	// 基础信息
	ID       int64   `json:"id" db:"id"`
	Username string  `json:"username" db:"username"`
	Email    *string `json:"email" db:"email"`

	// 密码相关（不在JSON中返回）
	PasswordHash string `json:"-" db:"password_hash"`

	// 用户状态和角色
	Role   string `json:"role" db:"role"`
	Status string `json:"status" db:"status"`

	// 用户信息
	FullName  *string `json:"full_name" db:"full_name"`
	AvatarURL *string `json:"avatar_url" db:"avatar_url"`
	Phone     *string `json:"phone" db:"phone"`

	// 验证状态
	EmailVerified bool `json:"email_verified" db:"email_verified"`
	PhoneVerified bool `json:"phone_verified" db:"phone_verified"`

	// 两步验证
	TwoFactorEnabled bool    `json:"two_factor_enabled" db:"two_factor_enabled"`
	TwoFactorSecret  *string `json:"-" db:"two_factor_secret"` // 不在JSON中返回

	// 登录相关
	LastLoginAt         *time.Time `json:"last_login_at" db:"last_login_at"`
	LastLoginIP         *string    `json:"last_login_ip" db:"last_login_ip"`
	LoginCount          int        `json:"login_count" db:"login_count"`
	FailedLoginAttempts int        `json:"failed_login_attempts" db:"failed_login_attempts"`
	LockedUntil         *time.Time `json:"locked_until" db:"locked_until"`

	// 密码相关
	PasswordChangedAt    *time.Time `json:"password_changed_at" db:"password_changed_at"`
	PasswordResetToken   *string    `json:"-" db:"password_reset_token"`   // 不在JSON中返回
	PasswordResetExpires *time.Time `json:"-" db:"password_reset_expires"` // 不在JSON中返回

	// API访问
	APIKey           *string    `json:"-" db:"api_key"` // 不在JSON中返回
	APIKeyCreatedAt  *time.Time `json:"api_key_created_at" db:"api_key_created_at"`
	APIKeyLastUsedAt *time.Time `json:"api_key_last_used_at" db:"api_key_last_used_at"`

	// 系统字段
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy *int64    `json:"created_by" db:"created_by"`
	UpdatedBy *int64    `json:"updated_by" db:"updated_by"`
}

// UserSession 用户会话模型
type UserSession struct {
	ID         int64     `json:"id" db:"id"`
	UserID     int64     `json:"user_id" db:"user_id"`
	TokenHash  string    `json:"-" db:"token_hash"` // 不在JSON中返回
	DeviceInfo string    `json:"device_info" db:"device_info"`
	IPAddress  string    `json:"ip_address" db:"ip_address"`
	UserAgent  string    `json:"user_agent" db:"user_agent"`
	ExpiresAt  time.Time `json:"expires_at" db:"expires_at"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	LastUsedAt time.Time `json:"last_used_at" db:"last_used_at"`
}

// UserPermission 用户权限模型
type UserPermission struct {
	ID         int64      `json:"id" db:"id"`
	UserID     int64      `json:"user_id" db:"user_id"`
	Permission string     `json:"permission" db:"permission"`
	Resource   string     `json:"resource" db:"resource"`
	GrantedAt  time.Time  `json:"granted_at" db:"granted_at"`
	GrantedBy  *int64     `json:"granted_by" db:"granted_by"`
	ExpiresAt  *time.Time `json:"expires_at" db:"expires_at"`
}

// Role 角色模型
type Role struct {
	ID           int64     `json:"id" db:"id"`
	Name         string    `json:"name" db:"name"`
	DisplayName  string    `json:"display_name" db:"display_name"`
	Description  string    `json:"description" db:"description"`
	Permissions  []string  `json:"permissions" db:"permissions"`
	IsSystemRole bool      `json:"is_system_role" db:"is_system_role"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// UserActivityLog 用户活动日志模型
type UserActivityLog struct {
	ID           int64     `json:"id" db:"id"`
	UserID       *int64    `json:"user_id" db:"user_id"`
	Action       string    `json:"action" db:"action"`
	Resource     string    `json:"resource" db:"resource"`
	ResourceID   string    `json:"resource_id" db:"resource_id"`
	Details      string    `json:"details" db:"details"`
	IPAddress    string    `json:"ip_address" db:"ip_address"`
	UserAgent    string    `json:"user_agent" db:"user_agent"`
	Success      bool      `json:"success" db:"success"`
	ErrorMessage string    `json:"error_message" db:"error_message"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Role     string `json:"role" binding:"required,oneof=admin operator user readonly"`
	FullName string `json:"full_name"`
	Phone    string `json:"phone"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email    string `json:"email" binding:"omitempty,email"`
	Role     string `json:"role" binding:"omitempty,oneof=admin operator user readonly"`
	Status   string `json:"status" binding:"omitempty,oneof=active inactive suspended"`
	FullName string `json:"full_name"`
	Phone    string `json:"phone"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ConfirmResetPasswordRequest 确认重置密码请求
type ConfirmResetPasswordRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []User `json:"users"`
	Total      int64  `json:"total"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
	TotalPages int    `json:"total_pages"`
}

// UserSummary 用户摘要信息
type UserSummary struct {
	ID              int64      `json:"id"`
	Username        string     `json:"username"`
	Email           string     `json:"email"`
	Role            string     `json:"role"`
	RoleDisplayName string     `json:"role_display_name"`
	Status          string     `json:"status"`
	FullName        string     `json:"full_name"`
	LastLoginAt     *time.Time `json:"last_login_at"`
	LoginCount      int        `json:"login_count"`
	CreatedAt       time.Time  `json:"created_at"`
	IsLocked        bool       `json:"is_locked"`
	HasAPIKey       bool       `json:"has_api_key"`
}

// UserActivitySummary 用户活动摘要
type UserActivitySummary struct {
	Username          string     `json:"username"`
	SuccessfulActions int        `json:"successful_actions"`
	FailedActions     int        `json:"failed_actions"`
	LastActivity      *time.Time `json:"last_activity"`
	ActiveDays        int        `json:"active_days"`
}

// 用户状态常量
const (
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"
	UserStatusPending   = "pending"
)

// 用户角色常量
const (
	RoleAdmin    = "admin"
	RoleOperator = "operator"
	RoleUser     = "user"
	RoleReadonly = "readonly"
)

// 权限常量
const (
	PermissionSystemRead      = "system:read"
	PermissionSystemWrite     = "system:write"
	PermissionSystemAdmin     = "system:admin"
	PermissionDatasourceRead  = "datasource:read"
	PermissionDatasourceWrite = "datasource:write"
	PermissionAPIRead         = "api:read"
	PermissionAPIWrite        = "api:write"
	PermissionIPQuery         = "ip:query"
	PermissionUserRead        = "user:read"
	PermissionUserWrite       = "user:write"
	PermissionUserAdmin       = "user:admin"
)

// IsLocked 检查用户是否被锁定
func (u *User) IsLocked() bool {
	return u.LockedUntil != nil && u.LockedUntil.After(time.Now())
}

// IsActive 检查用户是否处于活跃状态
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive && !u.IsLocked()
}

// HasRole 检查用户是否具有指定角色
func (u *User) HasRole(role string) bool {
	return u.Role == role
}

// IsAdmin 检查用户是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// CanLogin 检查用户是否可以登录
func (u *User) CanLogin() bool {
	return u.IsActive() && !u.IsLocked()
}

// SanitizeForResponse 清理用户数据用于响应（移除敏感信息）
func (u *User) SanitizeForResponse() *User {
	sanitized := *u
	sanitized.PasswordHash = ""
	sanitized.TwoFactorSecret = nil
	sanitized.PasswordResetToken = nil
	sanitized.APIKey = nil
	return &sanitized
}
