package monitoring

import (
	"context"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
)

type Monitor struct {
	logger *zap.Logger

	// 系统指标
	metrics struct {
		sync.RWMutex
		SystemMetrics   SystemMetrics   `json:"system_metrics"`
		ServiceMetrics  ServiceMetrics  `json:"service_metrics"`
		DatabaseMetrics DatabaseMetrics `json:"database_metrics"`
		CacheMetrics    CacheMetrics    `json:"cache_metrics"`
		APIMetrics      APIMetrics      `json:"api_metrics"`
		LastUpdate      time.Time       `json:"last_update"`
	}

	// 健康检查
	healthChecks map[string]HealthChecker

	// 配置
	config MonitorConfig

	// 启动时间
	startTime time.Time

	// CPU监控
	lastCPUTime  time.Time
	lastCPUUsage float64
}

type MonitorConfig struct {
	Enabled           bool            `json:"enabled"`
	UpdateInterval    time.Duration   `json:"update_interval"`
	RetentionPeriod   time.Duration   `json:"retention_period"`
	AlertThresholds   AlertThresholds `json:"alert_thresholds"`
	EnableHealthCheck bool            `json:"enable_health_check"`
}

type SystemMetrics struct {
	CPUUsage       float64       `json:"cpu_usage"`
	MemoryUsage    float64       `json:"memory_usage"`
	GoroutineCount int           `json:"goroutine_count"`
	HeapSize       uint64        `json:"heap_size"`
	HeapInUse      uint64        `json:"heap_in_use"`
	Uptime         time.Duration `json:"uptime"`
	Timestamp      time.Time     `json:"timestamp"`
}

type ServiceMetrics struct {
	TotalRequests     int64     `json:"total_requests"`
	SuccessRequests   int64     `json:"success_requests"`
	ErrorRequests     int64     `json:"error_requests"`
	AvgResponseTime   float64   `json:"avg_response_time"`
	RequestsPerSecond float64   `json:"requests_per_second"`
	ActiveConnections int       `json:"active_connections"`
	Timestamp         time.Time `json:"timestamp"`
}

type DatabaseMetrics struct {
	TotalQueries      int64     `json:"total_queries"`
	SuccessQueries    int64     `json:"success_queries"`
	ErrorQueries      int64     `json:"error_queries"`
	AvgQueryTime      float64   `json:"avg_query_time"`
	ActiveConnections int       `json:"active_connections"`
	PoolSize          int       `json:"pool_size"`
	Timestamp         time.Time `json:"timestamp"`
}

type CacheMetrics struct {
	TotalRequests int64     `json:"total_requests"`
	CacheHits     int64     `json:"cache_hits"`
	CacheMisses   int64     `json:"cache_misses"`
	HitRate       float64   `json:"hit_rate"`
	EvictionCount int64     `json:"eviction_count"`
	KeyCount      int64     `json:"key_count"`
	Timestamp     time.Time `json:"timestamp"`
}

type APIMetrics struct {
	EndpointStats map[string]EndpointStat `json:"endpoint_stats"`
	Timestamp     time.Time               `json:"timestamp"`
}

type EndpointStat struct {
	Path         string        `json:"path"`
	Method       string        `json:"method"`
	RequestCount int64         `json:"request_count"`
	ErrorCount   int64         `json:"error_count"`
	AvgDuration  time.Duration `json:"avg_duration"`
	LastAccess   time.Time     `json:"last_access"`
}

type AlertThresholds struct {
	CPUUsage     float64 `json:"cpu_usage"`
	MemoryUsage  float64 `json:"memory_usage"`
	ErrorRate    float64 `json:"error_rate"`
	ResponseTime float64 `json:"response_time"`
	CacheHitRate float64 `json:"cache_hit_rate"`
}

type HealthChecker interface {
	Name() string
	Check(ctx context.Context) error
}

type HealthStatus struct {
	Service   string        `json:"service"`
	Status    string        `json:"status"` // "healthy", "unhealthy", "unknown"
	Message   string        `json:"message,omitempty"`
	Timestamp time.Time     `json:"timestamp"`
	Duration  time.Duration `json:"duration"`
}

func NewMonitor(logger *zap.Logger) *Monitor {
	now := time.Now()
	monitor := &Monitor{
		logger:       logger,
		healthChecks: make(map[string]HealthChecker),
		startTime:    now,
		config: MonitorConfig{
			Enabled:         true,
			UpdateInterval:  30 * time.Second,
			RetentionPeriod: 24 * time.Hour,
			AlertThresholds: AlertThresholds{
				CPUUsage:     80.0,
				MemoryUsage:  85.0,
				ErrorRate:    5.0,
				ResponseTime: 1000.0, // ms
				CacheHitRate: 70.0,
			},
			EnableHealthCheck: true,
		},
	}

	// 初始化指标
	monitor.metrics.LastUpdate = now
	monitor.metrics.APIMetrics.EndpointStats = make(map[string]EndpointStat)

	// 启动监控
	if monitor.config.Enabled {
		go monitor.startMonitoring()
	}

	return monitor
}

// RegisterHealthChecker 注册健康检查器
func (m *Monitor) RegisterHealthChecker(checker HealthChecker) {
	m.healthChecks[checker.Name()] = checker
	m.logger.Info("Health checker registered", zap.String("name", checker.Name()))
}

// GetMetrics 获取所有指标
func (m *Monitor) GetMetrics() map[string]interface{} {
	m.metrics.RLock()
	defer m.metrics.RUnlock()

	return map[string]interface{}{
		"system":      m.metrics.SystemMetrics,
		"service":     m.metrics.ServiceMetrics,
		"database":    m.metrics.DatabaseMetrics,
		"cache":       m.metrics.CacheMetrics,
		"api":         m.metrics.APIMetrics,
		"last_update": m.metrics.LastUpdate,
	}
}

// GetHealthStatus 获取健康状态
func (m *Monitor) GetHealthStatus(ctx context.Context) map[string]HealthStatus {
	if !m.config.EnableHealthCheck {
		return map[string]HealthStatus{}
	}

	results := make(map[string]HealthStatus)
	var wg sync.WaitGroup
	var mu sync.Mutex

	for name, checker := range m.healthChecks {
		wg.Add(1)
		go func(name string, checker HealthChecker) {
			defer wg.Done()

			start := time.Now()
			status := HealthStatus{
				Service:   name,
				Timestamp: start,
			}

			// 设置超时
			checkCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
			defer cancel()

			if err := checker.Check(checkCtx); err != nil {
				status.Status = "unhealthy"
				status.Message = err.Error()
			} else {
				status.Status = "healthy"
			}

			status.Duration = time.Since(start)

			mu.Lock()
			results[name] = status
			mu.Unlock()
		}(name, checker)
	}

	wg.Wait()
	return results
}

// RecordAPIRequest 记录API请求
func (m *Monitor) RecordAPIRequest(method, path string, duration time.Duration, success bool) {
	if !m.config.Enabled {
		return
	}

	m.metrics.Lock()
	defer m.metrics.Unlock()

	// 更新服务指标
	m.metrics.ServiceMetrics.TotalRequests++
	if success {
		m.metrics.ServiceMetrics.SuccessRequests++
	} else {
		m.metrics.ServiceMetrics.ErrorRequests++
	}

	// 更新平均响应时间
	if m.metrics.ServiceMetrics.TotalRequests > 0 {
		totalTime := m.metrics.ServiceMetrics.AvgResponseTime * float64(m.metrics.ServiceMetrics.TotalRequests-1)
		m.metrics.ServiceMetrics.AvgResponseTime = (totalTime + duration.Seconds()*1000) / float64(m.metrics.ServiceMetrics.TotalRequests)
	}

	// 更新API端点统计
	key := method + " " + path
	stat := m.metrics.APIMetrics.EndpointStats[key]
	stat.Path = path
	stat.Method = method
	stat.RequestCount++
	if !success {
		stat.ErrorCount++
	}

	// 更新平均持续时间
	if stat.RequestCount > 0 {
		totalDuration := stat.AvgDuration * time.Duration(stat.RequestCount-1)
		stat.AvgDuration = (totalDuration + duration) / time.Duration(stat.RequestCount)
	}
	stat.LastAccess = time.Now()

	m.metrics.APIMetrics.EndpointStats[key] = stat
	m.metrics.ServiceMetrics.Timestamp = time.Now()
	m.metrics.APIMetrics.Timestamp = time.Now()
}

// RecordDatabaseQuery 记录数据库查询
func (m *Monitor) RecordDatabaseQuery(duration time.Duration, success bool) {
	if !m.config.Enabled {
		return
	}

	m.metrics.Lock()
	defer m.metrics.Unlock()

	m.metrics.DatabaseMetrics.TotalQueries++
	if success {
		m.metrics.DatabaseMetrics.SuccessQueries++
	} else {
		m.metrics.DatabaseMetrics.ErrorQueries++
	}

	// 更新平均查询时间
	if m.metrics.DatabaseMetrics.TotalQueries > 0 {
		totalTime := m.metrics.DatabaseMetrics.AvgQueryTime * float64(m.metrics.DatabaseMetrics.TotalQueries-1)
		m.metrics.DatabaseMetrics.AvgQueryTime = (totalTime + duration.Seconds()*1000) / float64(m.metrics.DatabaseMetrics.TotalQueries)
	}

	m.metrics.DatabaseMetrics.Timestamp = time.Now()
}

// RecordCacheOperation 记录缓存操作
func (m *Monitor) RecordCacheOperation(hit bool) {
	if !m.config.Enabled {
		return
	}

	m.metrics.Lock()
	defer m.metrics.Unlock()

	m.metrics.CacheMetrics.TotalRequests++
	if hit {
		m.metrics.CacheMetrics.CacheHits++
	} else {
		m.metrics.CacheMetrics.CacheMisses++
	}

	// 计算命中率
	if m.metrics.CacheMetrics.TotalRequests > 0 {
		m.metrics.CacheMetrics.HitRate = float64(m.metrics.CacheMetrics.CacheHits) / float64(m.metrics.CacheMetrics.TotalRequests) * 100
	}

	m.metrics.CacheMetrics.Timestamp = time.Now()
}

// startMonitoring 启动监控
func (m *Monitor) startMonitoring() {
	// 立即执行一次系统指标更新
	m.updateSystemMetrics()

	ticker := time.NewTicker(m.config.UpdateInterval)
	defer ticker.Stop()

	m.logger.Info("Monitor started",
		zap.Duration("update_interval", m.config.UpdateInterval),
		zap.Bool("enabled", m.config.Enabled))

	for {
		select {
		case <-ticker.C:
			m.updateSystemMetrics()
			m.checkAlerts()
		}
	}
}

// updateSystemMetrics 更新系统指标
func (m *Monitor) updateSystemMetrics() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	now := time.Now()
	uptime := now.Sub(m.startTime)

	m.metrics.Lock()
	m.metrics.SystemMetrics = SystemMetrics{
		CPUUsage:       m.getCPUUsage(), // 添加CPU使用率计算
		GoroutineCount: runtime.NumGoroutine(),
		HeapSize:       memStats.HeapSys,
		HeapInUse:      memStats.HeapInuse,
		MemoryUsage:    float64(memStats.HeapInuse) / float64(memStats.HeapSys) * 100,
		Uptime:         uptime,
		Timestamp:      now,
	}
	m.metrics.LastUpdate = now
	m.metrics.Unlock()
}

// getCPUUsage 获取CPU使用率（简化实现）
func (m *Monitor) getCPUUsage() float64 {
	// 简化的CPU使用率计算
	// 在生产环境中，可以使用更精确的方法，如读取/proc/stat
	now := time.Now()
	if m.lastCPUTime.IsZero() {
		m.lastCPUTime = now
		m.lastCPUUsage = 0.0
		return 0.0
	}

	// 基于goroutine数量的简单估算
	goroutines := runtime.NumGoroutine()
	cpuUsage := float64(goroutines) / 100.0 // 简化计算
	if cpuUsage > 100.0 {
		cpuUsage = 100.0
	}

	m.lastCPUTime = now
	m.lastCPUUsage = cpuUsage
	return cpuUsage
}

// checkAlerts 检查告警
func (m *Monitor) checkAlerts() {
	m.metrics.RLock()
	defer m.metrics.RUnlock()

	// 检查CPU使用率
	if m.metrics.SystemMetrics.CPUUsage > m.config.AlertThresholds.CPUUsage {
		m.logger.Warn("High CPU usage detected",
			zap.Float64("usage", m.metrics.SystemMetrics.CPUUsage),
			zap.Float64("threshold", m.config.AlertThresholds.CPUUsage))
	}

	// 检查内存使用率
	if m.metrics.SystemMetrics.MemoryUsage > m.config.AlertThresholds.MemoryUsage {
		m.logger.Warn("High memory usage detected",
			zap.Float64("usage", m.metrics.SystemMetrics.MemoryUsage),
			zap.Float64("threshold", m.config.AlertThresholds.MemoryUsage))
	}

	// 检查错误率
	if m.metrics.ServiceMetrics.TotalRequests > 0 {
		errorRate := float64(m.metrics.ServiceMetrics.ErrorRequests) / float64(m.metrics.ServiceMetrics.TotalRequests) * 100
		if errorRate > m.config.AlertThresholds.ErrorRate {
			m.logger.Warn("High error rate detected",
				zap.Float64("error_rate", errorRate),
				zap.Float64("threshold", m.config.AlertThresholds.ErrorRate))
		}
	}

	// 检查响应时间
	if m.metrics.ServiceMetrics.AvgResponseTime > m.config.AlertThresholds.ResponseTime {
		m.logger.Warn("High response time detected",
			zap.Float64("avg_response_time", m.metrics.ServiceMetrics.AvgResponseTime),
			zap.Float64("threshold", m.config.AlertThresholds.ResponseTime))
	}

	// 检查缓存命中率
	if m.metrics.CacheMetrics.HitRate < m.config.AlertThresholds.CacheHitRate && m.metrics.CacheMetrics.TotalRequests > 100 {
		m.logger.Warn("Low cache hit rate detected",
			zap.Float64("hit_rate", m.metrics.CacheMetrics.HitRate),
			zap.Float64("threshold", m.config.AlertThresholds.CacheHitRate))
	}
}

// GetSummary 获取监控摘要
func (m *Monitor) GetSummary() map[string]interface{} {
	m.metrics.RLock()
	defer m.metrics.RUnlock()

	summary := map[string]interface{}{
		"status": "healthy",
		"uptime": time.Since(m.metrics.LastUpdate),
		"system": map[string]interface{}{
			"goroutines":   m.metrics.SystemMetrics.GoroutineCount,
			"memory_usage": m.metrics.SystemMetrics.MemoryUsage,
			"heap_size":    m.metrics.SystemMetrics.HeapSize,
		},
		"service": map[string]interface{}{
			"total_requests":    m.metrics.ServiceMetrics.TotalRequests,
			"success_rate":      m.calculateSuccessRate(),
			"avg_response_time": m.metrics.ServiceMetrics.AvgResponseTime,
		},
		"cache": map[string]interface{}{
			"hit_rate":       m.metrics.CacheMetrics.HitRate,
			"total_requests": m.metrics.CacheMetrics.TotalRequests,
		},
		"database": map[string]interface{}{
			"total_queries":  m.metrics.DatabaseMetrics.TotalQueries,
			"avg_query_time": m.metrics.DatabaseMetrics.AvgQueryTime,
		},
	}

	return summary
}

// calculateSuccessRate 计算成功率
func (m *Monitor) calculateSuccessRate() float64 {
	if m.metrics.ServiceMetrics.TotalRequests == 0 {
		return 100.0
	}
	return float64(m.metrics.ServiceMetrics.SuccessRequests) / float64(m.metrics.ServiceMetrics.TotalRequests) * 100
}

// DatabaseHealthChecker 数据库健康检查器
type DatabaseHealthChecker struct {
	db interface {
		Ping(ctx context.Context) error
	}
}

func NewDatabaseHealthChecker(db interface {
	Ping(ctx context.Context) error
}) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{db: db}
}

func (d *DatabaseHealthChecker) Name() string {
	return "database"
}

func (d *DatabaseHealthChecker) Check(ctx context.Context) error {
	return d.db.Ping(ctx)
}

// CacheHealthChecker 缓存健康检查器
type CacheHealthChecker struct {
	cache interface {
		Ping(ctx context.Context) error
	}
}

func NewCacheHealthChecker(cache interface {
	Ping(ctx context.Context) error
}) *CacheHealthChecker {
	return &CacheHealthChecker{cache: cache}
}

func (c *CacheHealthChecker) Name() string {
	return "cache"
}

func (c *CacheHealthChecker) Check(ctx context.Context) error {
	return c.cache.Ping(ctx)
}
