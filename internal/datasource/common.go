package datasource

import (
	"fmt"
	"net"
	"time"

	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"github.com/oschwald/maxminddb-golang"
)

func ParseMMDB(filePath string) ([]model.IPInfo, error) {
	db, err := maxminddb.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	var ipInfos []model.IPInfo
	networks := db.Networks(maxminddb.SkipAliasedNetworks)

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0

	// 定义 MMDB 记录结构体，覆盖 GeoLite2-Country/City/ASN
	record := struct {
		Continent struct {
			Code  string `maxminddb:"code"`
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
		} `maxminddb:"continent"`
		Country struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			IsInEuropeanUnion bool `maxminddb:"is_in_european_union"`
			GeonameID         int  `maxminddb:"geoname_id"`
		} `maxminddb:"country"`
		Subdivisions []struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			GeonameID int `maxminddb:"geoname_id"`
		} `maxminddb:"subdivisions"`
		City struct {
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			GeonameID int `maxminddb:"geoname_id"`
		} `maxminddb:"city"`
		Postal struct {
			Code string `maxminddb:"code"`
		} `maxminddb:"postal"`
		Location struct {
			Latitude       float64 `maxminddb:"latitude"`
			Longitude      float64 `maxminddb:"longitude"`
			TimeZone       string  `maxminddb:"time_zone"`
			AccuracyRadius int     `maxminddb:"accuracy_radius"`
		} `maxminddb:"location"`
		AutonomousSystemNumber       int    `maxminddb:"autonomous_system_number"`
		AutonomousSystemOrganization string `maxminddb:"autonomous_system_organization"`
		Traits                       struct {
			IsAnonymousProxy    bool `maxminddb:"is_anonymous_proxy"`
			IsSatelliteProvider bool `maxminddb:"is_satellite_provider"`
		} `maxminddb:"traits"`
	}{}

	for networks.Next() {
		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			break
		}
		utils.ShowProcess(processedCount, nil)
		_inNet, err := networks.Network(&record)
		if err != nil {
			continue
		}
		// 构造 IPInfo
		_isProxy := record.Traits.IsAnonymousProxy || record.Traits.IsSatelliteProvider
		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      _inNet.String(),
				IPVersion: GetIPVersion(_inNet),
				StartIP:   _inNet.IP.String(),
				EndIP:     GetEndIP(_inNet),
				Netmask:   net.IP(_inNet.Mask).String(),
			},
			Geolocation: model.Geolocation{
				Continent: model.Continent{
					Code: record.Continent.Code,
					Name: record.Continent.Names.En,
				},
				Country: model.Country{
					Code:              record.Country.ISOCode,
					Name:              record.Country.Names.En,
					IsInEuropeanUnion: PtrBool(record.Country.IsInEuropeanUnion),
					GeonameID:         PtrInt(record.Country.GeonameID),
				},
				Region: model.Region{
					Code:      GetFirstSubdivisionCode(record.Subdivisions),
					Name:      GetFirstSubdivisionName(record.Subdivisions),
					GeonameID: GetFirstSubdivisionGeonameID(record.Subdivisions),
				},
				Subdivisions:   getSubdivisions(record.Subdivisions),
				City:           record.City.Names.En,
				PostalCode:     record.Postal.Code,
				Latitude:       PtrFloat64(record.Location.Latitude),
				Longitude:      PtrFloat64(record.Location.Longitude),
				AccuracyRadius: PtrInt(record.Location.AccuracyRadius),
				GeonameID:      PtrInt(record.City.GeonameID),
			},
			Network: model.Network{
				ASN:                fmt.Sprintf("%d", record.AutonomousSystemNumber),
				Organization:       record.AutonomousSystemOrganization,
				AutonomousSystemID: PtrInt(record.AutonomousSystemNumber),
			},
			Timezone: model.Timezone{
				Name: record.Location.TimeZone,
				// Offset 可通过外部库（如 time.LoadLocation）计算
			},
			Security: model.Security{
				IsProxy:     &_isProxy,
				IsAnonymous: PtrBool(record.Traits.IsAnonymousProxy),
			},
			Metadata: model.Metadata{
				Source:      "maxmind",
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  PtrInt(85),
			},
			Extended: model.Extended{
				CustomFields: make(map[string]interface{}),
			},
		}

		ipInfos = append(ipInfos, ipInfo)
		processedCount++
	}

	return ipInfos, networks.Err()
}

func PtrInt(i int) *int {
	if i == 0 {
		return nil
	}
	return &i
}

func PtrFloat64(f float64) *float64 {
	if f == 0 {
		return nil
	}
	return &f
}

func PtrBool(b bool) *bool {
	return &b
}

func GetFirstSubdivisionCode(subdivisions []struct {
	ISOCode string `maxminddb:"iso_code"`
	Names   struct {
		En string `maxminddb:"en"`
	} `maxminddb:"names"`
	GeonameID int `maxminddb:"geoname_id"`
}) string {
	if len(subdivisions) > 0 {
		return subdivisions[0].ISOCode
	}
	return ""
}

func GetFirstSubdivisionName(subdivisions []struct {
	ISOCode string `maxminddb:"iso_code"`
	Names   struct {
		En string `maxminddb:"en"`
	} `maxminddb:"names"`
	GeonameID int `maxminddb:"geoname_id"`
}) string {
	if len(subdivisions) > 0 {
		return subdivisions[0].Names.En
	}
	return ""
}

func GetFirstSubdivisionGeonameID(subdivisions []struct {
	ISOCode string `maxminddb:"iso_code"`
	Names   struct {
		En string `maxminddb:"en"`
	} `maxminddb:"names"`
	GeonameID int `maxminddb:"geoname_id"`
}) *int {
	if len(subdivisions) > 0 {
		return PtrInt(subdivisions[0].GeonameID)
	}
	return nil
}

func getSubdivisions(subdivisions []struct {
	ISOCode string `maxminddb:"iso_code"`
	Names   struct {
		En string `maxminddb:"en"`
	} `maxminddb:"names"`
	GeonameID int `maxminddb:"geoname_id"`
}) []model.Subdivision {
	var subs []model.Subdivision
	for _, sub := range subdivisions {
		subs = append(subs, model.Subdivision{
			Code:      sub.ISOCode,
			Name:      sub.Names.En,
			GeonameID: PtrInt(sub.GeonameID),
		})
	}
	return subs
}

// 辅助函数
func GetIPVersion(net *net.IPNet) string {
	if len(net.IP) == 4 {
		return "IPv4"
	}
	return "IPv6"
}

func GetEndIP(_net *net.IPNet) string {
	ip := _net.IP.To4()
	if ip == nil {
		ip = _net.IP.To16()
	}
	mask := _net.Mask
	endIP := make(net.IP, len(ip))
	copy(endIP, ip)
	for i := 0; i < len(ip); i++ {
		endIP[i] |= ^mask[i]
	}
	return endIP.String()
}
