package datasource

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"go.uber.org/zap"
)

// ValidationResult 验证结果
type ValidationResult struct {
	Source      string    `json:"source"`
	Valid       bool      `json:"valid"`
	Error       string    `json:"error,omitempty"`
	Details     string    `json:"details,omitempty"`
	CheckedAt   time.Time `json:"checked_at"`
	DataFiles   []string  `json:"data_files,omitempty"`
	RecordCount int       `json:"record_count,omitempty"`
}

// DatasourceValidator 数据源验证器
type DatasourceValidator struct {
	logger *zap.Logger
	config *config.Config
}

// NewDatasourceValidator 创建新的数据源验证器
func NewDatasourceValidator(logger *zap.Logger, cfg *config.Config) *DatasourceValidator {
	return &DatasourceValidator{
		logger: logger,
		config: cfg,
	}
}

// ValidateAllDatasources 验证所有数据源配置
func (v *DatasourceValidator) ValidateAllDatasources(ctx context.Context) ([]ValidationResult, error) {
	var results []ValidationResult

	// 获取所有启用的数据源
	datasourceConfigs := v.getEnabledDatasources()

	for sourceName, dsConfig := range datasourceConfigs {
		// 跳过 qqwry 和 iplocate 验证（根据用户要求）
		if sourceName == "qqwry" || sourceName == "iplocate" {
			results = append(results, ValidationResult{
				Source:    sourceName,
				Valid:     true,
				Details:   "Skipped validation as requested",
				CheckedAt: time.Now(),
			})
			continue
		}

		result := v.validateSingleDatasource(ctx, sourceName, dsConfig)
		results = append(results, result)
	}

	return results, nil
}

// ValidateSpecificDatasources 验证指定的数据源
func (v *DatasourceValidator) ValidateSpecificDatasources(ctx context.Context, sourceNames []string) ([]ValidationResult, error) {
	var results []ValidationResult
	datasourceConfigs := v.getEnabledDatasources()

	for _, sourceName := range sourceNames {
		// 跳过 qqwry 和 iplocate 验证（根据用户要求）
		if sourceName == "qqwry" || sourceName == "iplocate" {
			results = append(results, ValidationResult{
				Source:    sourceName,
				Valid:     true,
				Details:   "Skipped validation as requested",
				CheckedAt: time.Now(),
			})
			continue
		}

		dsConfig, exists := datasourceConfigs[sourceName]
		if !exists {
			results = append(results, ValidationResult{
				Source:    sourceName,
				Valid:     false,
				Error:     "Datasource not found in configuration",
				CheckedAt: time.Now(),
			})
			continue
		}

		result := v.validateSingleDatasource(ctx, sourceName, dsConfig)
		results = append(results, result)
	}

	return results, nil
}

// validateSingleDatasource 验证单个数据源
func (v *DatasourceValidator) validateSingleDatasource(_ context.Context, sourceName string, dsConfig config.DatasourceConfig) ValidationResult {
	result := ValidationResult{
		Source:    sourceName,
		CheckedAt: time.Now(),
	}

	v.logger.Info("Validating datasource", zap.String("source", sourceName))

	// 1. 检查配置有效性
	if err := v.validateConfiguration(sourceName, dsConfig); err != nil {
		result.Valid = false
		result.Error = fmt.Sprintf("Configuration validation failed: %v", err)
		return result
	}

	// 2. 检查数据文件是否存在
	dataFiles, err := v.checkDataFiles(sourceName)
	if err != nil {
		result.Valid = false
		result.Error = fmt.Sprintf("Data file check failed: %v", err)
		return result
	}
	result.DataFiles = dataFiles

	// 3. 检查数据文件格式（简化版本，不实际解析）
	if len(dataFiles) > 0 {
		err := v.validateDataFileFormats(dataFiles, sourceName)
		if err != nil {
			result.Valid = false
			result.Error = fmt.Sprintf("Data file format validation failed: %v", err)
			return result
		}
		result.Details = fmt.Sprintf("Found %d valid data files", len(dataFiles))
	} else {
		result.Details = "No data files found, but configuration is valid"
	}

	result.Valid = true
	v.logger.Info("Datasource validation completed",
		zap.String("source", sourceName),
		zap.Bool("valid", result.Valid),
		zap.Int("record_count", result.RecordCount))

	return result
}

// validateConfiguration 验证数据源配置
func (v *DatasourceValidator) validateConfiguration(sourceName string, dsConfig config.DatasourceConfig) error {
	// 检查是否启用
	if !dsConfig.Enabled {
		return fmt.Errorf("datasource is disabled")
	}

	// 检查 URL 配置
	if len(dsConfig.URL) == 0 {
		return fmt.Errorf("no URLs configured")
	}

	// 检查特定数据源的配置要求
	switch sourceName {
	case "maxmind":
		return v.validateMaxMindConfig()
	case "ip2location":
		return v.validateIP2LocationConfig()
	default:
		// 对于其他数据源，基本的 URL 检查就足够了
		return nil
	}
}

// validateMaxMindConfig 验证 MaxMind 配置
func (v *DatasourceValidator) validateMaxMindConfig() error {
	id := os.Getenv("maxmind_id")
	key := os.Getenv("maxmind_license_key")

	if strings.TrimSpace(id) == "" {
		return fmt.Errorf("maxmind_id environment variable is not set or empty")
	}

	if strings.TrimSpace(key) == "" {
		return fmt.Errorf("maxmind_license_key environment variable is not set or empty")
	}

	return nil
}

// validateIP2LocationConfig 验证 IP2Location 配置
func (v *DatasourceValidator) validateIP2LocationConfig() error {
	key := os.Getenv("ip2location_key")

	if strings.TrimSpace(key) == "" {
		return fmt.Errorf("ip2location_key environment variable is not set or empty")
	}

	return nil
}

// validateDataFileFormats 验证数据文件格式
func (v *DatasourceValidator) validateDataFileFormats(dataFiles []string, sourceName string) error {
	for _, filePath := range dataFiles {
		// 检查文件是否可读
		file, err := os.Open(filePath)
		if err != nil {
			return fmt.Errorf("cannot open file %s: %v", filePath, err)
		}
		file.Close()

		// 根据数据源类型检查文件格式
		switch sourceName {
		case "maxmind", "dbip":
			if !strings.HasSuffix(strings.ToLower(filePath), ".mmdb") {
				return fmt.Errorf("invalid file format for %s: %s (expected .mmdb)", sourceName, filePath)
			}
		case "ip2location":
			if !strings.HasSuffix(strings.ToLower(filePath), ".csv") &&
				!strings.HasSuffix(strings.ToLower(filePath), ".bin") {
				return fmt.Errorf("invalid file format for %s: %s (expected .csv or .bin)", sourceName, filePath)
			}
		case "ipapi":
			if !strings.HasSuffix(strings.ToLower(filePath), ".csv") &&
				!strings.HasSuffix(strings.ToLower(filePath), ".zip") {
				return fmt.Errorf("invalid file format for %s: %s (expected .csv or .zip)", sourceName, filePath)
			}
		}

		// 检查文件大小（不能为空）
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			return fmt.Errorf("cannot stat file %s: %v", filePath, err)
		}
		if fileInfo.Size() == 0 {
			return fmt.Errorf("file %s is empty", filePath)
		}
	}

	return nil
}

// checkDataFiles 检查数据文件是否存在
func (v *DatasourceValidator) checkDataFiles(sourceName string) ([]string, error) {
	var dataDir string
	var extensions []string

	switch sourceName {
	case "maxmind":
		dataDir = "./data/maxmind"
		extensions = []string{".mmdb"}
	case "ip2location":
		dataDir = "./data/ip2location"
		extensions = []string{".csv", ".bin"}
	case "dbip":
		dataDir = "./data/dbip"
		extensions = []string{".mmdb"}
	case "ipapi":
		dataDir = "./data/ipapi"
		extensions = []string{".csv", ".zip"}
	default:
		return nil, fmt.Errorf("unknown datasource: %s", sourceName)
	}

	var foundFiles []string

	// 检查数据目录是否存在
	if _, err := os.Stat(dataDir); os.IsNotExist(err) {
		return foundFiles, nil // 目录不存在，返回空列表但不报错
	}

	// 遍历目录查找数据文件
	err := filepath.Walk(dataDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			for _, ext := range extensions {
				if strings.HasSuffix(strings.ToLower(info.Name()), ext) {
					foundFiles = append(foundFiles, path)
					break
				}
			}
		}

		return nil
	})

	return foundFiles, err
}

// getEnabledDatasources 获取所有启用的数据源配置
func (v *DatasourceValidator) getEnabledDatasources() map[string]config.DatasourceConfig {
	datasources := make(map[string]config.DatasourceConfig)

	if v.config.Datasources.Maxmind.Enabled {
		datasources["maxmind"] = v.config.Datasources.Maxmind
	}
	if v.config.Datasources.IP2Location.Enabled {
		datasources["ip2location"] = v.config.Datasources.IP2Location
	}
	if v.config.Datasources.DBIP.Enabled {
		datasources["dbip"] = v.config.Datasources.DBIP
	}
	if v.config.Datasources.IPAPI.Enabled {
		datasources["ipapi"] = v.config.Datasources.IPAPI
	}
	if v.config.Datasources.IPLocate.Enabled {
		datasources["iplocate"] = v.config.Datasources.IPLocate
	}
	if v.config.Datasources.QQWry.Enabled {
		datasources["qqwry"] = v.config.Datasources.QQWry
	}

	return datasources
}
