package ipgeolocation

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

type IPGeolocation struct {
	config      config.APIConfig
	logger      *zap.Logger
	client      *http.Client
	rateLimiter *rate.Limiter
}

func NewIPGeolocation(config config.APIConfig, logger *zap.Logger) *IPGeolocation {
	limiter := rate.NewLimiter(rate.Limit(float64(config.RateLimit)/24/3600), config.RateLimit)
	return &IPGeolocation{
		config:      config,
		logger:      logger,
		client:      &http.Client{},
		rateLimiter: limiter,
	}
}

func (i *IPGeolocation) Name() string {
	return "ipgeolocation"
}

func (i *IPGeolocation) Fetch(ctx context.Context) error {
	return nil
}

func (i *IPGeolocation) Parse(ctx context.Context) ([]model.IPInfo, error) {
	return nil, nil
}

func (i *IPGeolocation) QueryIP(ctx context.Context, ip string) (*model.IPInfo, error) {
	if err := i.rateLimiter.Wait(ctx); err != nil {
		i.logger.Warn("IPGeolocation rate limit exceeded", zap.String("ip", ip))
		return nil, fmt.Errorf("rate limit exceeded")
	}

	url := fmt.Sprintf("%s/ipgeo?apiKey=%s&ip=%s", i.config.URL, i.config.APIKey, ip)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := i.client.Do(req)
	if err != nil {
		i.logger.Error("Failed to query IPGeolocation", zap.Error(err))
		return nil, err
	}
	defer resp.Body.Close()

	var result struct {
		IP          string `json:"ip"`
		CountryCode string `json:"country_code2"`
		CountryName string `json:"country_name"`
		StateProv   string `json:"state_prov"`
		City        string `json:"city"`
		Zipcode     string `json:"zipcode"`
		Latitude    string `json:"latitude"`
		Longitude   string `json:"longitude"`
		Timezone    string `json:"time_zone"`
		Currency    struct {
			Code string `json:"code"`
			Name string `json:"name"`
		} `json:"currency"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	latitude, _ := strconv.ParseFloat(result.Latitude, 64)
	longitude, _ := strconv.ParseFloat(result.Longitude, 64)

	ipInfo := &model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      ip + "/32",
			IPVersion: "IPv4",
		},
		Geolocation: model.Geolocation{
			Country: model.Country{
				Code: result.CountryCode,
				Name: result.CountryName,
			},
			Region: model.Region{
				Name: result.StateProv,
			},
			City:       result.City,
			PostalCode: result.Zipcode,
			Latitude:   &latitude,
			Longitude:  &longitude,
		},
		Timezone: model.Timezone{
			Name:   result.Timezone,
			Offset: "-08:00",
		},
		Extended: model.Extended{
			Currency: model.Currency{
				Code: result.Currency.Code,
				Name: result.Currency.Name,
			},
		},
		Metadata: model.Metadata{
			Source:      "ipgeolocation",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(75),
		},
	}
	return ipInfo, nil
}

func ptrInt(i int) *int {
	return &i
}
