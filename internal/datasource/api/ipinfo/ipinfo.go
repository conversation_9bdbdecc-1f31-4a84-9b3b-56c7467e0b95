package ipinfo

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

type IPInfo struct {
	config      config.APIConfig
	logger      *zap.Logger
	client      *http.Client
	rateLimiter *rate.Limiter
}

func NewIPInfo(config config.APIConfig, logger *zap.Logger) *IPInfo {
	// 每日限额 1000 次，均匀分布
	limiter := rate.NewLimiter(rate.Limit(float64(config.RateLimit)/24/3600), config.RateLimit)
	return &IPInfo{
		config:      config,
		logger:      logger,
		client:      &http.Client{},
		rateLimiter: limiter,
	}
}

func (i *IPInfo) Name() string {
	return "ipinfo"
}

func (i *IPInfo) Fetch(ctx context.Context) error {
	// API 不需批量下载
	return nil
}

func (i *IPInfo) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// API 不支持批量解析
	return nil, nil
}

func (i *IPInfo) QueryIP(ctx context.Context, ip string) (*model.IPInfo, error) {
	if err := i.rateLimiter.Wait(ctx); err != nil {
		i.logger.Warn("ipinfo rate limit exceeded", zap.String("ip", ip))
		return nil, fmt.Errorf("rate limit exceeded")
	}

	var url string
	if i.config.APIKey != "" {
		url = fmt.Sprintf("%s/%s?token=%s", i.config.URL[0], ip, i.config.APIKey)
	} else {
		url = fmt.Sprintf("%s/%s", i.config.URL[0], ip)
	}
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := i.client.Do(req)
	if err != nil {
		i.logger.Error("Failed to query ipinfo", zap.Error(err))
		return nil, err
	}
	defer resp.Body.Close()

	var result struct {
		IP       string `json:"ip"`
		City     string `json:"city"`
		Region   string `json:"region"`
		Country  string `json:"country"`
		Loc      string `json:"loc"`
		Org      string `json:"org"`
		Timezone string `json:"timezone"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	// 解析经纬度
	var lat, lon float64
	if result.Loc != "" {
		coords := strings.Split(result.Loc, ",")
		if len(coords) == 2 {
			lat, _ = strconv.ParseFloat(coords[0], 64)
			lon, _ = strconv.ParseFloat(coords[1], 64)
		}
	}

	// 生成CIDR和确定IP版本
	var cidr, ipVersion string
	if strings.Contains(ip, ":") {
		// IPv6
		cidr = ip + "/128"
		ipVersion = "IPv6"
	} else {
		// IPv4
		cidr = ip + "/32"
		ipVersion = "IPv4"
	}

	ipInfo := &model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      cidr,
			IPVersion: ipVersion,
		},
		Geolocation: model.Geolocation{
			Country: model.Country{
				Code: result.Country,
			},
			Region: model.Region{
				Name: result.Region,
			},
			City:      result.City,
			Latitude:  &lat,
			Longitude: &lon,
		},
		Network: model.Network{
			Organization: result.Org,
		},
		Timezone: model.Timezone{
			Name: result.Timezone,
		},
		Metadata: model.Metadata{
			Source:      "ipinfo",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(75),
		},
	}
	return ipInfo, nil
}

func (i *IPInfo) Update(ctx context.Context, data []model.IPInfo) error {
	return nil
}

func ptrInt(i int) *int {
	return &i
}
