package ipstack

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

func TestIPStackQueryIP(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte(`{
            "ip": "*******",
            "country_code": "AU",
            "country_name": "Australia",
            "region_name": "Queensland",
            "city": "Brisbane",
            "zip": "4000",
            "latitude": -27.4705,
            "longitude": 153.0260
        }`))
	}))
	defer server.Close()

	ipstack := &IPStack{
		config: config.APIConfig{
			URL:       []string{server.URL},
			APIKey:    "test",
			RateLimit: 100,
		},
		logger:      logger,
		client:      server.Client(),
		rateLimiter: rate.NewLimiter(rate.Inf, 100),
	}

	ipInfo, err := ipstack.QueryIP(context.Background(), "*******")
	assert.NoError(t, err)
	assert.NotNil(t, ipInfo)
	assert.Equal(t, "*******/32", ipInfo.IPRange.CIDR)
	assert.Equal(t, "AU", ipInfo.Geolocation.Country.Code)
	assert.Equal(t, "Australia", ipInfo.Geolocation.Country.Name)
	assert.Equal(t, "Queensland", ipInfo.Geolocation.Region.Name)
	assert.Equal(t, "Brisbane", ipInfo.Geolocation.City)
	assert.Equal(t, "4000", ipInfo.Geolocation.PostalCode)
	assert.Equal(t, -27.4705, ipInfo.Geolocation.Latitude)
	assert.Equal(t, 153.0260, ipInfo.Geolocation.Longitude)
}
