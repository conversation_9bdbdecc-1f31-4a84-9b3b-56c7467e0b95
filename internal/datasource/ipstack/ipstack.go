package ipstack

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

type IPStack struct {
	config      config.APIConfig
	logger      *zap.Logger
	client      *http.Client
	rateLimiter *rate.Limiter
}

func NewIPStack(config config.APIConfig, logger *zap.Logger) *IPStack {
	limiter := rate.NewLimiter(rate.Limit(float64(config.RateLimit)/24/3600), config.RateLimit)
	return &IPStack{
		config:      config,
		logger:      logger,
		client:      &http.Client{},
		rateLimiter: limiter,
	}
}

func (i *IPStack) Name() string {
	return "ipstack"
}

func (i *IPStack) Fetch(ctx context.Context) error {
	return nil
}

func (i *IPStack) Parse(ctx context.Context) ([]model.IPInfo, error) {
	return nil, nil
}

func (i *IPStack) QueryIP(ctx context.Context, ip string) (*model.IPInfo, error) {
	if err := i.rateLimiter.Wait(ctx); err != nil {
		i.logger.Warn("ipstack rate limit exceeded", zap.String("ip", ip))
		return nil, fmt.Errorf("rate limit exceeded")
	}

	url := fmt.Sprintf("%s/%s?access_key=%s", i.config.URL, ip, i.config.APIKey)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := i.client.Do(req)
	if err != nil {
		i.logger.Error("Failed to query ipstack", zap.Error(err))
		return nil, err
	}
	defer resp.Body.Close()

	var result struct {
		IP          string  `json:"ip"`
		CountryCode string  `json:"country_code"`
		CountryName string  `json:"country_name"`
		RegionName  string  `json:"region_name"`
		City        string  `json:"city"`
		Zip         string  `json:"zip"`
		Latitude    float64 `json:"latitude"`
		Longitude   float64 `json:"longitude"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	ipInfo := &model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      ip + "/32",
			IPVersion: "IPv4",
		},
		Geolocation: model.Geolocation{
			Country: model.Country{
				Code: result.CountryCode,
				Name: result.CountryName,
			},
			Region: model.Region{
				Name: result.RegionName,
			},
			City:       result.City,
			PostalCode: result.Zip,
			Latitude:   &result.Latitude,
			Longitude:  &result.Longitude,
		},
		Metadata: model.Metadata{
			Source:      "ipstack",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(75),
		},
	}
	return ipInfo, nil
}

func ptrInt(i int) *int {
	return &i
}
