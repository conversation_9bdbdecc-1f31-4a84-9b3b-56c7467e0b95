package datasource

import (
	"context"

	"github.com/cosin2077/ipInsight/internal/model"
)

type Datasource interface {
	// Name 返回数据源名称
	Name() string
	// Fetch 下载或获取数据
	Fetch(ctx context.Context) error
	// Parse 解析数据为 IPInfo 列表
	Parse(ctx context.Context) ([]model.IPInfo, error)
	// Update 更新数据库
	Update(ctx context.Context, data []model.IPInfo) error
}

// ForceDownloadable 支持强制下载的数据源接口
type ForceDownloadable interface {
	// SetForceDownload 设置强制下载标志
	SetForceDownload(force bool)
}
