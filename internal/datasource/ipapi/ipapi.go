package ipapi

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"go.uber.org/zap"
)

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

type IPAPI struct {
	config    config.DatasourceConfig
	logger    *zap.Logger
	dataDir   string
	force     bool                       // 强制下载标志
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

func NewIPAPI(config config.DatasourceConfig, logger *zap.Logger, dbAdapter database.DatabaseInterface) *IPAPI {
	return &IPAPI{
		config:    config,
		logger:    logger,
		dataDir:   "./data/ipapi",
		force:     false,
		dbAdapter: dbAdapter,
	}
}

// SetForceDownload 设置强制下载标志
func (i *IPAPI) SetForceDownload(force bool) {
	i.force = force
}

func (i *IPAPI) Name() string {
	return "ipapi"
}

func (i *IPAPI) Fetch(ctx context.Context) error {
	if err := os.MkdirAll(i.dataDir, 0755); err != nil {
		i.logger.Error("Failed to create data directory", zap.Error(err))
		return err
	}

	// 处理多个URL
	for idx, url := range i.config.URL {
		// 根据URL basename生成文件名
		fileName := filepath.Base(url)
		if fileName == "" || fileName == "." || fileName == "/" {
			// 如果无法从URL获取文件名，使用默认命名
			fileName = fmt.Sprintf("ipapi_%d.zip", idx)
		}

		// 确保文件扩展名正确
		if !strings.HasSuffix(strings.ToLower(fileName), ".zip") {
			fileName += ".zip"
		}

		zipFilePath := filepath.Join(i.dataDir, fileName)

		// 检查是否需要下载（支持强制下载）
		downloadCheckOptions := utils.DefaultNoNeedDownloadOptions()
		downloadCheckOptions.Force = i.force

		ok, err := utils.NoNeedDownloadWithOptions(zipFilePath, i.logger, downloadCheckOptions)
		if err != nil {
			i.logger.Warn("Failed to check download necessity",
				zap.String("url", url),
				zap.String("file", fileName),
				zap.Error(err))
			continue
		}
		if ok {
			i.logger.Info("Skipping IPAPI file download - file is fresh",
				zap.String("url", url),
				zap.String("path", zipFilePath))
			continue
		}

		// 下载文件
		err = i.downloadSingleFile(ctx, url, zipFilePath)
		if err != nil {
			i.logger.Error("Failed to download IPAPI file",
				zap.String("url", url),
				zap.String("file", fileName),
				zap.Error(err))
			continue
		}

		i.logger.Info("IPAPI file downloaded successfully",
			zap.String("url", url),
			zap.String("file", fileName))

		// 解压文件
		err = i.extractZipFile(zipFilePath)
		if err != nil {
			i.logger.Error("Failed to extract IPAPI file",
				zap.String("file", fileName),
				zap.Error(err))
			continue
		}
	}

	return nil
}

func (i *IPAPI) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (i *IPAPI) ParseAndUpdate(ctx context.Context) error {
	// 查找所有CSV文件
	csvFiles, err := i.findCSVFiles()
	if err != nil {
		return fmt.Errorf("failed to find CSV files: %w", err)
	}

	if len(csvFiles) == 0 {
		return fmt.Errorf("no CSV files found in %s", i.dataDir)
	}

	// 使用共享的数据库适配器
	dbAdapter := i.dbAdapter

	// 流式处理每个CSV文件
	for _, csvFile := range csvFiles {
		i.logger.Info("Processing CSV file with streaming", zap.String("file", csvFile))

		err := i.parseCSVFileStream(ctx, csvFile, dbAdapter)
		if err != nil {
			i.logger.Error("Failed to process CSV file with streaming",
				zap.String("file", csvFile),
				zap.Error(err))
			return fmt.Errorf("failed to process CSV file %s: %w", csvFile, err)
		}

		i.logger.Info("Successfully processed CSV file with streaming",
			zap.String("file", csvFile))
	}

	return nil
}

func (i *IPAPI) Update(ctx context.Context, data []model.IPInfo) error {
	// 使用共享的数据库适配器
	_, err := i.dbAdapter.BatchUpsertIPs(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	i.logger.Info("Successfully updated IPAPI data",
		zap.Int("records", len(data)),
		zap.String("source", "ipapi"))

	return nil
}

func ptrInt(i int) *int {
	return &i
}

// extractZipFile 解压 ZIP 文件并查找 CSV 文件
func (i *IPAPI) extractZipFile(zipFilePath string) error {
	// 使用 utils 包中的 ExtractZip 函数
	if err := i.callExtractZip(zipFilePath, i.dataDir, i.logger); err != nil {
		i.logger.Error("Failed to extract ZIP file", zap.String("file", zipFilePath), zap.Error(err))
		return fmt.Errorf("extract ZIP file: %w", err)
	}

	// 查找解压后的 CSV 文件
	csvFiles, err := i.findCSVFiles()
	if err != nil {
		return fmt.Errorf("find CSV files: %w", err)
	}

	if len(csvFiles) == 0 {
		return fmt.Errorf("no CSV files found in extracted ZIP")
	}

	i.logger.Info("IPAPI ZIP file extracted successfully",
		zap.Strings("csv_files", csvFiles),
		zap.Int("total_csv_files", len(csvFiles)))

	// 不清理 ZIP 文件，解压了多个 csv 文件，靠 zip 检查 fresh
	// if err := os.Remove(zipFilePath); err != nil {
	// 	i.logger.Warn("Failed to remove ZIP file", zap.String("file", zipFilePath), zap.Error(err))
	// }

	return nil
}

// findCSVFiles 在数据目录中查找 CSV 文件
func (i *IPAPI) findCSVFiles() ([]string, error) {
	var csvFiles []string

	err := filepath.Walk(i.dataDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(strings.ToLower(info.Name()), ".csv") {
			csvFiles = append(csvFiles, path)
		}

		return nil
	})

	return csvFiles, err
}

// parseIPv4Record 解析IPv4记录
func (i *IPAPI) parseIPv4Record(record []string) (model.IPInfo, error) {
	// 格式: ip_version,start_ip,end_ip,continent,country_code,country,state,city,zip,timezone,latitude,longitude,accuracy,source
	startIP := strings.TrimSpace(record[1])
	endIP := strings.TrimSpace(record[2])

	// 验证IP地址有效性
	if startIP == "" || startIP == "-" {
		return model.IPInfo{}, fmt.Errorf("invalid start IP: %s", startIP)
	}
	if endIP == "" || endIP == "-" {
		return model.IPInfo{}, fmt.Errorf("invalid end IP: %s", endIP)
	}

	// 验证IP地址格式
	if net.ParseIP(startIP) == nil {
		return model.IPInfo{}, fmt.Errorf("invalid start IP format: %s", startIP)
	}
	if net.ParseIP(endIP) == nil {
		return model.IPInfo{}, fmt.Errorf("invalid end IP format: %s", endIP)
	}

	continent := record[3]
	countryCode := record[4]
	country := record[5]
	state := record[6]
	city := record[7]
	zipCode := record[8]
	timezone := record[9]
	latitude, _ := strconv.ParseFloat(record[10], 64)
	longitude, _ := strconv.ParseFloat(record[11], 64)
	accuracy, _ := strconv.Atoi(record[12])
	source := record[13]

	// 转换IP范围为CIDR
	cidrs, err := datasource.RangeToCIDR(startIP, endIP)
	if err != nil {
		return model.IPInfo{}, fmt.Errorf("convert IP range to CIDR: %w", err)
	}

	// 使用第一个CIDR（如果有多个，可以考虑分别处理）
	var cidr string
	if len(cidrs) > 0 {
		cidr = cidrs[0]
	} else {
		return model.IPInfo{}, fmt.Errorf("no CIDR generated from IP range")
	}

	return model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      cidr,
			StartIP:   startIP,
			EndIP:     endIP,
			IPVersion: "IPv4",
		},
		Geolocation: model.Geolocation{
			Continent: model.Continent{
				Name: continent,
			},
			Country: model.Country{
				Code: countryCode,
				Name: country,
			},
			Region: model.Region{
				Name: state,
			},
			City:           city,
			PostalCode:     zipCode,
			Latitude:       &latitude,
			Longitude:      &longitude,
			AccuracyRadius: &accuracy,
		},
		Timezone: model.Timezone{
			Name: timezone,
		},
		Metadata: model.Metadata{
			Source:      "ipapi",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(70),
			Accuracy:    &source,
		},
	}, nil
}

// parseIPv6Record 解析IPv6记录
func (i *IPAPI) parseIPv6Record(record []string) (model.IPInfo, error) {
	// 格式与IPv4相同: ip_version,start_ip,end_ip,continent,country_code,country,state,city,zip,timezone,latitude,longitude,accuracy,source
	startIP := strings.TrimSpace(record[1])
	endIP := strings.TrimSpace(record[2])

	// 验证IP地址有效性
	if startIP == "" || startIP == "-" {
		return model.IPInfo{}, fmt.Errorf("invalid start IP: %s", startIP)
	}
	if endIP == "" || endIP == "-" {
		return model.IPInfo{}, fmt.Errorf("invalid end IP: %s", endIP)
	}

	// 验证IP地址格式
	if net.ParseIP(startIP) == nil {
		return model.IPInfo{}, fmt.Errorf("invalid start IP format: %s", startIP)
	}
	if net.ParseIP(endIP) == nil {
		return model.IPInfo{}, fmt.Errorf("invalid end IP format: %s", endIP)
	}

	continent := record[3]
	countryCode := record[4]
	country := record[5]
	state := record[6]
	city := record[7]
	zipCode := record[8]
	timezone := record[9]
	latitude, _ := strconv.ParseFloat(record[10], 64)
	longitude, _ := strconv.ParseFloat(record[11], 64)
	accuracy, _ := strconv.Atoi(record[12])
	source := record[13]

	// 转换IP范围为CIDR
	cidrs, err := datasource.RangeToCIDR(startIP, endIP)
	if err != nil {
		return model.IPInfo{}, fmt.Errorf("convert IP range to CIDR: %w", err)
	}

	// 使用第一个CIDR
	var cidr string
	if len(cidrs) > 0 {
		cidr = cidrs[0]
	} else {
		return model.IPInfo{}, fmt.Errorf("no CIDR generated from IP range")
	}

	return model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      cidr,
			StartIP:   startIP,
			EndIP:     endIP,
			IPVersion: "IPv6",
		},
		Geolocation: model.Geolocation{
			Continent: model.Continent{
				Name: continent,
			},
			Country: model.Country{
				Code: countryCode,
				Name: country,
			},
			Region: model.Region{
				Name: state,
			},
			City:           city,
			PostalCode:     zipCode,
			Latitude:       &latitude,
			Longitude:      &longitude,
			AccuracyRadius: &accuracy,
		},
		Timezone: model.Timezone{
			Name: timezone,
		},
		Metadata: model.Metadata{
			Source:      "ipapi",
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  ptrInt(70),
			Accuracy:    &source,
		},
	}, nil
}

// downloadSingleFile 下载单个文件
func (i *IPAPI) downloadSingleFile(ctx context.Context, url, filePath string) error {
	// 创建临时文件
	tempFile, err := os.CreateTemp(i.dataDir, "ipapi-*.tmp")
	if err != nil {
		return fmt.Errorf("create temp file: %w", err)
	}
	defer tempFile.Close()
	defer os.Remove(tempFile.Name())

	// 使用改进的下载功能
	downloadOptions := utils.DefaultDownloadOptions()
	downloadOptions.ShowProgress = true
	downloadOptions.CheckRemoteSize = true
	downloadOptions.SkipIfExists = false

	err = utils.DownloadWithRetry(ctx, url, tempFile, i.logger, nil, downloadOptions)
	if err != nil {
		return fmt.Errorf("download failed: %w", err)
	}

	// 移动临时文件到最终位置
	if err := os.Rename(tempFile.Name(), filePath); err != nil {
		return fmt.Errorf("move temp file: %w", err)
	}

	return nil
}

// callExtractZip 调用 utils 包中的 ExtractZip 函数
func (i *IPAPI) callExtractZip(src, destDir string, logger *zap.Logger) error {
	return utils.ExtractZip(src, destDir, logger)
}

// parseCSVFileStream 流式解析CSV文件并直接写入数据库
func (i *IPAPI) parseCSVFileStream(ctx context.Context, csvFile string, dbAdapter database.DatabaseInterface) error {
	file, err := os.Open(csvFile)
	if err != nil {
		return fmt.Errorf("open file %s: %w", csvFile, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.LazyQuotes = true
	reader.TrimLeadingSpace = true
	reader.FieldsPerRecord = -1

	// 批量处理设置
	batchSize := 1000
	batch := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	isIPv6 := strings.Contains(strings.ToLower(csvFile), "ipv6")

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			continue
		}

		// 跳过标题行
		if processedCount == 0 && (record[0] == "ip_version" || record[0] == "start_ip") {
			processedCount++ // 标题行也算作已处理的一行
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			i.logger.Info("Debug mode: processing limited",
				zap.String("file", csvFile),
				zap.Int("processed", processedCount),
				zap.Int("debug_limit", debugLimit))
			break
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		// 根据用户提供的格式，应该有14列
		if len(record) < 14 {
			i.logger.Warn("Invalid record length",
				zap.String("file", csvFile),
				zap.Int("expected", 14),
				zap.Int("got", len(record)))
			continue
		}

		var ipInfo model.IPInfo
		if isIPv6 {
			// IPv6格式处理
			ipInfo, err = i.parseIPv6Record(record)
		} else {
			// IPv4格式处理
			ipInfo, err = i.parseIPv4Record(record)
		}

		if err != nil {
			i.logger.Warn("Failed to parse record",
				zap.String("file", csvFile),
				zap.Error(err))
			continue
		}

		batch = append(batch, ipInfo)
		processedCount++

		// 当批次满了，写入数据库
		if len(batch) >= batchSize {
			_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
			if err != nil {
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			i.logger.Info("Wrote IPAPI batch to database",
				zap.Int("batch_number", totalBatches),
				zap.Int("batch_size", len(batch)),
				zap.Int("total_processed", processedCount))

			// 重置批次，释放内存
			batch = make([]model.IPInfo, 0, batchSize)
		}

		// 每处理1万条记录报告一次进度
		if processedCount%10000 == 0 {
			i.logger.Info("IPAPI parsing progress",
				zap.String("file", csvFile),
				zap.Int("processed", processedCount))
		}
	}

	// 处理最后一批数据
	utils.SaveInfoToJSON(batch, "ipapi_csv")
	if len(batch) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
		if err != nil {
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		i.logger.Info("Wrote final IPAPI batch to database",
			zap.Int("batch_number", totalBatches),
			zap.Int("batch_size", len(batch)),
			zap.Int("total_processed", processedCount))
	}

	i.logger.Info("Completed IPAPI streaming processing",
		zap.String("file", csvFile),
		zap.Int("total_records", processedCount),
		zap.Int("total_batches", totalBatches))

	return nil
}
