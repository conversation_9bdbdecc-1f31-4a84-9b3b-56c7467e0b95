package dbip

import (
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"go.uber.org/zap"
)

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

type DBIP struct {
	config    config.DatasourceConfig
	logger    *zap.Logger
	dataDir   string
	force     bool // 强制下载标志
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

func NewDBIP(config config.DatasourceConfig, logger *zap.Logger, dbAdapter database.DatabaseInterface) *DBIP {
	return &DBIP{
		config:    config,
		logger:    logger,
		dataDir:   "./data/dbip",
		force:     false,
		dbAdapter: dbAdapter,
	}
}

// SetForceDownload 设置强制下载标志
func (d *DBIP) SetForceDownload(force bool) {
	d.force = force
}

func (d *DBIP) Name() string {
	return "dbip"
}

func (d *DBIP) Fetch(ctx context.Context) error {
	if err := os.MkdirAll(d.dataDir, 0755); err != nil {
		d.logger.Error("Failed to create data directory", zap.Error(err))
		return fmt.Errorf("create data directory: %w", err)
	}

	// 数据库文件配置
	databases := []struct {
		urlTemplate string
		filename    string
		desc        string
	}{
		{"https://download.db-ip.com/free/dbip-country-lite-%s.mmdb.gz", "dbip-country-lite.mmdb.gz", "Country database"},
		{"https://download.db-ip.com/free/dbip-city-lite-%s.mmdb.gz", "dbip-city-lite.mmdb.gz", "City database"},
	}

	var wg sync.WaitGroup
	for _, db := range databases {
		wg.Add(1)
		go func(database struct {
			urlTemplate string
			filename    string
			desc        string
		}) {
			defer wg.Done()

			// 尝试当前月份和前几个月的数据
			for monthOffset := 0; monthOffset < 3; monthOffset++ {
				targetDate := time.Now().AddDate(0, -monthOffset, 0).Format("2006-01")
				downloadURL := fmt.Sprintf(database.urlTemplate, targetDate)

				d.logger.Info("Attempting to download DB-IP database",
					zap.String("url", downloadURL),
					zap.String("description", database.desc),
					zap.String("date", targetDate))

				err := d.fetchSingle(ctx, downloadURL, database.filename)
				if err == nil {
					d.logger.Info("Successfully downloaded DB-IP database",
						zap.String("filename", database.filename),
						zap.String("date", targetDate))
					break
				} else {
					d.logger.Warn("Failed to download DB-IP database for date",
						zap.String("date", targetDate),
						zap.Error(err))

					// 如果是最后一次尝试，记录错误
					if monthOffset == 2 {
						d.logger.Error("Failed to download DB-IP database after trying multiple dates",
							zap.String("filename", database.filename),
							zap.Error(err))
					}
				}
			}
		}(db)
	}
	wg.Wait()
	return nil
}

// fetchSingle 下载单个文件
func (d *DBIP) fetchSingle(ctx context.Context, downloadURL, filename string) error {
	filePath := filepath.Join(d.dataDir, filename)

	// 对于压缩文件，检查解压后的文件是否存在且新鲜
	var checkPath string
	if strings.HasSuffix(filename, ".gz") {
		// 检查解压后的文件
		checkPath = strings.TrimSuffix(filePath, ".gz")
	} else {
		// 检查原文件
		checkPath = filePath
	}

	// 检查是否需要下载（支持强制下载）
	downloadCheckOptions := utils.DefaultNoNeedDownloadOptions()
	downloadCheckOptions.Force = d.force

	ok, err := utils.NoNeedDownloadWithOptions(checkPath, d.logger, downloadCheckOptions)
	if err != nil {
		return fmt.Errorf("check download necessity: %w", err)
	}
	if ok {
		d.logger.Info("Skipping DB-IP database download - file is fresh",
			zap.String("check_path", checkPath),
			zap.String("filename", filename))
		return nil
	}

	// 创建临时文件
	tempFile, err := os.CreateTemp(d.dataDir, filename+".tmp")
	if err != nil {
		return fmt.Errorf("create temp file: %w", err)
	}
	defer tempFile.Close()
	defer os.Remove(tempFile.Name())

	// 使用改进的下载功能
	downloadOptions := utils.DefaultDownloadOptions()
	downloadOptions.ShowProgress = true
	downloadOptions.CheckRemoteSize = true
	downloadOptions.SkipIfExists = false // 我们已经在上面检查过了

	err = utils.DownloadWithRetry(ctx, downloadURL, tempFile, d.logger, nil, downloadOptions)
	if err != nil {
		return fmt.Errorf("download failed: %w", err)
	}

	// 移动临时文件到最终位置
	if err := os.Rename(tempFile.Name(), filePath); err != nil {
		return fmt.Errorf("move temp file: %w", err)
	}

	// 如果是压缩文件，解压缩
	if strings.HasSuffix(filename, ".gz") {
		err = d.extractGzFile(filePath)
		if err != nil {
			return fmt.Errorf("extract gz file: %w", err)
		}
	}

	return nil
}

// extractGzFile 解压缩 .gz 文件
func (d *DBIP) extractGzFile(gzFilePath string) error {
	// 打开压缩文件
	gzFile, err := os.Open(gzFilePath)
	if err != nil {
		return fmt.Errorf("open gz file: %w", err)
	}
	defer gzFile.Close()

	// 创建 gzip reader
	gzReader, err := gzip.NewReader(gzFile)
	if err != nil {
		return fmt.Errorf("create gzip reader: %w", err)
	}
	defer gzReader.Close()

	// 创建输出文件（去掉 .gz 扩展名）
	outputPath := strings.TrimSuffix(gzFilePath, ".gz")
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("create output file: %w", err)
	}
	defer outputFile.Close()

	// 解压缩
	_, err = io.Copy(outputFile, gzReader)
	if err != nil {
		return fmt.Errorf("decompress file: %w", err)
	}

	d.logger.Info("Successfully extracted gz file",
		zap.String("source", gzFilePath),
		zap.String("target", outputPath))

	// 删除压缩文件
	os.Remove(gzFilePath)

	return nil
}

func (d *DBIP) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (d *DBIP) ParseAndUpdate(ctx context.Context) error {
	// 查找所有MMDB文件
	mmdbFiles, err := utils.ReadExtFiles(d.dataDir, ".mmdb")
	if err != nil {
		return fmt.Errorf("failed to read MMDB files: %w", err)
	}

	if len(mmdbFiles) == 0 {
		return fmt.Errorf("no MMDB files found in %s", d.dataDir)
	}

	// 使用共享的数据库适配器
	dbAdapter := d.dbAdapter

	// 流式处理每个MMDB文件
	for _, mmdbFile := range mmdbFiles {
		d.logger.Info("Processing MMDB file with streaming", zap.String("file", mmdbFile))

		_, err := os.Stat(mmdbFile)
		if err != nil {
			d.logger.Warn("MMDB file not found", zap.String("file", mmdbFile), zap.Error(err))
			continue
		}

		fileName := filepath.Base(mmdbFile)

		// 根据文件名判断MMDB类型并调用相应的流式解析函数
		switch {
		case strings.Contains(fileName, "country"):
			d.logger.Info("Streaming Country MMDB file", zap.String("file", fileName))
			err = datasource.ParseCountryMMDBStream(ctx, mmdbFile, "dbip", dbAdapter, d.logger)

		case strings.Contains(fileName, "city"):
			d.logger.Info("Streaming City MMDB file", zap.String("file", fileName))
			err = datasource.ParseCityMMDBStream(ctx, mmdbFile, "dbip", dbAdapter, d.logger)

		default:
			d.logger.Warn("Unknown MMDB file type, skipping", zap.String("file", fileName))
			continue
		}

		if err != nil {
			d.logger.Error("Failed to process MMDB file with streaming",
				zap.String("file", fileName),
				zap.Error(err))
			return fmt.Errorf("failed to process MMDB file %s: %w", fileName, err)
		}

		d.logger.Info("Successfully processed MMDB file with streaming",
			zap.String("file", fileName))
	}

	return nil
}

func (d *DBIP) Update(ctx context.Context, data []model.IPInfo) error {
	// 使用共享的数据库适配器
	_, err := d.dbAdapter.BatchUpsertIPs(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	d.logger.Info("Successfully updated DBIP data",
		zap.Int("records", len(data)),
		zap.String("source", "dbip"))

	return nil
}
