package iplocate

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestIPLocateParse(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	dataDir := "./testdata"
	os.Mkdir<PERSON>ll(dataDir, 0755)
	defer os.RemoveAll(dataDir)

	csvContent := `16777216,16777471,"AU"`
	csvFile := filepath.Join(dataDir, "iplocate.csv")
	os.WriteFile(csvFile, []byte(csvContent), 0644)

	iplocate := &IPLocate{
		config:  config.DatasourceConfig{URL: []string{}, Schedule: ""},
		logger:  logger,
		dataDir: dataDir,
	}

	ipInfos, err := iplocate.Parse(context.Background())
	assert.NoError(t, err)
	assert.Len(t, ipInfos, 1)

	ipInfo := ipInfos[0]
	assert.Equal(t, "*******", ipInfo.IPRange.StartIP)
	assert.Equal(t, "*********", ipInfo.IPRange.EndIP)
	assert.Equal(t, "AU", ipInfo.Geolocation.Country.Code)
	assert.Equal(t, "iplocate", ipInfo.Metadata.Source)
}
