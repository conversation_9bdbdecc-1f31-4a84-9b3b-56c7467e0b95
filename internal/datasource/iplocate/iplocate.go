package iplocate

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"go.uber.org/zap"
)

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

type IPLocate struct {
	config    config.DatasourceConfig
	logger    *zap.Logger
	dataDir   string
	force     bool                       // 强制下载标志
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

func NewIPLocate(config config.DatasourceConfig, logger *zap.Logger, dbAdapter database.DatabaseInterface) *IPLocate {
	return &IPLocate{
		config:    config,
		logger:    logger,
		dataDir:   "./data/iplocate",
		force:     false,
		dbAdapter: dbAdapter,
	}
}

// SetForceDownload 设置强制下载标志
func (i *IPLocate) SetForceDownload(force bool) {
	i.force = force
}

func (i *IPLocate) Name() string {
	return "iplocate"
}

func (i *IPLocate) Fetch(ctx context.Context) error {
	if err := os.MkdirAll(i.dataDir, 0755); err != nil {
		i.logger.Error("Failed to create data directory", zap.Error(err))
		return err
	}

	// 定义文件映射：URL索引 -> 文件名
	fileMap := map[int]string{
		0: "iplocate.csv",       // CSV文件
		1: "ip-to-asn.mmdb",     // ASN MMDB文件
		2: "ip-to-country.mmdb", // 国家MMDB文件
	}

	// 根据URL内容智能判断文件类型
	getFileName := func(url string) string {
		if strings.Contains(url, "ip-to-asn.mmdb") {
			return "ip-to-asn.mmdb"
		} else if strings.Contains(url, "ip-to-country.mmdb") {
			return "ip-to-country.mmdb"
		} else if strings.Contains(url, ".csv") {
			return "iplocate.csv"
		}
		return fileMap[0] // 默认返回CSV
	}

	// 下载所有配置的文件
	for idx, url := range i.config.URL {
		fileName := getFileName(url)
		if fileName == "" {
			i.logger.Warn("Unknown URL type", zap.Int("index", idx), zap.String("url", url))
			continue
		}

		filePath := filepath.Join(i.dataDir, fileName)

		// 检查是否需要下载（支持强制下载）
		downloadCheckOptions := utils.DefaultNoNeedDownloadOptions()
		downloadCheckOptions.Force = i.force

		ok, err := utils.NoNeedDownloadWithOptions(filePath, i.logger, downloadCheckOptions)
		if err != nil {
			i.logger.Error("Failed to check download necessity", zap.String("file", filePath), zap.Error(err))
			continue
		}
		if ok {
			i.logger.Info("Skipping IPLocate file download - file is fresh",
				zap.String("path", filePath),
				zap.String("filename", fileName))
			continue
		}

		i.logger.Info("Downloading file", zap.String("url", url), zap.String("file", fileName))

		resp, err := http.Get(url)
		if err != nil {
			i.logger.Error("Failed to fetch data", zap.String("url", url), zap.Error(err))
			continue // 继续下载其他文件，不因为一个文件失败而停止
		}

		out, err := os.Create(filePath)
		if err != nil {
			resp.Body.Close()
			i.logger.Error("Failed to create file", zap.String("file", filePath), zap.Error(err))
			continue
		}

		_, err = io.Copy(out, resp.Body)
		out.Close()
		resp.Body.Close()

		if err != nil {
			i.logger.Error("Failed to save file", zap.String("file", filePath), zap.Error(err))
			continue
		}

		i.logger.Info("Successfully downloaded", zap.String("file", fileName))
	}

	return nil
}

func (i *IPLocate) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (i *IPLocate) ParseAndUpdate(ctx context.Context) error {
	// 使用共享的数据库适配器
	dbAdapter := i.dbAdapter

	// 1. 解析CSV文件（如果存在）
	csvFile := filepath.Join(i.dataDir, "iplocate.csv")
	if _, err := os.Stat(csvFile); err == nil {
		i.logger.Info("Processing CSV file with streaming", zap.String("file", csvFile))
		csvInfos, err := i.parseCSV(csvFile)
		if err != nil {
			i.logger.Error("Failed to parse CSV", zap.Error(err))
		} else if len(csvInfos) > 0 {
			// 分批处理CSV数据
			batchSize := 1000
			for i := 0; i < len(csvInfos); i += batchSize {
				end := i + batchSize
				if end > len(csvInfos) {
					end = len(csvInfos)
				}
				batch := csvInfos[i:end]
				utils.SaveInfoToJSON(batch, "iplocation_csv")
				_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
				if err != nil {
					return fmt.Errorf("failed to write CSV batch to database: %w", err)
				}
			}
			i.logger.Info("Successfully processed CSV file", zap.Int("records", len(csvInfos)))
		}
	}

	// 2. 解析ASN MMDB文件（如果存在）
	asnFile := filepath.Join(i.dataDir, "ip-to-asn.mmdb")
	if _, err := os.Stat(asnFile); err == nil {
		i.logger.Info("Processing ASN MMDB file with streaming", zap.String("file", asnFile))
		// ASN文件通常较小，使用传统方法
		asnInfos, err := datasource.ParseASNMMDB(asnFile, "iplocate")
		if err != nil {
			i.logger.Error("Failed to parse ASN MMDB", zap.Error(err))
		} else if len(asnInfos) > 0 {
			utils.SaveInfoToJSON(asnInfos, "iplocation_asn_csv")
			_, err := dbAdapter.BatchUpsertIPs(ctx, asnInfos)
			if err != nil {
				return fmt.Errorf("failed to write ASN batch to database: %w", err)
			}
			i.logger.Info("Successfully processed ASN MMDB file", zap.Int("records", len(asnInfos)))
		}
	}

	// 3. 解析国家MMDB文件（如果存在）
	countryFile := filepath.Join(i.dataDir, "ip-to-country.mmdb")
	if _, err := os.Stat(countryFile); err == nil {
		i.logger.Info("Processing Country MMDB file with streaming", zap.String("file", countryFile))
		err := datasource.ParseCountryMMDBStream(ctx, countryFile, "iplocate", dbAdapter, i.logger)
		if err != nil {
			return fmt.Errorf("failed to process Country MMDB file: %w", err)
		}
		i.logger.Info("Successfully processed Country MMDB file")
	}

	return nil
}

// parseCSV 流式解析CSV文件
func (i *IPLocate) parseCSV(csvFile string) ([]model.IPInfo, error) {
	file, err := os.Open(csvFile)
	if err != nil {
		return nil, fmt.Errorf("parse iplocate %s: %w", csvFile, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	// 设置CSV读取选项以处理格式问题
	reader.LazyQuotes = true       // 允许懒惰引号处理
	reader.TrimLeadingSpace = true // 去除前导空格
	reader.FieldsPerRecord = -1    // 允许可变字段数量

	var ipInfos []model.IPInfo

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			// 如果CSV解析失败，记录警告但继续处理
			i.logger.Warn("CSV row parsing failed, skipping row",
				zap.String("file", csvFile),
				zap.Error(err))
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			i.logger.Info("Debug mode: processing limited",
				zap.String("file", csvFile),
				zap.Int("processed", processedCount),
				zap.Int("debug_limit", debugLimit))
			break
		}
		utils.ShowProcess(processedCount, nil)
		if len(record) < 3 {
			i.logger.Warn("Invalid record length", zap.String("datasource", i.Name()), zap.String("file", csvFile), zap.Int("len", len(record)))
			continue
		}
		startIP, err1 := strconv.ParseInt(record[0], 10, 64)
		endIP, err2 := strconv.ParseInt(record[1], 10, 64)
		if err1 != nil || err2 != nil {
			i.logger.Warn("Invalid IP int", zap.String("datasource", i.Name()), zap.String("file", csvFile), zap.Int64("startIP", startIP), zap.Int64("endIP", endIP), zap.Error(err1), zap.Error(err2))
			continue
		}
		startIPStr := datasource.IntToIP(uint64(startIP), false).String()
		endIPStr := datasource.IntToIP(uint64(endIP), false).String()
		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				StartIP:   startIPStr,
				EndIP:     endIPStr,
				IPVersion: "IPv4",
			},
			Geolocation: model.Geolocation{
				Country: model.Country{
					Code: record[2],
				},
			},
			Metadata: model.Metadata{
				Source:      "iplocate",
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  ptrInt(65),
			},
		}
		ipInfos = append(ipInfos, ipInfo)
		processedCount++
	}
	return ipInfos, nil
}

func (i *IPLocate) Update(ctx context.Context, data []model.IPInfo) error {
	// 预处理数据：为每个IP范围生成CIDR
	var processedData []model.IPInfo
	for _, ipInfo := range data {
		cidrs, err := datasource.RangeToCIDR(ipInfo.IPRange.StartIP, ipInfo.IPRange.EndIP)
		if err != nil {
			i.logger.Warn("Failed to convert to CIDR", zap.Error(err))
			continue
		}
		for _, cidr := range cidrs {
			ipInfoCopy := ipInfo
			ipInfoCopy.IPRange.CIDR = cidr
			processedData = append(processedData, ipInfoCopy)
		}
	}

	// 使用共享的数据库适配器
	_, err := i.dbAdapter.BatchUpsertIPs(ctx, processedData)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	i.logger.Info("Successfully updated IPLocate data",
		zap.Int("records", len(processedData)),
		zap.String("source", "iplocate"))

	return nil
}

func intToIP(ipInt int64) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		(ipInt>>24)&0xff,
		(ipInt>>16)&0xff,
		(ipInt>>8)&0xff,
		ipInt&0xff)
}

// mergeIPInfos 合并相同IP范围的数据，优先级：MMDB > CSV
func (i *IPLocate) mergeIPInfos(ipInfos []model.IPInfo) []model.IPInfo {
	// 使用CIDR作为key进行分组
	cidrMap := make(map[string]*model.IPInfo)

	for _, info := range ipInfos {
		// 如果没有CIDR，尝试从StartIP和EndIP生成
		cidr := info.IPRange.CIDR
		if cidr == "" {
			cidrs, err := datasource.RangeToCIDR(info.IPRange.StartIP, info.IPRange.EndIP)
			if err != nil || len(cidrs) == 0 {
				continue
			}
			cidr = cidrs[0] // 使用第一个CIDR
		}

		existing, exists := cidrMap[cidr]
		if !exists {
			// 新的IP范围，直接添加
			infoCopy := info
			infoCopy.IPRange.CIDR = cidr
			cidrMap[cidr] = &infoCopy
		} else {
			// 已存在的IP范围，进行数据融合
			merged := i.mergeIPInfo(*existing, info)
			merged.IPRange.CIDR = cidr
			cidrMap[cidr] = &merged
		}
	}

	// 转换回切片
	var result []model.IPInfo
	for _, info := range cidrMap {
		result = append(result, *info)
	}

	return result
}

// mergeIPInfo 合并两个IPInfo，优先保留更完整的数据
func (i *IPLocate) mergeIPInfo(base, new model.IPInfo) model.IPInfo {
	merged := base

	// 合并地理位置信息
	if new.Geolocation.Country.Code != "" && merged.Geolocation.Country.Code == "" {
		merged.Geolocation.Country = new.Geolocation.Country
	}
	if new.Geolocation.Country.Name != "" && merged.Geolocation.Country.Name == "" {
		merged.Geolocation.Country.Name = new.Geolocation.Country.Name
	}
	if new.Geolocation.Continent.Code != "" && merged.Geolocation.Continent.Code == "" {
		merged.Geolocation.Continent = new.Geolocation.Continent
	}

	// 合并网络信息
	if new.Network.ASN != "" && merged.Network.ASN == "" {
		merged.Network.ASN = new.Network.ASN
	}
	if new.Network.Organization != "" && merged.Network.Organization == "" {
		merged.Network.Organization = new.Network.Organization
	}
	if new.Network.AutonomousSystemID != nil && merged.Network.AutonomousSystemID == nil {
		merged.Network.AutonomousSystemID = new.Network.AutonomousSystemID
	}

	// 更新元数据
	merged.Metadata.LastUpdated = time.Now().Format(time.RFC3339)

	// 如果新数据的置信度更高，使用新数据的置信度
	if new.Metadata.Confidence != nil && merged.Metadata.Confidence != nil {
		if *new.Metadata.Confidence > *merged.Metadata.Confidence {
			merged.Metadata.Confidence = new.Metadata.Confidence
		}
	} else if new.Metadata.Confidence != nil {
		merged.Metadata.Confidence = new.Metadata.Confidence
	}

	return merged
}

func ptrInt(i int) *int {
	return &i
}
