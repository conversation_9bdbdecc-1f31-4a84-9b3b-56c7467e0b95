package maxmind

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestMaxmindParse(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	// 创建测试数据目录
	dataDir := "./testdata"
	os.MkdirAll(dataDir, 0755)
	defer os.RemoveAll(dataDir)

	// 模拟 GeoLite2-Country-Blocks-IPv4.csv
	blocksContent := `network,geoname_id,country_iso_code,country_name
*******/24,2077456,AU,Australia`
	blocksFile := filepath.Join(dataDir, "GeoLite2-Country-Blocks-IPv4.csv")
	os.WriteFile(blocksFile, []byte(blocksContent), 0644)

	// 模拟 GeoLite2-Country-Locations-en.csv
	locationsContent := `geoname_id,locale_code,continent_code,continent_name,country_iso_code,country_name
2077456,en,OC,Oceania,AU,Australia`
	locationsFile := filepath.Join(dataDir, "GeoLite2-Country-Locations-en.csv")
	os.WriteFile(locationsFile, []byte(locationsContent), 0644)

	// 初始化 Maxmind
	maxmind := &Maxmind{
		config:  config.DatasourceConfig{URL: []string{}, Schedule: ""},
		logger:  logger,
		dataDir: dataDir,
	}

	// 测试 Parse
	ipInfos, err := maxmind.Parse(context.Background())
	assert.NoError(t, err)
	assert.Len(t, ipInfos, 1)

	ipInfo := ipInfos[0]
	assert.Equal(t, "*******/24", ipInfo.IPRange.CIDR)
	assert.Equal(t, "AU", ipInfo.Geolocation.Country.Code)
	assert.Equal(t, "Australia", ipInfo.Geolocation.Country.Name)
	assert.Equal(t, "maxmind", ipInfo.Metadata.Source)
}
