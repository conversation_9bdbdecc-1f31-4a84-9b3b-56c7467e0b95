package maxmind

import (
	"context"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"go.uber.org/zap"
)

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

type Maxmind struct {
	config    config.DatasourceConfig
	logger    *zap.Logger
	dataDir   string
	force     bool                       // 强制下载标志
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

func NewMaxmind(config config.DatasourceConfig, logger *zap.Logger, dbAdapter database.DatabaseInterface) *Maxmind {
	return &Maxmind{
		config:    config,
		logger:    logger,
		dataDir:   "./data/maxmind",
		force:     false,
		dbAdapter: dbAdapter,
	}
}

// SetForceDownload 设置强制下载标志
func (m *Maxmind) SetForceDownload(force bool) {
	m.force = force
}

func (m *Maxmind) Name() string {
	return "maxmind"
}

func (m *Maxmind) FetchSingle(ctx context.Context, url string, dbName string) error {
	mmdbPath := filepath.Join(m.dataDir, dbName)

	// 使用新的下载去重逻辑，支持强制下载
	checkOptions := utils.DefaultNoNeedDownloadOptions()
	checkOptions.Force = m.force

	ok, err := utils.NoNeedDownloadWithOptions(mmdbPath, m.logger, checkOptions)
	if err != nil {
		return err
	}
	if ok {
		m.logger.Info("Skipping MaxMind database download - file is fresh",
			zap.String("path", mmdbPath),
			zap.String("database", dbName))
		return nil
	}
	tempFile, err := os.CreateTemp(m.dataDir, "maxmind-*.tar.gz")
	if err != nil {
		m.logger.Error("Failed to create temp file", zap.Error(err))
		return fmt.Errorf("create temp file: %w", err)
	}
	tempPath := tempFile.Name()
	// 保留压缩包以便调试，不自动删除
	// defer os.Remove(tempPath)

	id := os.Getenv("maxmind_id")
	key := os.Getenv("maxmind_license_key")

	if strings.TrimSpace(id) == "" || strings.TrimSpace(key) == "" {
		err := "maxmind_id and maxmind_license_key not valid"
		m.logger.Error(err)
		return errors.New(err)
	}
	typ := "basic"
	auth := utils.Auth{
		Username: id,
		Password: key,
		Type:     &typ,
	}

	// 使用改进的下载功能
	downloadOptions := utils.DefaultDownloadOptions()
	downloadOptions.ShowProgress = true
	downloadOptions.CheckRemoteSize = true
	downloadOptions.SkipIfExists = false // 我们已经在上面检查过了

	if err := utils.DownloadWithRetry(ctx, url, tempFile, m.logger, &auth, downloadOptions); err != nil {
		tempFile.Close()
		return fmt.Errorf("download failed: %w", err)
	}
	tempFile.Close()

	m.logger.Info("Extracting MaxMind tar.gz file", zap.String("file", tempPath))
	if err := utils.ExtractTarGz(tempPath, m.dataDir, m.logger); err != nil {
		return fmt.Errorf("extract tar.gz failed: %w", err)
	}

	// 检查解压后的文件
	m.logger.Info("Checking extracted files in data directory")
	entries, err := os.ReadDir(m.dataDir)
	if err != nil {
		m.logger.Error("Failed to read data directory", zap.Error(err))
	} else {
		for _, entry := range entries {
			m.logger.Info("Found file/directory",
				zap.String("name", entry.Name()),
				zap.Bool("is_dir", entry.IsDir()))
		}
	}

	m.logger.Info("Moving MMDB files to data directory")
	if err := m.MoveMMDBFiles(".mmdb"); err != nil {
		return fmt.Errorf("move MMDB files failed: %w", err)
	}
	return nil
}
func (m *Maxmind) MoveMMDBFiles(ext string) error {
	entries, err := os.ReadDir(m.dataDir)
	if err != nil {
		return err
	}
	for _, entry := range entries {
		if entry.IsDir() {
			dirPath := filepath.Join(m.dataDir, entry.Name())
			err = filepath.WalkDir(dirPath, func(path string, d fs.DirEntry, err error) error {
				if err != nil {
					return err
				}
				if !d.IsDir() && strings.HasSuffix(d.Name(), ext) {
					newPath := filepath.Join(m.dataDir, d.Name())
					return os.Rename(path, newPath)
				}
				return nil
			})
			if err != nil {
				return err
			}
			if err := os.RemoveAll(dirPath); err != nil {
				return err
			}
		}
	}
	return nil
}
func (m *Maxmind) ReadMMDBFiles() ([]string, error) {
	entries, err := os.ReadDir(m.dataDir)
	if err != nil {
		return nil, err
	}
	dbList := []string{}
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".mmdb") {
			newPath := filepath.Join(m.dataDir, entry.Name())
			dbList = append(dbList, newPath)
		}
	}
	return dbList, nil
}
func (m *Maxmind) Fetch(ctx context.Context) error {
	if err := os.MkdirAll(m.dataDir, 0755); err != nil {
		m.logger.Error("Failed to create data directory", zap.Error(err))
		return fmt.Errorf("create data directory: %w", err)
	}
	var wg sync.WaitGroup
	for _, url := range m.config.URL {
		if !strings.Contains(url, "CSV") {
			wg.Add(1)
			go func(_url string) {
				defer wg.Done()
				dbName := "GeoLite2-Country"
				if strings.Contains(_url, "GeoLite2-Country") {
					dbName = "GeoLite2-Country.mmdb"
				}
				if strings.Contains(_url, "GeoLite2-City") {
					dbName = "GeoLite2-City.mmdb"
				}
				if strings.Contains(_url, "GeoLite2-ASN") {
					dbName = "GeoLite2-ASN.mmdb"
				}
				err := m.FetchSingle(ctx, _url, dbName)
				if err != nil {
					m.logger.Error("Failed to fetch MaxMind database", zap.Error(err))
				}
			}(url)
		}
	}
	wg.Wait()
	return nil
}

func (m *Maxmind) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (m *Maxmind) ParseAndUpdate(ctx context.Context) error {
	dbList, err := utils.ReadExtFiles(m.dataDir, ".mmdb")
	if err != nil {
		return fmt.Errorf("failed to read MMDB files: %w", err)
	}

	if len(dbList) == 0 {
		return fmt.Errorf("no MMDB files found in %s", m.dataDir)
	}

	// 使用共享的数据库适配器
	dbAdapter := m.dbAdapter

	// 流式处理每个MMDB文件
	for _, mmdbFile := range dbList {
		m.logger.Info("Processing MMDB file with streaming", zap.String("file", mmdbFile))

		_, err := os.Stat(mmdbFile)
		if err != nil {
			m.logger.Warn("MMDB file not found", zap.String("file", mmdbFile), zap.Error(err))
			continue
		}

		fileName := filepath.Base(mmdbFile)

		// 根据文件名判断MMDB类型并调用相应的流式解析函数
		switch {
		case strings.Contains(fileName, "ASN"):
			m.logger.Info("Streaming ASN MMDB file", zap.String("file", fileName))
			// ASN文件通常较小，暂时使用传统方法
			mmdbInfos, parseErr := datasource.ParseASNMMDB(mmdbFile, "maxmind")
			if parseErr != nil {
				m.logger.Error("Failed to parse ASN MMDB", zap.String("file", fileName), zap.Error(parseErr))
				continue
			}
			utils.SaveInfoToJSON(mmdbInfos, "maxmind_asn")
			if len(mmdbInfos) > 0 {
				_, err = dbAdapter.BatchUpsertIPs(ctx, mmdbInfos)
				if err != nil {
					return fmt.Errorf("failed to process ASN MMDB file %s: %w", fileName, err)
				}
			}

		case strings.Contains(fileName, "Country"):
			m.logger.Info("Streaming Country MMDB file", zap.String("file", fileName))
			err = datasource.ParseCountryMMDBStream(ctx, mmdbFile, "maxmind", dbAdapter, m.logger)

		case strings.Contains(fileName, "City"):
			m.logger.Info("Streaming City MMDB file", zap.String("file", fileName))
			err = datasource.ParseCityMMDBStream(ctx, mmdbFile, "maxmind", dbAdapter, m.logger)

		default:
			m.logger.Warn("Unknown MMDB file type, skipping", zap.String("file", fileName))
			continue
		}

		if err != nil {
			m.logger.Error("Failed to process MMDB file with streaming",
				zap.String("file", fileName),
				zap.Error(err))
			return fmt.Errorf("failed to process MMDB file %s: %w", fileName, err)
		}

		m.logger.Info("Successfully processed MMDB file with streaming",
			zap.String("file", fileName))
	}

	return nil
}

// parseCityMMDB 解析城市MMDB文件 - 适配GeoLite2-City格式
func (m *Maxmind) parseCityMMDB(filePath string, source string) ([]model.IPInfo, error) {
	return datasource.ParseCityMMDB(filePath, source)
}

func (m *Maxmind) Update(ctx context.Context, data []model.IPInfo) error {
	// 使用共享的数据库适配器
	_, err := m.dbAdapter.BatchUpsertIPs(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	m.logger.Info("Successfully updated MaxMind data",
		zap.Int("records", len(data)),
		zap.String("source", "maxmind"))

	return nil
}
