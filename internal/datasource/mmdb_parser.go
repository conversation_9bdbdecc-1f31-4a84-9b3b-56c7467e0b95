package datasource

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"github.com/oschwald/maxminddb-golang"
	"go.uber.org/zap"
)

// ParseASNMMDB 解析ASN MMDB文件
func ParseASNMMDB(filePath string, source string) ([]model.IPInfo, error) {
	db, err := maxminddb.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	var ipInfos []model.IPInfo
	networks := db.Networks(maxminddb.SkipAliasedNetworks)

	// ASN MMDB 记录结构体 - 适配IP2Location CSV格式
	record := struct {
		// 基础ASN信息
		AutonomousSystemNumber       int    `maxminddb:"autonomous_system_number"`
		AutonomousSystemOrganization string `maxminddb:"autonomous_system_organization"`

		// IP2Location扩展字段
		ASN         string `maxminddb:"asn"`          // ASN编号字符串格式
		CountryCode string `maxminddb:"country_code"` // 国家代码
		Name        string `maxminddb:"name"`         // ASN名称
		Org         string `maxminddb:"org"`          // 组织名称
		Domain      string `maxminddb:"domain"`       // 域名
	}{}

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0

	for networks.Next() {
		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			break
		}
		utils.ShowProcess(processedCount, nil)
		_inNet, err := networks.Network(&record)
		if err != nil {
			continue
		}

		// 优先使用IP2Location格式的字段，回退到MaxMind格式
		asnNumber := record.AutonomousSystemNumber
		asnString := record.ASN
		if asnString == "" && asnNumber > 0 {
			asnString = fmt.Sprintf("%d", asnNumber)
		}

		organization := record.Org
		if organization == "" {
			organization = record.AutonomousSystemOrganization
		}
		if organization == "" {
			organization = record.Name
		}

		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      _inNet.String(),
				IPVersion: GetIPVersion(_inNet),
				StartIP:   _inNet.IP.String(),
				EndIP:     GetEndIP(_inNet),
				Netmask:   net.IP(_inNet.Mask).String(),
			},
			Network: model.Network{
				ASN:                asnString,
				Organization:       organization,
				Domain:             record.Domain,
				AutonomousSystemID: PtrInt(asnNumber),
			},
			Geolocation: model.Geolocation{
				Country: model.Country{
					Code: record.CountryCode,
				},
			},
			Metadata: model.Metadata{
				Source:      source,
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  PtrInt(80),
			},
			Extended: model.Extended{
				CustomFields: make(map[string]any),
			},
		}

		ipInfos = append(ipInfos, ipInfo)
		processedCount++
	}

	return ipInfos, networks.Err()
}

// ParseCountryMMDB 解析国家MMDB文件
func ParseCountryMMDB(filePath string, source string) ([]model.IPInfo, error) {
	db, err := maxminddb.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	var ipInfos []model.IPInfo
	networks := db.Networks(maxminddb.SkipAliasedNetworks)

	// 国家 MMDB 记录结构体 - 适配IP2Location CSV格式
	record := struct {
		// MaxMind格式字段
		Continent struct {
			Code  string `maxminddb:"code"`
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
		} `maxminddb:"continent"`
		Country struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			IsInEuropeanUnion bool `maxminddb:"is_in_european_union"`
			GeonameID         int  `maxminddb:"geoname_id"`
		} `maxminddb:"country"`

		// IP2Location扩展字段
		ContinentCode string `maxminddb:"continent_code"` // 大陆代码
		CountryCode   string `maxminddb:"country_code"`   // 国家代码
		CountryName   string `maxminddb:"country_name"`   // 国家名称
	}{}

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0

	for networks.Next() {
		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			break
		}
		utils.ShowProcess(processedCount, nil)

		_inNet, err := networks.Network(&record)
		if err != nil {
			continue
		}

		// 优先使用IP2Location格式的字段，回退到MaxMind格式
		continentCode := record.ContinentCode
		if continentCode == "" {
			continentCode = record.Continent.Code
		}

		countryCode := record.CountryCode
		if countryCode == "" {
			countryCode = record.Country.ISOCode
		}

		countryName := record.CountryName
		if countryName == "" {
			countryName = record.Country.Names.En
		}

		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      _inNet.String(),
				IPVersion: GetIPVersion(_inNet),
				StartIP:   _inNet.IP.String(),
				EndIP:     GetEndIP(_inNet),
				Netmask:   net.IP(_inNet.Mask).String(),
			},
			Geolocation: model.Geolocation{
				Continent: model.Continent{
					Code: continentCode,
					Name: record.Continent.Names.En, // 保持MaxMind的大陆名称
				},
				Country: model.Country{
					Code:              countryCode,
					Name:              countryName,
					IsInEuropeanUnion: PtrBool(record.Country.IsInEuropeanUnion),
					GeonameID:         PtrInt(record.Country.GeonameID),
				},
			},
			Metadata: model.Metadata{
				Source:      source,
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  PtrInt(75),
			},
			Extended: model.Extended{
				CustomFields: make(map[string]any),
			},
		}

		ipInfos = append(ipInfos, ipInfo)
		processedCount++
	}

	return ipInfos, networks.Err()
}

// ParseCityMMDB 解析城市MMDB文件 - 使用流式处理避免OOM
func ParseCityMMDB(filePath string, source string) ([]model.IPInfo, error) {
	db, err := maxminddb.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	var allIPInfos []model.IPInfo
	networks := db.Networks(maxminddb.SkipAliasedNetworks)

	// 城市 MMDB 记录结构体 - 适配GeoLite2-City CSV格式
	record := struct {
		// MaxMind格式字段
		Continent struct {
			Code  string `maxminddb:"code"`
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
		} `maxminddb:"continent"`
		Country struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			IsInEuropeanUnion bool `maxminddb:"is_in_european_union"`
			GeonameID         int  `maxminddb:"geoname_id"`
		} `maxminddb:"country"`
		Subdivisions []struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			GeonameID int `maxminddb:"geoname_id"`
		} `maxminddb:"subdivisions"`
		City struct {
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			GeonameID int `maxminddb:"geoname_id"`
		} `maxminddb:"city"`
		Postal struct {
			Code string `maxminddb:"code"`
		} `maxminddb:"postal"`
		Location struct {
			Latitude       float64 `maxminddb:"latitude"`
			Longitude      float64 `maxminddb:"longitude"`
			TimeZone       string  `maxminddb:"time_zone"`
			AccuracyRadius int     `maxminddb:"accuracy_radius"`
			MetroCode      int     `maxminddb:"metro_code"`
		} `maxminddb:"location"`

		// GeoLite2-City CSV扩展字段
		GeonameID         int    `maxminddb:"geoname_id"`             // 地理名称ID
		LocaleCode        string `maxminddb:"locale_code"`            // 语言代码
		ContinentCode     string `maxminddb:"continent_code"`         // 大陆代码
		ContinentName     string `maxminddb:"continent_name"`         // 大陆名称
		CountryISOCode    string `maxminddb:"country_iso_code"`       // 国家ISO代码
		CountryName       string `maxminddb:"country_name"`           // 国家名称
		Subdivision1Code  string `maxminddb:"subdivision_1_iso_code"` // 一级行政区代码
		Subdivision1Name  string `maxminddb:"subdivision_1_name"`     // 一级行政区名称
		Subdivision2Code  string `maxminddb:"subdivision_2_iso_code"` // 二级行政区代码
		Subdivision2Name  string `maxminddb:"subdivision_2_name"`     // 二级行政区名称
		CityName          string `maxminddb:"city_name"`              // 城市名称
		MetroCodeCSV      string `maxminddb:"metro_code"`             // 都市区代码(CSV格式)
		TimeZone          string `maxminddb:"time_zone"`              // 时区
		IsInEuropeanUnion int    `maxminddb:"is_in_european_union"`   // 是否欧盟成员(CSV格式为int)
	}{}

	// 获取配置的处理限制
	batchSize := 1000
	debugLimit := utils.SetDebugLimit()
	processedCount := 0
	batchIPInfos := make([]model.IPInfo, 0, batchSize)

	for networks.Next() {
		utils.ShowProcess(processedCount, nil)
		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			break
		}
		_inNet, err := networks.Network(&record)
		if err != nil {
			continue
		}

		// 优先使用CSV格式的字段，回退到MaxMind格式
		continentCode := record.ContinentCode
		if continentCode == "" {
			continentCode = record.Continent.Code
		}

		continentName := record.ContinentName
		if continentName == "" {
			continentName = record.Continent.Names.En
		}

		countryCode := record.CountryISOCode
		if countryCode == "" {
			countryCode = record.Country.ISOCode
		}

		countryName := record.CountryName
		if countryName == "" {
			countryName = record.Country.Names.En
		}

		cityName := record.CityName
		if cityName == "" {
			cityName = record.City.Names.En
		}

		timeZone := record.TimeZone
		if timeZone == "" {
			timeZone = record.Location.TimeZone
		}

		// 处理行政区划信息
		var subdivisions []model.Subdivision
		if record.Subdivision1Code != "" || record.Subdivision1Name != "" {
			subdivisions = append(subdivisions, model.Subdivision{
				Code: record.Subdivision1Code,
				Name: record.Subdivision1Name,
			})
		}
		if record.Subdivision2Code != "" || record.Subdivision2Name != "" {
			subdivisions = append(subdivisions, model.Subdivision{
				Code: record.Subdivision2Code,
				Name: record.Subdivision2Name,
			})
		}
		// 如果CSV字段为空，使用MaxMind格式的subdivisions
		if len(subdivisions) == 0 && len(record.Subdivisions) > 0 {
			for _, sub := range record.Subdivisions {
				subdivisions = append(subdivisions, model.Subdivision{
					Code:      sub.ISOCode,
					Name:      sub.Names.En,
					GeonameID: PtrInt(sub.GeonameID),
				})
			}
		}

		// 处理欧盟成员状态
		isInEU := record.Country.IsInEuropeanUnion
		if record.IsInEuropeanUnion == 1 {
			isInEU = true
		}

		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      _inNet.String(),
				IPVersion: GetIPVersion(_inNet),
				StartIP:   _inNet.IP.String(),
				EndIP:     GetEndIP(_inNet),
				Netmask:   net.IP(_inNet.Mask).String(),
			},
			Geolocation: model.Geolocation{
				Continent: model.Continent{
					Code: continentCode,
					Name: continentName,
				},
				Country: model.Country{
					Code:              countryCode,
					Name:              countryName,
					IsInEuropeanUnion: PtrBool(isInEU),
					GeonameID:         PtrInt(record.Country.GeonameID),
				},
				Region: model.Region{
					Code: record.Subdivision1Code,
					Name: record.Subdivision1Name,
				},
				Subdivisions:   subdivisions,
				City:           cityName,
				PostalCode:     record.Postal.Code,
				Latitude:       PtrFloat64(record.Location.Latitude),
				Longitude:      PtrFloat64(record.Location.Longitude),
				AccuracyRadius: PtrInt(record.Location.AccuracyRadius),
				GeonameID:      PtrInt(record.City.GeonameID),
			},
			Timezone: model.Timezone{
				Name: timeZone,
			},
			Metadata: model.Metadata{
				Source:      source,
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  PtrInt(85),
			},
			Extended: model.Extended{
				CustomFields: make(map[string]any),
			},
		}

		batchIPInfos = append(batchIPInfos, ipInfo)
		processedCount++

		// 分批收集，避免内存耗尽
		if len(batchIPInfos) >= batchSize {
			allIPInfos = append(allIPInfos, batchIPInfos...)
			batchIPInfos = make([]model.IPInfo, 0, batchSize) // 重置批次
		}
	}

	// 处理最后一批数据
	if len(batchIPInfos) > 0 {
		allIPInfos = append(allIPInfos, batchIPInfos...)
	}

	return allIPInfos, networks.Err()
}

// ParseCityMMDBStream 流式解析城市MMDB文件，直接写入数据库，避免OOM
func ParseCityMMDBStream(ctx context.Context, filePath string, source string, dbAdapter database.DatabaseInterface, logger *zap.Logger) error {
	db, err := maxminddb.Open(filePath)
	if err != nil {
		return err
	}
	defer db.Close()

	networks := db.Networks(maxminddb.SkipAliasedNetworks)
	debugLimit := utils.SetDebugLimit()
	// 城市 MMDB 记录结构体
	record := struct {
		// MaxMind格式字段
		Continent struct {
			Code  string `maxminddb:"code"`
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
		} `maxminddb:"continent"`
		Country struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			IsInEuropeanUnion bool `maxminddb:"is_in_european_union"`
			GeonameID         int  `maxminddb:"geoname_id"`
		} `maxminddb:"country"`
		Subdivisions []struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			GeonameID int `maxminddb:"geoname_id"`
		} `maxminddb:"subdivisions"`
		City struct {
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			GeonameID int `maxminddb:"geoname_id"`
		} `maxminddb:"city"`
		Postal struct {
			Code string `maxminddb:"code"`
		} `maxminddb:"postal"`
		Location struct {
			Latitude       float64 `maxminddb:"latitude"`
			Longitude      float64 `maxminddb:"longitude"`
			TimeZone       string  `maxminddb:"time_zone"`
			AccuracyRadius int     `maxminddb:"accuracy_radius"`
			MetroCode      int     `maxminddb:"metro_code"`
		} `maxminddb:"location"`

		// GeoLite2-City CSV扩展字段
		GeonameID         int    `maxminddb:"geoname_id"`
		LocaleCode        string `maxminddb:"locale_code"`
		ContinentCode     string `maxminddb:"continent_code"`
		ContinentName     string `maxminddb:"continent_name"`
		CountryISOCode    string `maxminddb:"country_iso_code"`
		CountryName       string `maxminddb:"country_name"`
		Subdivision1Code  string `maxminddb:"subdivision_1_iso_code"`
		Subdivision1Name  string `maxminddb:"subdivision_1_name"`
		Subdivision2Code  string `maxminddb:"subdivision_2_iso_code"`
		Subdivision2Name  string `maxminddb:"subdivision_2_name"`
		CityName          string `maxminddb:"city_name"`
		MetroCodeCSV      string `maxminddb:"metro_code"`
		TimeZone          string `maxminddb:"time_zone"`
		IsInEuropeanUnion int    `maxminddb:"is_in_european_union"`
	}{}

	// 批量处理设置
	batchSize := 1000 // 固定批次大小，避免内存问题
	batchIPInfos := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	for networks.Next() {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}
		_inNet, err := networks.Network(&record)
		if err != nil {
			continue
		}

		// 构建IPInfo对象（复用之前的逻辑）
		ipInfo := buildIPInfoFromCityRecord(&record, _inNet, source)
		batchIPInfos = append(batchIPInfos, ipInfo)
		processedCount++

		// 当批次满了或者检查是否需要写入数据库
		if len(batchIPInfos) >= batchSize {
			// 写入数据库
			_, err := dbAdapter.BatchUpsertIPs(ctx, batchIPInfos)
			if err != nil {
				logger.Error("Failed to write batch to database",
					zap.Error(err),
					zap.Int("batch_size", len(batchIPInfos)),
					zap.Int("processed_count", processedCount))
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			logger.Info("Wrote batch to database",
				zap.Int("batch_number", totalBatches),
				zap.Int("batch_size", len(batchIPInfos)),
				zap.Int("total_processed", processedCount))

			// 重置批次，释放内存
			batchIPInfos = make([]model.IPInfo, 0, batchSize)
		}
	}

	// 处理最后一批数据
	if len(batchIPInfos) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batchIPInfos)
		if err != nil {
			logger.Error("Failed to write final batch to database",
				zap.Error(err),
				zap.Int("batch_size", len(batchIPInfos)))
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		logger.Info("Wrote final batch to database",
			zap.Int("batch_number", totalBatches),
			zap.Int("batch_size", len(batchIPInfos)),
			zap.Int("total_processed", processedCount))
	}

	logger.Info("Completed streaming MMDB processing",
		zap.String("file", filePath),
		zap.Int("total_records", processedCount),
		zap.Int("total_batches", totalBatches))

	return networks.Err()
}

// buildIPInfoFromCityRecord 从城市记录构建IPInfo对象
func buildIPInfoFromCityRecord(record *struct {
	Continent struct {
		Code  string `maxminddb:"code"`
		Names struct {
			En string `maxminddb:"en"`
		} `maxminddb:"names"`
	} `maxminddb:"continent"`
	Country struct {
		ISOCode string `maxminddb:"iso_code"`
		Names   struct {
			En string `maxminddb:"en"`
		} `maxminddb:"names"`
		IsInEuropeanUnion bool `maxminddb:"is_in_european_union"`
		GeonameID         int  `maxminddb:"geoname_id"`
	} `maxminddb:"country"`
	Subdivisions []struct {
		ISOCode string `maxminddb:"iso_code"`
		Names   struct {
			En string `maxminddb:"en"`
		} `maxminddb:"names"`
		GeonameID int `maxminddb:"geoname_id"`
	} `maxminddb:"subdivisions"`
	City struct {
		Names struct {
			En string `maxminddb:"en"`
		} `maxminddb:"names"`
		GeonameID int `maxminddb:"geoname_id"`
	} `maxminddb:"city"`
	Postal struct {
		Code string `maxminddb:"code"`
	} `maxminddb:"postal"`
	Location struct {
		Latitude       float64 `maxminddb:"latitude"`
		Longitude      float64 `maxminddb:"longitude"`
		TimeZone       string  `maxminddb:"time_zone"`
		AccuracyRadius int     `maxminddb:"accuracy_radius"`
		MetroCode      int     `maxminddb:"metro_code"`
	} `maxminddb:"location"`
	GeonameID         int    `maxminddb:"geoname_id"`
	LocaleCode        string `maxminddb:"locale_code"`
	ContinentCode     string `maxminddb:"continent_code"`
	ContinentName     string `maxminddb:"continent_name"`
	CountryISOCode    string `maxminddb:"country_iso_code"`
	CountryName       string `maxminddb:"country_name"`
	Subdivision1Code  string `maxminddb:"subdivision_1_iso_code"`
	Subdivision1Name  string `maxminddb:"subdivision_1_name"`
	Subdivision2Code  string `maxminddb:"subdivision_2_iso_code"`
	Subdivision2Name  string `maxminddb:"subdivision_2_name"`
	CityName          string `maxminddb:"city_name"`
	MetroCodeCSV      string `maxminddb:"metro_code"`
	TimeZone          string `maxminddb:"time_zone"`
	IsInEuropeanUnion int    `maxminddb:"is_in_european_union"`
}, _inNet *net.IPNet, source string) model.IPInfo {
	// 优先使用CSV格式的字段，回退到MaxMind格式
	continentCode := record.ContinentCode
	if continentCode == "" {
		continentCode = record.Continent.Code
	}

	continentName := record.ContinentName
	if continentName == "" {
		continentName = record.Continent.Names.En
	}

	countryCode := record.CountryISOCode
	if countryCode == "" {
		countryCode = record.Country.ISOCode
	}

	countryName := record.CountryName
	if countryName == "" {
		countryName = record.Country.Names.En
	}

	cityName := record.CityName
	if cityName == "" {
		cityName = record.City.Names.En
	}

	timeZone := record.TimeZone
	if timeZone == "" {
		timeZone = record.Location.TimeZone
	}

	// 处理行政区划信息
	var subdivisions []model.Subdivision
	if record.Subdivision1Code != "" || record.Subdivision1Name != "" {
		subdivisions = append(subdivisions, model.Subdivision{
			Code: record.Subdivision1Code,
			Name: record.Subdivision1Name,
		})
	}
	if record.Subdivision2Code != "" || record.Subdivision2Name != "" {
		subdivisions = append(subdivisions, model.Subdivision{
			Code: record.Subdivision2Code,
			Name: record.Subdivision2Name,
		})
	}
	// 如果CSV字段为空，使用MaxMind格式的subdivisions
	if len(subdivisions) == 0 && len(record.Subdivisions) > 0 {
		for _, sub := range record.Subdivisions {
			subdivisions = append(subdivisions, model.Subdivision{
				Code:      sub.ISOCode,
				Name:      sub.Names.En,
				GeonameID: PtrInt(sub.GeonameID),
			})
		}
	}

	// 处理欧盟成员状态
	isInEU := record.Country.IsInEuropeanUnion
	if record.IsInEuropeanUnion == 1 {
		isInEU = true
	}

	return model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      _inNet.String(),
			IPVersion: GetIPVersion(_inNet),
			StartIP:   _inNet.IP.String(),
			EndIP:     GetEndIP(_inNet),
			Netmask:   net.IP(_inNet.Mask).String(),
		},
		Geolocation: model.Geolocation{
			Continent: model.Continent{
				Code: continentCode,
				Name: continentName,
			},
			Country: model.Country{
				Code:              countryCode,
				Name:              countryName,
				IsInEuropeanUnion: PtrBool(isInEU),
				GeonameID:         PtrInt(record.Country.GeonameID),
			},
			Region: model.Region{
				Code: record.Subdivision1Code,
				Name: record.Subdivision1Name,
			},
			Subdivisions:   subdivisions,
			City:           cityName,
			PostalCode:     record.Postal.Code,
			Latitude:       PtrFloat64(record.Location.Latitude),
			Longitude:      PtrFloat64(record.Location.Longitude),
			AccuracyRadius: PtrInt(record.Location.AccuracyRadius),
			GeonameID:      PtrInt(record.City.GeonameID),
		},
		Timezone: model.Timezone{
			Name: timeZone,
		},
		Metadata: model.Metadata{
			Source:      source,
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  PtrInt(85),
		},
		Extended: model.Extended{
			CustomFields: make(map[string]any),
		},
	}
}

// ParseCountryMMDBStream 流式解析国家MMDB文件，直接写入数据库，避免OOM
func ParseCountryMMDBStream(ctx context.Context, filePath string, source string, dbAdapter database.DatabaseInterface, logger *zap.Logger) error {
	db, err := maxminddb.Open(filePath)
	if err != nil {
		return err
	}
	defer db.Close()

	networks := db.Networks(maxminddb.SkipAliasedNetworks)

	// 国家 MMDB 记录结构体
	record := struct {
		// MaxMind格式字段
		Continent struct {
			Code  string `maxminddb:"code"`
			Names struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
		} `maxminddb:"continent"`
		Country struct {
			ISOCode string `maxminddb:"iso_code"`
			Names   struct {
				En string `maxminddb:"en"`
			} `maxminddb:"names"`
			IsInEuropeanUnion bool `maxminddb:"is_in_european_union"`
			GeonameID         int  `maxminddb:"geoname_id"`
		} `maxminddb:"country"`

		// IP2Location扩展字段
		ContinentCode string `maxminddb:"continent_code"` // 大陆代码
		CountryCode   string `maxminddb:"country_code"`   // 国家代码
		CountryName   string `maxminddb:"country_name"`   // 国家名称
	}{}

	// 批量处理设置
	batchSize := 1000 // 固定批次大小，避免内存问题
	batchIPInfos := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	debugLimit := utils.SetDebugLimit()
	for networks.Next() {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		_inNet, err := networks.Network(&record)
		if err != nil {
			continue
		}
		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}
		// 优先使用IP2Location格式的字段，回退到MaxMind格式
		continentCode := record.ContinentCode
		if continentCode == "" {
			continentCode = record.Continent.Code
		}

		countryCode := record.CountryCode
		if countryCode == "" {
			countryCode = record.Country.ISOCode
		}

		countryName := record.CountryName
		if countryName == "" {
			countryName = record.Country.Names.En
		}

		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      _inNet.String(),
				IPVersion: GetIPVersion(_inNet),
				StartIP:   _inNet.IP.String(),
				EndIP:     GetEndIP(_inNet),
				Netmask:   net.IP(_inNet.Mask).String(),
			},
			Geolocation: model.Geolocation{
				Continent: model.Continent{
					Code: continentCode,
					Name: record.Continent.Names.En, // 保持MaxMind的大陆名称
				},
				Country: model.Country{
					Code:              countryCode,
					Name:              countryName,
					IsInEuropeanUnion: PtrBool(record.Country.IsInEuropeanUnion),
					GeonameID:         PtrInt(record.Country.GeonameID),
				},
			},
			Metadata: model.Metadata{
				Source:      source,
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  PtrInt(75),
			},
			Extended: model.Extended{
				CustomFields: make(map[string]any),
			},
		}

		batchIPInfos = append(batchIPInfos, ipInfo)
		processedCount++

		// 当批次满了，写入数据库
		if len(batchIPInfos) >= batchSize {
			// 写入数据库
			_, err := dbAdapter.BatchUpsertIPs(ctx, batchIPInfos)
			if err != nil {
				logger.Error("Failed to write batch to database",
					zap.Error(err),
					zap.Int("batch_size", len(batchIPInfos)),
					zap.Int("processed_count", processedCount))
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			logger.Info("Wrote batch to database",
				zap.Int("batch_number", totalBatches),
				zap.Int("batch_size", len(batchIPInfos)),
				zap.Int("total_processed", processedCount))

			// 重置批次，释放内存
			batchIPInfos = make([]model.IPInfo, 0, batchSize)
		}
	}

	utils.SaveInfoToJSON(batchIPInfos, source)
	// 处理最后一批数据
	if len(batchIPInfos) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batchIPInfos)
		if err != nil {
			logger.Error("Failed to write final batch to database",
				zap.Error(err),
				zap.Int("batch_size", len(batchIPInfos)))
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		logger.Info("Wrote final batch to database",
			zap.Int("batch_number", totalBatches),
			zap.Int("batch_size", len(batchIPInfos)),
			zap.Int("total_processed", processedCount))
	}

	logger.Info("Completed streaming MMDB processing",
		zap.String("file", filePath),
		zap.Int("total_records", processedCount),
		zap.Int("total_batches", totalBatches))

	return networks.Err()
}
