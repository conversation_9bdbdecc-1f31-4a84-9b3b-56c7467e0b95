package qqwry

import (
	"context"
	"encoding/binary"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"go.uber.org/zap"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// QQWry 数据库相关常量
const (
	redirectMode1 = 0x01
	redirectMode2 = 0x02
	indexLen      = 7
)

// 全局变量
var (
	data []byte
)

// Location 表示位置信息
type Location struct {
	IP       string
	Country  string
	Province string
	City     string
	District string
	ISP      string
}

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

// QQWryDatasource QQWry数据源适配器
type QQWryDatasource struct {
	config    *config.DatasourceConfig
	dataDir   string
	logger    *zap.Logger
	force     bool                       // 强制下载标志
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

// NewQQWryDatasource 创建新的 QQWry 数据源实例
func NewQQWryDatasource(cfg *config.DatasourceConfig, dataDir string, logger *zap.Logger, dbAdapter database.DatabaseInterface) *QQWryDatasource {
	return &QQWryDatasource{
		config:    cfg,
		dataDir:   dataDir,
		logger:    logger,
		force:     false,
		dbAdapter: dbAdapter,
	}
}

// SetForceDownload 设置强制下载标志
func (q *QQWryDatasource) SetForceDownload(force bool) {
	q.force = force
}

func (q *QQWryDatasource) Name() string {
	return "qqwry"
}

func (q *QQWryDatasource) Fetch(ctx context.Context) error {
	if err := os.MkdirAll(q.dataDir, 0755); err != nil {
		q.logger.Error("Failed to create data directory", zap.Error(err))
		return fmt.Errorf("create data directory: %w", err)
	}

	// 下载 QQWry 数据文件
	for _, url := range q.config.URL {
		filename := filepath.Base(url)
		if strings.Contains(filename, "?") {
			// 处理带参数的URL，提取文件名
			parts := strings.Split(filename, "?")
			filename = parts[0]
		}

		q.logger.Info("Starting QQWry database download",
			zap.String("url", url),
			zap.String("filename", filename))

		err := q.fetchSingle(ctx, url, filename)
		if err != nil {
			q.logger.Error("Failed to fetch QQWry database",
				zap.String("url", url),
				zap.Error(err))
			continue
		}

		q.logger.Info("Successfully fetched QQWry database",
			zap.String("filename", filename))
	}

	return nil
}

// fetchSingle 下载单个文件
func (q *QQWryDatasource) fetchSingle(ctx context.Context, downloadURL, filename string) error {
	filePath := filepath.Join(q.dataDir, filename)

	// 检查是否需要下载（支持强制下载）
	downloadCheckOptions := utils.DefaultNoNeedDownloadOptions()
	downloadCheckOptions.Force = q.force

	ok, err := utils.NoNeedDownloadWithOptions(filePath, q.logger, downloadCheckOptions)
	if err != nil {
		return fmt.Errorf("check download necessity: %w", err)
	}
	if ok {
		q.logger.Info("Skipping QQWry file download - file is fresh",
			zap.String("path", filePath),
			zap.String("filename", filename))
		return nil
	}

	// 创建临时文件
	tempFile, err := os.CreateTemp(q.dataDir, filename+".tmp")
	if err != nil {
		return fmt.Errorf("create temp file: %w", err)
	}
	defer tempFile.Close()
	defer os.Remove(tempFile.Name())

	// 使用改进的下载功能
	downloadOptions := utils.DefaultDownloadOptions()
	downloadOptions.ShowProgress = true
	downloadOptions.CheckRemoteSize = true
	downloadOptions.SkipIfExists = false // 我们已经在上面检查过了

	err = utils.DownloadWithRetry(ctx, downloadURL, tempFile, q.logger, nil, downloadOptions)
	if err != nil {
		return fmt.Errorf("download failed: %w", err)
	}

	// 移动临时文件到最终位置
	if err := os.Rename(tempFile.Name(), filePath); err != nil {
		return fmt.Errorf("move temp file: %w", err)
	}

	return nil
}

func (q *QQWryDatasource) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (q *QQWryDatasource) ParseAndUpdate(ctx context.Context) error {
	// 查找 .dat 文件
	datFile := filepath.Join(q.dataDir, "qqwry.dat")
	if _, err := os.Stat(datFile); os.IsNotExist(err) {
		return fmt.Errorf("qqwry.dat file not found")
	}

	// 加载数据库文件
	err := LoadFile(datFile)
	if err != nil {
		return fmt.Errorf("load database: %w", err)
	}

	q.logger.Info("QQWry database loaded successfully", zap.String("file", datFile))

	// 使用共享的数据库适配器
	dbAdapter := q.dbAdapter

	// 实现流式数据库解析
	return q.parseAllRecordsStream(ctx, dbAdapter)
}

func (q *QQWryDatasource) Update(ctx context.Context, data []model.IPInfo) error {
	// QQWry 数据源主要用于查询，不需要批量插入数据库
	// 但我们可以将测试记录插入数据库以验证集成
	if len(data) == 0 {
		q.logger.Info("No QQWry data to update")
		return nil
	}

	// 预处理数据：为QQWry数据生成CIDR
	for i := range data {
		if data[i].IPRange.CIDR == "" && data[i].IPRange.StartIP != "" {
			data[i].IPRange.CIDR = data[i].IPRange.StartIP + "/32"
		}
	}

	// 使用共享的数据库适配器
	_, err := q.dbAdapter.BatchUpsertIPs(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	q.logger.Info("Successfully updated QQWry data",
		zap.Int("records", len(data)),
		zap.String("source", "qqwry"))

	return nil
}

// uint32ToIP 将uint32转换为IP字符串
func (q *QQWryDatasource) uint32ToIP(ip uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		(ip>>24)&0xFF,
		(ip>>16)&0xFF,
		(ip>>8)&0xFF,
		ip&0xFF)
}

// generateCIDR 根据起始和结束IP生成CIDR
func (q *QQWryDatasource) generateCIDR(startIP, endIP uint32) string {
	// 简单实现：如果是单个IP，返回/32
	if startIP == endIP {
		return q.uint32ToIP(startIP) + "/32"
	}

	// 计算网络掩码长度
	xor := startIP ^ endIP
	prefixLen := 32
	for xor != 0 {
		xor >>= 1
		prefixLen--
	}

	// 计算网络地址
	mask := uint32(0xFFFFFFFF) << (32 - prefixLen)
	networkIP := startIP & mask

	return fmt.Sprintf("%s/%d", q.uint32ToIP(networkIP), prefixLen)
}

// parseAllRecordsStream 流式解析QQWry数据库中的所有IP记录并直接写入数据库
func (q *QQWryDatasource) parseAllRecordsStream(ctx context.Context, dbAdapter database.DatabaseInterface) error {
	// 检查是否有数据加载
	if len(data) < 8 {
		return fmt.Errorf("QQWry database not loaded or invalid")
	}

	// 读取索引开始和结束位置
	indexStart := binary.LittleEndian.Uint32(data[:4])
	indexEnd := binary.LittleEndian.Uint32(data[4:8])

	// 计算记录数量
	recordCount := (indexEnd-indexStart)/indexLen + 1
	q.logger.Info("Starting QQWry streaming processing",
		zap.Uint32("index_start", indexStart),
		zap.Uint32("index_end", indexEnd),
		zap.Uint32("record_count", recordCount))

	// 批量处理设置
	batchSize := 1000
	batch := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()

	// 遍历所有索引记录
	for offset := indexStart; offset <= indexEnd; offset += indexLen {
		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			q.logger.Info("Debug mode: processing limited",
				zap.Int("processed", processedCount),
				zap.Int("debug_limit", debugLimit))
			break
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if offset+6 >= uint32(len(data)) {
			break
		}

		// 读取IP范围的开始IP
		startIP := binary.LittleEndian.Uint32(data[offset : offset+4])
		// 直接使用 qqwry_iter.go 中定义的函数
		recordOffset := byte3ToUInt32(data[offset+4 : offset+7])

		// 检查记录偏移是否有效
		if recordOffset >= uint32(len(data)) || recordOffset+4 >= uint32(len(data)) {
			continue
		}

		// 首先读取结束IP
		endIP := binary.LittleEndian.Uint32(data[recordOffset : recordOffset+4])

		// 解析记录（跳过结束IP的4字节）
		location, err := parseLocationAtOffset(recordOffset + 4)
		if err != nil {
			q.logger.Warn("Failed to parse location", zap.Error(err))
			continue
		}

		// 创建IPInfo对象
		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      q.generateCIDR(startIP, endIP),
				StartIP:   q.uint32ToIP(startIP),
				EndIP:     q.uint32ToIP(endIP),
				IPVersion: "IPv4",
			},
			Geolocation: model.Geolocation{
				Country: model.Country{
					Name: location.Country,
				},
				Region: model.Region{
					Name: location.Province,
				},
				City: location.City,
			},
			Network: model.Network{
				ISP: location.ISP,
			},
			Metadata: model.Metadata{
				Source:      "qqwry",
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  datasource.PtrInt(75),
			},
			Extended: model.Extended{
				CustomFields: map[string]interface{}{
					"district": location.District,
					"province": location.Province,
				},
			},
		}

		batch = append(batch, ipInfo)
		processedCount++

		// 当批次满了，写入数据库
		if len(batch) >= batchSize {
			_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
			if err != nil {
				q.logger.Error("Failed to write batch to database",
					zap.Error(err),
					zap.Int("batch_size", len(batch)),
					zap.Int("processed_count", processedCount))
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			q.logger.Info("Wrote QQWry batch to database",
				zap.Int("batch_number", totalBatches),
				zap.Int("batch_size", len(batch)),
				zap.Int("total_processed", processedCount))

			// 重置批次，释放内存
			batch = make([]model.IPInfo, 0, batchSize)
		}

		// 每处理1万条记录报告一次进度
		if processedCount%10000 == 0 {
			q.logger.Info("QQWry parsing progress",
				zap.Int("processed", processedCount),
				zap.Uint32("total", recordCount),
				zap.Float64("percentage", float64(processedCount)/float64(recordCount)*100))
		}
	}
	utils.SaveInfoToJSON(batch, "qqwry")
	// 处理最后一批数据
	if len(batch) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
		if err != nil {
			q.logger.Error("Failed to write final batch to database",
				zap.Error(err),
				zap.Int("batch_size", len(batch)))
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		q.logger.Info("Wrote final QQWry batch to database",
			zap.Int("batch_number", totalBatches),
			zap.Int("batch_size", len(batch)),
			zap.Int("total_processed", processedCount))
	}

	q.logger.Info("Completed QQWry streaming processing",
		zap.Int("total_records", processedCount),
		zap.Int("total_batches", totalBatches))

	return nil
}

// LoadFile 加载QQWry数据文件到内存
func LoadFile(filepath string) error {
	var err error
	data, err = ioutil.ReadFile(filepath)
	if err != nil {
		return fmt.Errorf("failed to read file: %w", err)
	}
	if len(data) < 8 {
		return fmt.Errorf("invalid file format: file too small")
	}
	return nil
}

// gbkToUtf8 将GBK编码转换为UTF-8
func gbkToUtf8(gbkBytes []byte) string {
	decoder := simplifiedchinese.GBK.NewDecoder()
	utf8Bytes, _, err := transform.Bytes(decoder, gbkBytes)
	if err != nil {
		return ""
	}
	return string(utf8Bytes)
}

// readMode 读取模式字节
func readMode(offset uint32) byte {
	if offset >= uint32(len(data)) {
		return 0
	}
	return data[offset]
}

// readUInt24 读取24位无符号整数
func readUInt24(offset uint32) uint32 {
	if offset+3 > uint32(len(data)) {
		return 0
	}
	// 直接使用 qqwry_iter.go 中定义的函数
	return byte3ToUInt32(data[offset : offset+3])
}

// readString 读取以null结尾的字符串
func readString(offset uint32) []byte {
	if offset >= uint32(len(data)) {
		return []byte("")
	}

	var result []byte
	for i := offset; i < uint32(len(data)); i++ {
		if data[i] == 0 {
			break
		}
		result = append(result, data[i])
	}
	return result
}

// readArea 读取区域信息
func readArea(offset uint32) []byte {
	mode := readMode(offset)
	if mode == redirectMode1 || mode == redirectMode2 {
		areaOffset := readUInt24(offset + 1)
		if areaOffset == 0 {
			return []byte("")
		}
		return readString(areaOffset)
	}
	return readString(offset)
}

// parseLocationAtOffset 解析指定偏移处的位置信息
func parseLocationAtOffset(offset uint32) (*Location, error) {
	if offset >= uint32(len(data)) {
		return nil, fmt.Errorf("offset out of bounds")
	}

	location := &Location{}
	var countryBytes []byte
	var areaBytes []byte

	mode := readMode(offset)
	if mode == redirectMode1 {
		// 重定向模式1
		countryOffset := readUInt24(offset + 1)
		mode = readMode(countryOffset)
		if mode == redirectMode2 {
			// 国家信息再次重定向
			c := readUInt24(countryOffset + 1)
			countryBytes = readString(c)
			countryOffset += 4
		} else {
			countryBytes = readString(countryOffset)
			countryOffset += uint32(len(countryBytes) + 1)
		}
		areaBytes = readArea(countryOffset)
	} else if mode == redirectMode2 {
		// 重定向模式2
		countryOffset := readUInt24(offset + 1)
		countryBytes = readString(countryOffset)
		areaBytes = readArea(offset + 4)
	} else {
		// 直接模式
		countryBytes = readString(offset)
		areaBytes = readArea(offset + uint32(len(countryBytes)+1))
	}

	// 转换编码
	countryStr := gbkToUtf8(countryBytes)
	areaStr := gbkToUtf8(areaBytes)

	// 解析国家和区域信息
	location.Country = parseCountry(countryStr)
	location.Province, location.City, location.District = parseArea(areaStr)
	location.ISP = parseISP(areaStr)

	return location, nil
}

// parseCountry 解析国家信息
func parseCountry(countryStr string) string {
	if countryStr == "" {
		return "未知"
	}
	// 移除常见的前缀
	countryStr = strings.TrimSpace(countryStr)
	if strings.HasPrefix(countryStr, "CZ88.NET") {
		return "中国"
	}
	return countryStr
}

// parseArea 解析区域信息（省、市、区）
func parseArea(areaStr string) (province, city, district string) {
	if areaStr == "" {
		return "未知", "未知", "未知"
	}

	areaStr = strings.TrimSpace(areaStr)

	// 处理常见的无效标记
	// if strings.Contains(areaStr, "CZ88.NET") {
	// 	return "未知", "未知", "未知"
	// }

	// 简单的省市区解析
	parts := strings.Split(areaStr, " ")
	if len(parts) >= 1 {
		province = parts[0]
	}
	if len(parts) >= 2 {
		city = parts[1]
	}
	if len(parts) >= 3 {
		district = strings.Join(parts[2:], " ")
	}

	// 如果没有解析出有效信息，使用原始字符串
	if province == "" {
		province = areaStr
		city = "未知"
		district = "未知"
	}

	return province, city, district
}

// parseISP 解析ISP信息
func parseISP(areaStr string) string {
	if areaStr == "" {
		return "未知"
	}

	// 检查常见的ISP标识
	// isps := []string{"电信", "联通", "移动", "铁通", "网通", "教育网", "长城宽带", "广电网络"}
	// for _, isp := range isps {
	// 	if strings.Contains(areaStr, isp) {
	// 		return isp
	// 	}
	// }

	// 如果没有找到ISP信息，返回区域信息
	return areaStr
}
