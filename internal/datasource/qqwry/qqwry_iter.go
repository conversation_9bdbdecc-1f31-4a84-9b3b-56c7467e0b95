package qqwry

import (
	"encoding/binary"
	"fmt"
	"net"
	"os"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

const (
	INDEX_LEN       = 7
	REDIRECT_MODE_1 = 0x01
	REDIRECT_MODE_2 = 0x02
)

// <AUTHOR>
type QQwry struct {
	Ip       string
	Country  string
	City     string
	filepath string
	file     *os.File
}

func NewQQwry(file string) (qqwry *QQwry) {
	qqwry = &QQwry{filepath: file}
	return
}

func (this *QQwry) Find(ip string) {
	if this.filepath == "" {
		return
	}

	file, err := os.OpenFile(this.filepath, os.O_RDONLY, 0400)
	if err != nil {
		return
	}
	defer file.Close()
	this.file = file

	this.Ip = ip
	ipv4 := net.ParseIP(ip).To4()
	byte4 := binary.BigEndian.Uint32(ipv4)
	offset := this.searchIndex(byte4)
	fmt.Printf("ipv4: %v, byte4: %v loc offset: %v", ipv4, byte4, offset)
	if offset <= 0 {
		return
	}

	var country []byte
	var area []byte
	// offset = 0
	mode := this.readMode(offset + 4)
	if mode == REDIRECT_MODE_1 {
		countryOffset := this.readUInt24()
		mode = this.readMode(countryOffset)
		if mode == REDIRECT_MODE_2 {
			c := this.readUInt24()
			country = this.readString(c)
			countryOffset += 4
		} else {
			country = this.readString(countryOffset)
			countryOffset += uint32(len(country) + 1)
		}
		area = this.readArea(countryOffset)
	} else if mode == REDIRECT_MODE_2 {
		countryOffset := this.readUInt24()
		country = this.readString(countryOffset)
		area = this.readArea(offset + 8)
	} else {
		country = this.readString(offset + 4)
		area = this.readArea(offset + uint32(5+len(country)))
	}

	this.Country = Gbk2Utf8(country)
	this.City = Gbk2Utf8(area)
}
func Gbk2Utf8(gbkBytes []byte) string {
	decoder := simplifiedchinese.GBK.NewDecoder()

	utf8Bytes, _, err := transform.Bytes(decoder, gbkBytes)
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return ""
	}
	return string(utf8Bytes)
}

func (this *QQwry) readMode(offset uint32) byte {
	this.file.Seek(int64(offset), 0)
	mode := make([]byte, 1)
	this.file.Read(mode)
	return mode[0]
}

func (this *QQwry) readArea(offset uint32) []byte {
	mode := this.readMode(offset)
	if mode == REDIRECT_MODE_1 || mode == REDIRECT_MODE_2 {
		areaOffset := this.readUInt24()
		if areaOffset == 0 {
			return []byte("")
		} else {
			return this.readString(areaOffset)
		}
	} else {
		return this.readString(offset)
	}
	return []byte("")
}

func (this *QQwry) readString(offset uint32) []byte {
	this.file.Seek(int64(offset), 0)
	data := make([]byte, 0, 30)
	buf := make([]byte, 1)
	for {
		this.file.Read(buf)
		if buf[0] == 0 {
			break
		}
		data = append(data, buf[0])
	}
	return data
}

func (this *QQwry) searchIndex(ip uint32) uint32 {
	header := make([]byte, 8)
	this.file.Seek(0, 0)
	this.file.Read(header)

	start := binary.LittleEndian.Uint32(header[:4])
	end := binary.LittleEndian.Uint32(header[4:])

	// log.Printf("len info %v, %v ---- %v, %v", start, end, hex.EncodeToString(header[:4]), hex.EncodeToString(header[4:]))

	for {
		mid := this.getMiddleOffset(start, end)
		this.file.Seek(int64(mid), 0)
		buf := make([]byte, INDEX_LEN)
		this.file.Read(buf)
		_ip := binary.LittleEndian.Uint32(buf[:4])

		// log.Printf(">> %v, %v, %v -- %v", start, mid, end, hex.EncodeToString(buf[:4]))

		if end-start == INDEX_LEN {
			offset := byte3ToUInt32(buf[4:])
			this.file.Read(buf)
			if ip < binary.LittleEndian.Uint32(buf[:4]) {
				return offset
			} else {
				return 0
			}
		}

		// 找到的比较大，向前移
		if _ip > ip {
			end = mid
		} else if _ip < ip { // 找到的比较小，向后移
			start = mid
		} else if _ip == ip {
			return byte3ToUInt32(buf[4:])
		}

	}
	return 0
}

func (this *QQwry) readUInt24() uint32 {
	buf := make([]byte, 3)
	this.file.Read(buf)
	return byte3ToUInt32(buf)
}

func (this *QQwry) getMiddleOffset(start uint32, end uint32) uint32 {
	records := ((end - start) / INDEX_LEN) >> 1
	return start + records*INDEX_LEN
}

func byte3ToUInt32(data []byte) uint32 {
	i := uint32(data[0]) & 0xff
	i |= (uint32(data[1]) << 8) & 0xff00
	i |= (uint32(data[2]) << 16) & 0xff0000
	return i
}
func uint32ToIP(ip uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d", ip>>24, ip>>16&0xFF, ip>>8&0xFF, ip&0xFF)
}

type IPRecord struct {
	StartIP string
	EndIP   string
	Country string
	Area    string
}

// 辅助函数：读取国家和城市信息（简化和假设直接模式）
func (q *QQwry) readLocation(offset uint32) (country, area string, err error) {
	_, err = q.file.Seek(int64(offset), 0)
	if err != nil {
		return "", "", err
	}
	var _c []byte
	var _a []byte
	mode := q.readMode(offset + 4)
	// pointer -> 5
	if mode == REDIRECT_MODE_1 {
		// fmt.Println("REDIRECT_MODE_1!")
		// 3 bytes country offset
		countryOffset := q.readUInt24()
		mode = q.readMode(countryOffset)

		if mode == REDIRECT_MODE_2 {
			// fmt.Println("~~~~~REDIRECT_MODE_2!")
			c := q.readUInt24()
			_c = q.readString(c)
			// mode 1 byte offset 3 byte
			countryOffset += 4
			country = Gbk2Utf8(_c)
			_a = q.readArea(countryOffset)
			area = Gbk2Utf8(_a)
		} else {
			_c = q.readString(countryOffset)
			country = Gbk2Utf8(_c)
			// length of country offset and 1 byte mode
			countryOffset += uint32(len(_c) + 1)
			_a = q.readArea(countryOffset)
			area = Gbk2Utf8(_a)
		}

	} else if mode == REDIRECT_MODE_2 {
		// fmt.Println("REDIRECT_MODE_2!")
		countryOffset := q.readUInt24()
		_c = q.readString(countryOffset)
		country = Gbk2Utf8(_c)
		_a = q.readArea(offset + 8)
		area = Gbk2Utf8(_a)
	} else {
		_c = q.readString(offset + 4)
		country = Gbk2Utf8(_c)
		// 4 字节ip 1 字节 MODE加上len(country)
		_a = (q.readArea(offset + uint32(5+len(_c))))
		area = Gbk2Utf8(_a)
	}

	return country, area, nil
}

func (q *QQwry) TraverseAll() ([]IPRecord, error) {

	file, err := os.Open(q.filepath)
	q.file = file
	if err != nil {
		return nil, err
	}

	header := make([]byte, 8)
	_, err = q.file.Read(header)
	if err != nil {
		return nil, err
	}
	// 起始索引偏移
	start := binary.LittleEndian.Uint32(header[0:4])
	end := binary.LittleEndian.Uint32(header[4:8])

	// 读取索引区
	indexSize := end - start + 1
	index := make([]byte, indexSize)
	_, err = q.file.Seek(int64(start), 0)
	if err != nil {
		return nil, err
	}
	_, err = q.file.Read(index)
	if err != nil {
		return nil, err
	}
	// 索引区长度
	__index := index

	// 起始索引偏移
	__start := start
	__end := end
	//
	records := make([]IPRecord, 1e6)
	recordCount := (__end - __start + 1) / INDEX_LEN // 计算记录总数

	for i := uint32(0); i < recordCount; i++ {
		offset := i * INDEX_LEN

		// 4字节起始IP
		startIP := binary.LittleEndian.Uint32(__index[offset : offset+4]) // 读取起始 IP
		// 3字节 IP记录offset
		dataOffset := byte3ToUInt32(__index[offset+4 : offset+7]) // 读取数据偏移量

		// 获取结束 IP（下一条记录的起始 IP - 1）
		var endIP uint32
		if i < recordCount-1 {
			nextOffset := (i + 1) * INDEX_LEN
			endIP = binary.LittleEndian.Uint32(__index[nextOffset:nextOffset+4]) - 1
		} else {
			endIP = 0xFFFFFFFF // 最后一条记录的结束 IP 为 ***************
		}

		country, city, err := q.readLocation(dataOffset)
		if err != nil {
			fmt.Println("q.readLocation error", err)
			continue
		}
		_record := IPRecord{
			StartIP: uint32ToIP(startIP),
			EndIP:   uint32ToIP(endIP),
			Country: country,
			Area:    city,
		}
		records = append(records, _record)

	}
	return records, nil
}

func main() {

	qqwry2 := NewQQwry("./qqwry.dat")

	records, err := qqwry2.TraverseAll()
	if err != nil {
		fmt.Println("遍历失败:", err)
		return
	}

	for _, record := range records {
		fmt.Printf("IP 范围: %s - %s, 国家: %s, Area: %s\n",
			record.StartIP, record.EndIP, record.Country, record.Area)
	}
}
