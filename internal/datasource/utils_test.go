package datasource

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRangeToCIDR(t *testing.T) {
	tests := []struct {
		startIP  string
		endIP    string
		expected []string
		hasError bool
	}{
		{
			startIP:  "*******",
			endIP:    "*********",
			expected: []string{"*******/24"},
			hasError: false,
		},
		{
			startIP:  "*******",
			endIP:    "*********",
			expected: []string{"*******/24", "*******/24"},
			hasError: false,
		},
		{
			startIP:  "invalid",
			endIP:    "*********",
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.startIP+"-"+tt.endIP, func(t *testing.T) {
			cidrs, err := RangeToCIDR(tt.startIP, tt.endIP)
			if tt.hasError {
				assert.Error(t, err)
				assert.Nil(t, cidrs)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, cidrs)
			}
		})
	}
}
