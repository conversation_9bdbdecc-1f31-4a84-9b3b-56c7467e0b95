package ip2location

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"go.uber.org/zap"
	"inet.af/netaddr"
)

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

type IP2Location struct {
	config    config.DatasourceConfig
	logger    *zap.Logger
	dataDir   string
	force     bool                       // 强制下载标志
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

func NewIP2Location(config config.DatasourceConfig, logger *zap.Logger, dbAdapter database.DatabaseInterface) *IP2Location {
	return &IP2Location{
		config:    config,
		logger:    logger,
		dataDir:   "./data/ip2location",
		force:     false,
		dbAdapter: dbAdapter,
	}
}

// SetForceDownload 设置强制下载标志
func (i *IP2Location) SetForceDownload(force bool) {
	i.force = force
}

func (i *IP2Location) Name() string {
	return "ip2location"
}

func (i *IP2Location) Fetch(ctx context.Context) error {
	//DATABASE_CODE:
	//DB11LITECSV -> DB11.LITE  IP-COUNTRY-REGION-CITY-LATITUDE-LONGITUDE-ZIPCODE-TIMEZONE
	//PX12LITECSV -> PX12.LITE  IP-PROXYTYPE-COUNTRY-REGION-CITY-ISP-DOMAIN-USAGETYPE-ASN-LASTSEEN-THREAT-RESIDENTIAL-PROVIDER-FRAUDSCORE
	//DBASNLITE -> DB-ASN.LITE  IP-ASN
	//下载地址
	//https://www.ip2location.com/download/?token={ip2location_key}&file={DATABASE_CODE}

	// 创建数据目录
	if err := os.MkdirAll(i.dataDir, 0755); err != nil {
		i.logger.Error("Failed to create data directory", zap.Error(err))
		return fmt.Errorf("create data directory: %w", err)
	}

	// 检查环境变量
	token := os.Getenv("ip2location_key")
	if strings.TrimSpace(token) == "" {
		err := "ip2location_key environment variable not set"
		i.logger.Error(err)
		return errors.New(err)
	}

	// 数据库配置
	databases := []struct {
		code     string
		filename string
		desc     string
	}{
		{"DB11LITECSV", "IP2LOCATION-LITE-DB11.CSV", "IP-COUNTRY-REGION-CITY-LATITUDE-LONGITUDE-ZIPCODE-TIMEZONE"},
		{"PX12LITECSV", "IP2PROXY-LITE-PX12.CSV", "IP-PROXYTYPE-COUNTRY-REGION-CITY-ISP-DOMAIN-USAGETYPE-ASN-LASTSEEN-THREAT-RESIDENTIAL-PROVIDER-FRAUDSCORE"},
		{"DBASNLITE", "IP2LOCATION-LITE-ASN.CSV", "IP-ASN"},
	}

	var wg sync.WaitGroup
	for _, db := range databases {
		wg.Add(1)
		go func(database struct {
			code     string
			filename string
			desc     string
		}) {
			defer wg.Done()

			// 构建下载URL
			downloadURL := fmt.Sprintf("https://www.ip2location.com/download/?token=%s&file=%s", token, database.code)

			i.logger.Info("Starting IP2Location database download",
				zap.String("database", database.code),
				zap.String("description", database.desc),
				zap.String("filename", database.filename))

			err := i.FetchSingle(ctx, downloadURL, database.filename)
			if err != nil {
				i.logger.Error("Failed to fetch IP2Location database",
					zap.String("database", database.code),
					zap.Error(err))
			} else {
				i.logger.Info("Successfully fetched IP2Location database",
					zap.String("database", database.code),
					zap.String("filename", database.filename))
			}
		}(db)
	}
	wg.Wait()
	return nil
}
func (m *IP2Location) FetchSingle(ctx context.Context, url string, dbName string) error {
	dbPath := filepath.Join(m.dataDir, dbName)
	dbExt := filepath.Ext(url)
	cacheDuration := 60 * 6 * 24 * time.Hour // 360天
	ok, err := utils.NoNeedDownload(dbPath, m.logger, &cacheDuration)
	if err != nil {
		return err
	}
	if ok {
		return nil
	}
	tempFile, err := os.CreateTemp(m.dataDir, dbName+".zip")

	if err != nil {
		m.logger.Error("Failed to create temp file", zap.Error(err))
		return fmt.Errorf("create temp file: %w", err)
	}
	defer tempFile.Close()
	tempPath := tempFile.Name()
	defer os.Remove(tempPath)

	if err := utils.Download(ctx, url, tempFile, m.logger, nil); err != nil {
		return err
	}
	if err := utils.ExtractZip(tempPath, m.dataDir, m.logger); err != nil {
		return err
	}
	if err := m.MoveCSVFiles(dbExt); err != nil {
		return err
	}
	return nil
}
func (m *IP2Location) MoveCSVFiles(ext string) error {
	entries, err := os.ReadDir(m.dataDir)
	if err != nil {
		return err
	}
	for _, entry := range entries {
		if entry.IsDir() {
			dirPath := filepath.Join(m.dataDir, entry.Name())
			err = filepath.WalkDir(dirPath, func(path string, d fs.DirEntry, err error) error {
				if err != nil {
					return err
				}
				if !d.IsDir() && strings.HasSuffix(d.Name(), ext) {
					newPath := filepath.Join(m.dataDir, d.Name())
					return os.Rename(path, newPath)
				}
				return nil
			})
			if err != nil {
				return err
			}
			if err := os.RemoveAll(dirPath); err != nil {
				return err
			}
		}
	}
	return nil
}
func (i *IP2Location) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (i *IP2Location) ParseAndUpdate(ctx context.Context) error {
	dbList, err := utils.ReadExtFiles(i.dataDir, ".CSV")
	if err != nil {
		return fmt.Errorf("failed to read CSV files: %w", err)
	}

	if len(dbList) == 0 {
		return fmt.Errorf("no CSV files found in %s", i.dataDir)
	}

	// 使用共享的数据库适配器
	dbAdapter := i.dbAdapter

	// 流式处理每个CSV文件
	for _, csvFile := range dbList {
		i.logger.Info("Processing CSV file with streaming", zap.String("file", csvFile))

		_, err := os.Stat(csvFile)
		if err != nil {
			i.logger.Warn("CSV file not found", zap.String("file", csvFile), zap.Error(err))
			continue
		}

		fileName := filepath.Base(csvFile)

		// 根据文件名判断CSV类型并调用相应的流式解析函数
		switch {
		case strings.Contains(fileName, "PX12"):
			i.logger.Info("Streaming IP2Proxy CSV file", zap.String("file", fileName))
			err = i.parseIP2ProxyCSVStream(ctx, csvFile, dbAdapter)

		case strings.Contains(fileName, "DB11"):
			i.logger.Info("Streaming IP2Location CSV file", zap.String("file", fileName))
			err = i.parseIP2LocationCSVStream(ctx, csvFile, dbAdapter)

		case strings.Contains(fileName, "ASN"):
			i.logger.Info("Streaming IP2ASN CSV file", zap.String("file", fileName))
			err = i.parseIP2ASNCSVStream(ctx, csvFile, dbAdapter)

		default:
			i.logger.Warn("Unknown CSV file type, skipping", zap.String("file", fileName))
			continue
		}

		if err != nil {
			i.logger.Error("Failed to process CSV file with streaming",
				zap.String("file", fileName),
				zap.Error(err))
			return fmt.Errorf("failed to process CSV file %s: %w", fileName, err)
		}

		i.logger.Info("Successfully processed CSV file with streaming",
			zap.String("file", fileName))
	}

	return nil
}

func (i *IP2Location) Update(ctx context.Context, data []model.IPInfo) error {
	// 使用共享的数据库适配器
	_, err := i.dbAdapter.BatchUpsertIPs(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	i.logger.Info("Successfully updated IP2Location data",
		zap.Int("records", len(data)),
		zap.String("source", "ip2location"))

	return nil
}

// parseIP2ProxyCSVStream 流式解析IP2Proxy CSV文件并直接写入数据库
func (i *IP2Location) parseIP2ProxyCSVStream(ctx context.Context, filePath string, dbAdapter database.DatabaseInterface) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// 批量处理设置
	batchSize := 1000
	batch := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("Error reading CSV row: %v\n", err)
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		// 实际是 16 列
		if len(record) != 16 {
			fmt.Printf("Invalid CSV row, expected 16 fields, got %d\n", len(record))
			continue
		}

		// 解析 IP 范围
		ipFrom, err := parseUint32(record[0])
		if err != nil {
			fmt.Printf("Invalid IP_FROM: %v\n", err)
			continue
		}
		ipTo, err := parseUint32(record[1])
		if err != nil {
			fmt.Printf("Invalid IP_TO: %v\n", err)
			continue
		}

		// 解析 FraudScore (第16列，索引15)
		fraudScore, err := parseUint32(record[15])
		if err != nil {
			fmt.Printf("Invalid FRAUDSCORE: %v\n", err)
			continue
		}

		// 定义 IP2Proxy 记录结构体
		type IP2ProxyRecord struct {
			IPFrom      uint32
			IPTo        uint32
			ProxyType   string
			CountryCode string
			CountryName string
			RegionName  string
			CityName    string
			ISP         string
			Domain      string
			UsageType   string
			ASN         string
			LastSeen    string
			Threat      string
			Residential string
			Provider    string
			FraudScore  uint32
		}

		ip2pRecord := IP2ProxyRecord{
			IPFrom:      ipFrom,
			IPTo:        ipTo,
			ProxyType:   record[2],
			CountryCode: record[3],
			CountryName: record[4],
			RegionName:  record[5],
			CityName:    record[6],
			ISP:         record[7],
			Domain:      record[8],
			UsageType:   record[9],
			ASN:         record[10],
			LastSeen:    record[12],
			Threat:      record[13],
			Residential: record[14],
			Provider:    record[11], // 第11列是ISP名称/Provider
			FraudScore:  fraudScore,
		}

		// 转换为 CIDR
		startIP := decimalToIP(ip2pRecord.IPFrom)
		endIP := decimalToIP(ip2pRecord.IPTo)
		cidrs, err := ipRangeToCIDR(startIP, endIP)
		if err != nil {
			fmt.Printf("Failed to convert IP range to CIDR: %v\n", err)
			continue
		}

		for _, cidrStr := range cidrs {
			_, ipNet, err := net.ParseCIDR(cidrStr)
			if err != nil {
				fmt.Printf("Invalid CIDR %s: %v\n", cidrStr, err)
				continue
			}

			// 判断是否为匿名或代理
			isProxy := ip2pRecord.ProxyType != "-" && ip2pRecord.ProxyType != ""
			isAnonymous := isProxy && (ip2pRecord.ProxyType == "VPN" || ip2pRecord.ProxyType == "TOR")

			ipInfo := model.IPInfo{
				IPRange: model.IPRange{
					CIDR:      ipNet.String(),
					IPVersion: datasource.GetIPVersion(ipNet),
					StartIP:   ipNet.IP.String(),
					EndIP:     datasource.GetEndIP(ipNet),
					Netmask:   net.IP(ipNet.Mask).String(),
				},
				Geolocation: model.Geolocation{
					Continent: model.Continent{
						Code: "", // IP2Proxy 不提供洲信息
						Name: "",
					},
					Country: model.Country{
						Code:              ip2pRecord.CountryCode,
						Name:              ip2pRecord.CountryName,
						IsInEuropeanUnion: nil,
						GeonameID:         nil,
					},
					Region: model.Region{
						Code:      "",
						Name:      ip2pRecord.RegionName,
						GeonameID: nil,
					},
					Subdivisions:   []model.Subdivision{},
					City:           ip2pRecord.CityName,
					PostalCode:     "",  // IP2Proxy 不提供邮编
					Latitude:       nil, // IP2Proxy 不提供经纬度
					Longitude:      nil,
					AccuracyRadius: nil,
					GeonameID:      nil,
				},
				Network: model.Network{
					ASN:                ip2pRecord.ASN,
					Organization:       ip2pRecord.ISP,
					AutonomousSystemID: nil, // 可选：如果需要解析 ASN 为数字
				},
				Timezone: model.Timezone{
					Name: "", // IP2Proxy 不提供时区
				},
				Security: model.Security{
					IsProxy:     &isProxy,
					IsAnonymous: &isAnonymous,
				},
				Metadata: model.Metadata{
					Source:      "ip2location",
					LastUpdated: time.Now().Format(time.RFC3339),
					Confidence:  datasource.PtrInt(85), // 假设置信度稍高
				},
				Extended: model.Extended{
					CustomFields: map[string]interface{}{
						"ProxyType":   ip2pRecord.ProxyType,
						"UsageType":   ip2pRecord.UsageType,
						"Domain":      ip2pRecord.Domain,
						"LastSeen":    ip2pRecord.LastSeen,
						"Threat":      ip2pRecord.Threat,
						"Residential": ip2pRecord.Residential,
						"Provider":    ip2pRecord.Provider,
						"FraudScore":  ip2pRecord.FraudScore,
					},
				},
			}

			batch = append(batch, ipInfo)
		}
		processedCount++

		// 当批次满了，写入数据库
		if len(batch) >= batchSize {
			_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
			if err != nil {
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			fmt.Printf("Wrote IP2Proxy batch %d to database (size: %d, total processed: %d)\n",
				totalBatches, len(batch), processedCount)

			// 重置批次，释放内存
			batch = make([]model.IPInfo, 0, batchSize)
		}

		// 每处理1万条记录报告一次进度
		if processedCount%10000 == 0 {
			fmt.Printf("IP2Proxy parsing progress: %d records processed\n", processedCount)
		}
	}

	// 处理最后一批数据
	utils.SaveInfoToJSON(batch, "ip2location_proxy_csv")
	if len(batch) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
		if err != nil {
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		fmt.Printf("Wrote final IP2Proxy batch %d to database (size: %d, total processed: %d)\n",
			totalBatches, len(batch), processedCount)
	}

	fmt.Printf("Completed IP2Proxy streaming processing: %d records, %d batches\n",
		processedCount, totalBatches)
	return nil
}

// parseIP2LocationCSVStream 流式解析IP2Location CSV文件并直接写入数据库
func (i *IP2Location) parseIP2LocationCSVStream(ctx context.Context, filePath string, dbAdapter database.DatabaseInterface) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// 批量处理设置
	batchSize := 1000
	batch := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("Error reading CSV row: %v\n", err)
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		if len(record) != 10 {
			fmt.Printf("Invalid CSV row, expected 10 fields, got %d\n", len(record))
			continue
		}

		ipFrom, err := parseUint32(record[0])
		if err != nil {
			fmt.Printf("Invalid IP_FROM: %v\n", err)
			continue
		}
		ipTo, err := parseUint32(record[1])
		if err != nil {
			fmt.Printf("Invalid IP_TO: %v\n", err)
			continue
		}
		latitude, err := parseFloat64(record[6])
		if err != nil {
			fmt.Printf("Invalid LATITUDE: %v\n", err)
			continue
		}
		longitude, err := parseFloat64(record[7])
		if err != nil {
			fmt.Printf("Invalid LONGITUDE: %v\n", err)
			continue
		}

		type IP2LocationRecord struct {
			IPFrom      uint32
			IPTo        uint32
			CountryCode string
			CountryName string
			RegionName  string
			CityName    string
			Latitude    float64
			Longitude   float64
			ZipCode     string
			TimeZone    string
		}

		ip2lRecord := IP2LocationRecord{
			IPFrom:      ipFrom,
			IPTo:        ipTo,
			CountryCode: record[2],
			CountryName: record[3],
			RegionName:  record[4],
			CityName:    record[5],
			Latitude:    latitude,
			Longitude:   longitude,
			ZipCode:     record[8],
			TimeZone:    record[9],
		}

		startIP := decimalToIP(ip2lRecord.IPFrom)
		endIP := decimalToIP(ip2lRecord.IPTo)

		cidrs, err := ipRangeToCIDR(startIP, endIP)
		if err != nil {
			fmt.Printf("Failed to convert IP range to CIDR: %v\n", err)
			continue
		}

		for _, cidrStr := range cidrs {
			_, ipNet, err := net.ParseCIDR(cidrStr)
			if err != nil {
				fmt.Printf("Invalid CIDR %s: %v\n", cidrStr, err)
				continue
			}

			ipInfo := model.IPInfo{
				IPRange: model.IPRange{
					CIDR:      ipNet.String(),
					IPVersion: datasource.GetIPVersion(ipNet),
					StartIP:   ipNet.IP.String(),
					EndIP:     datasource.GetEndIP(ipNet),
					Netmask:   net.IP(ipNet.Mask).String(),
				},
				Geolocation: model.Geolocation{
					Continent: model.Continent{
						Code: "", // IP2LOCATION 不提供洲信息，留空
						Name: "",
					},
					Country: model.Country{
						Code:              ip2lRecord.CountryCode,
						Name:              ip2lRecord.CountryName,
						IsInEuropeanUnion: nil, // IP2LOCATION 不提供，设为 nil
						GeonameID:         nil, // IP2LOCATION 不提供
					},
					Region: model.Region{
						Code:      "", // IP2LOCATION 不提供地区代码
						Name:      ip2lRecord.RegionName,
						GeonameID: nil, // IP2LOCATION 不提供
					},
					Subdivisions:   []model.Subdivision{}, // IP2LOCATION 不提供次级行政区
					City:           ip2lRecord.CityName,
					PostalCode:     ip2lRecord.ZipCode,
					Latitude:       datasource.PtrFloat64(ip2lRecord.Latitude),
					Longitude:      datasource.PtrFloat64(ip2lRecord.Longitude),
					AccuracyRadius: nil, // IP2LOCATION 不提供
					GeonameID:      nil, // IP2LOCATION 不提供
				},
				Network: model.Network{
					ASN:                "", // IP2LOCATION 不提供 ASN
					Organization:       "",
					AutonomousSystemID: nil,
				},
				Timezone: model.Timezone{
					Name: ip2lRecord.TimeZone,
					// Offset 可通过 time.LoadLocation 计算（可选）
				},
				Security: model.Security{
					IsProxy:     nil, // IP2LOCATION 不提供安全信息
					IsAnonymous: nil,
				},
				Metadata: model.Metadata{
					Source:      "ip2location",
					LastUpdated: time.Now().Format(time.RFC3339),
					Confidence:  datasource.PtrInt(80), // 假设默认置信度
				},
				Extended: model.Extended{
					CustomFields: make(map[string]interface{}),
				},
			}

			batch = append(batch, ipInfo)
		}
		processedCount++

		// 当批次满了，写入数据库
		if len(batch) >= batchSize {
			_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
			if err != nil {
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			fmt.Printf("Wrote IP2Location batch %d to database (size: %d, total processed: %d)\n",
				totalBatches, len(batch), processedCount)

			// 重置批次，释放内存
			batch = make([]model.IPInfo, 0, batchSize)
		}

		// 每处理1万条记录报告一次进度
		if processedCount%10000 == 0 {
			fmt.Printf("IP2Location parsing progress: %d records processed\n", processedCount)
		}
	}

	utils.SaveInfoToJSON(batch, "ip2location_csv")
	// 处理最后一批数据
	if len(batch) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
		if err != nil {
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		fmt.Printf("Wrote final IP2Location batch %d to database (size: %d, total processed: %d)\n",
			totalBatches, len(batch), processedCount)
	}

	fmt.Printf("Completed IP2Location streaming processing: %d records, %d batches\n",
		processedCount, totalBatches)
	return nil
}

// parseIP2ASNCSVStream 流式解析IP2ASN CSV文件并直接写入数据库
func (i *IP2Location) parseIP2ASNCSVStream(ctx context.Context, filePath string, dbAdapter database.DatabaseInterface) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// 批量处理设置
	batchSize := 1000
	batch := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("Error reading CSV row: %v\n", err)
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		if len(record) != 5 {
			fmt.Printf("Invalid CSV row, expected 5 fields, got %d\n", len(record))
			continue
		}

		ipFrom, err := parseUint32(record[0])
		if err != nil {
			fmt.Printf("Invalid IP_FROM: %v\n", err)
			continue
		}
		ipTo, err := parseUint32(record[1])
		if err != nil {
			fmt.Printf("Invalid IP_TO: %v\n", err)
			continue
		}

		cidrStr := record[2]
		// 跳过无效的CIDR值
		if cidrStr == "-" || cidrStr == "" {
			continue
		}
		_, ipNet, err := net.ParseCIDR(cidrStr)
		if err != nil {
			fmt.Printf("Invalid CIDR %s: %v\n", cidrStr, err)
			continue
		}

		asn := record[3]
		organization := record[4]
		if asn == "-" {
			asn = "" // 转换为空字符串，保持一致性
		}
		if organization == "-" {
			organization = ""
		}

		type IP2ASNRecord struct {
			IPFrom       uint32
			IPTo         uint32
			CIDR         string
			ASN          string
			Organization string
		}

		ip2asnRecord := IP2ASNRecord{
			IPFrom:       ipFrom,
			IPTo:         ipTo,
			CIDR:         cidrStr,
			ASN:          asn,
			Organization: organization,
		}

		startIP := decimalToIP(ip2asnRecord.IPFrom)
		endIP := decimalToIP(ip2asnRecord.IPTo)
		if !ipNet.Contains(net.ParseIP(startIP)) || !ipNet.Contains(net.ParseIP(endIP)) {
			fmt.Printf("CIDR %s does not match IP range %s-%s\n", cidrStr, startIP, endIP)
			continue
		}

		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      ipNet.String(),
				IPVersion: datasource.GetIPVersion(ipNet),
				StartIP:   ipNet.IP.String(),
				EndIP:     datasource.GetEndIP(ipNet),
				Netmask:   net.IP(ipNet.Mask).String(),
			},
			Geolocation: model.Geolocation{
				Continent: model.Continent{
					Code: "",
					Name: "",
				},
				Country: model.Country{
					Code:              "",
					Name:              "",
					IsInEuropeanUnion: nil,
					GeonameID:         nil,
				},
				Region: model.Region{
					Code:      "",
					Name:      "",
					GeonameID: nil,
				},
				Subdivisions:   []model.Subdivision{},
				City:           "",
				PostalCode:     "",
				Latitude:       nil,
				Longitude:      nil,
				AccuracyRadius: nil,
				GeonameID:      nil,
			},
			Network: model.Network{
				ASN:                ip2asnRecord.ASN,
				Organization:       ip2asnRecord.Organization,
				AutonomousSystemID: nil, // 可选：如果需要将 ASN 解析为数字
			},
			Timezone: model.Timezone{
				Name: "",
			},
			Security: model.Security{
				IsProxy:     nil,
				IsAnonymous: nil,
			},
			Metadata: model.Metadata{
				Source:      "ip2asn",
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  datasource.PtrInt(90), // ASN 数据置信度较高
			},
			Extended: model.Extended{
				CustomFields: make(map[string]interface{}),
			},
		}

		batch = append(batch, ipInfo)
		processedCount++

		// 当批次满了，写入数据库
		if len(batch) >= batchSize {
			_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
			if err != nil {
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			fmt.Printf("Wrote IP2ASN batch %d to database (size: %d, total processed: %d)\n",
				totalBatches, len(batch), processedCount)

			// 重置批次，释放内存
			batch = make([]model.IPInfo, 0, batchSize)
		}

		// 每处理1万条记录报告一次进度
		if processedCount%10000 == 0 {
			fmt.Printf("IP2ASN parsing progress: %d records processed\n", processedCount)
		}
	}

	utils.SaveInfoToJSON(batch, "ip2location_asn_csv")
	// 处理最后一批数据
	if len(batch) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
		if err != nil {
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		fmt.Printf("Wrote final IP2ASN batch %d to database (size: %d, total processed: %d)\n",
			totalBatches, len(batch), processedCount)
	}

	fmt.Printf("Completed IP2ASN streaming processing: %d records, %d batches\n",
		processedCount, totalBatches)
	return nil
}

// 根据 ParseIP2LocationCSV 以及字段定义逻辑，帮我补全函数 ParseIP2ProxyCSV 和 ParseIP2ASNCSV
// 解析 IP2LOCATION LITE  数据库, 字段为: IP-COUNTRY-REGION-CITY-LATITUDE-LONGITUDE-ZIPCODE-TIMEZONE
func ParseIP2LocationCSV(filePath string) ([]model.IPInfo, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()
	reader := csv.NewReader(file)
	var ipInfos []model.IPInfo

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("Error reading CSV row: %v\n", err)
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}
		utils.ShowProcess(processedCount, nil)
		if len(record) != 10 {
			fmt.Printf("Invalid CSV row, expected 10 fields, got %d\n", len(record))
			continue
		}
		ipFrom, err := parseUint32(record[0])
		if err != nil {
			fmt.Printf("Invalid IP_FROM: %v\n", err)
			continue
		}
		ipTo, err := parseUint32(record[1])
		if err != nil {
			fmt.Printf("Invalid IP_TO: %v\n", err)
			continue
		}
		latitude, err := parseFloat64(record[6])
		if err != nil {
			fmt.Printf("Invalid LATITUDE: %v\n", err)
			continue
		}
		longitude, err := parseFloat64(record[7])
		if err != nil {
			fmt.Printf("Invalid LONGITUDE: %v\n", err)
			continue
		}
		type IP2LocationRecord struct {
			IPFrom      uint32
			IPTo        uint32
			CountryCode string
			CountryName string
			RegionName  string
			CityName    string
			Latitude    float64
			Longitude   float64
			ZipCode     string
			TimeZone    string
		}

		ip2lRecord := IP2LocationRecord{
			IPFrom:      ipFrom,
			IPTo:        ipTo,
			CountryCode: record[2],
			CountryName: record[3],
			RegionName:  record[4],
			CityName:    record[5],
			Latitude:    latitude,
			Longitude:   longitude,
			ZipCode:     record[8],
			TimeZone:    record[9],
		}

		startIP := decimalToIP(ip2lRecord.IPFrom)
		endIP := decimalToIP(ip2lRecord.IPTo)

		cidrs, err := ipRangeToCIDR(startIP, endIP)
		if err != nil {
			fmt.Printf("Failed to convert IP range to CIDR: %v\n", err)
			continue
		}

		for _, cidrStr := range cidrs {
			_, ipNet, err := net.ParseCIDR(cidrStr)
			if err != nil {
				fmt.Printf("Invalid CIDR %s: %v\n", cidrStr, err)
				continue
			}

			ipInfo := model.IPInfo{
				IPRange: model.IPRange{
					CIDR:      ipNet.String(),
					IPVersion: datasource.GetIPVersion(ipNet),
					StartIP:   ipNet.IP.String(),
					EndIP:     datasource.GetEndIP(ipNet),
					Netmask:   net.IP(ipNet.Mask).String(),
				},
				Geolocation: model.Geolocation{
					Continent: model.Continent{
						Code: "", // IP2LOCATION 不提供洲信息，留空
						Name: "",
					},
					Country: model.Country{
						Code:              ip2lRecord.CountryCode,
						Name:              ip2lRecord.CountryName,
						IsInEuropeanUnion: nil, // IP2LOCATION 不提供，设为 nil
						GeonameID:         nil, // IP2LOCATION 不提供
					},
					Region: model.Region{
						Code:      "", // IP2LOCATION 不提供地区代码
						Name:      ip2lRecord.RegionName,
						GeonameID: nil, // IP2LOCATION 不提供
					},
					Subdivisions:   []model.Subdivision{}, // IP2LOCATION 不提供次级行政区
					City:           ip2lRecord.CityName,
					PostalCode:     ip2lRecord.ZipCode,
					Latitude:       datasource.PtrFloat64(ip2lRecord.Latitude),
					Longitude:      datasource.PtrFloat64(ip2lRecord.Longitude),
					AccuracyRadius: nil, // IP2LOCATION 不提供
					GeonameID:      nil, // IP2LOCATION 不提供
				},
				Network: model.Network{
					ASN:                "", // IP2LOCATION 不提供 ASN
					Organization:       "",
					AutonomousSystemID: nil,
				},
				Timezone: model.Timezone{
					Name: ip2lRecord.TimeZone,
					// Offset 可通过 time.LoadLocation 计算（可选）
				},
				Security: model.Security{
					IsProxy:     nil, // IP2LOCATION 不提供安全信息
					IsAnonymous: nil,
				},
				Metadata: model.Metadata{
					Source:      "ip2location",
					LastUpdated: time.Now().Format(time.RFC3339),
					Confidence:  datasource.PtrInt(80), // 假设默认置信度
				},
				Extended: model.Extended{
					CustomFields: make(map[string]interface{}),
				},
			}
			ipInfos = append(ipInfos, ipInfo)
			processedCount++
		}
	}
	return ipInfos, nil
}

// 解析 ASN 数据库, 字段为: IP-ASN
// ParseIP2ProxyCSV 解析 IP2Proxy LITE 数据库
// 字段: IP-PROXYTYPE-COUNTRY-REGION-CITY-ISP-DOMAIN-USAGETYPE-ASN-LASTSEEN-THREAT-RESIDENTIAL-PROVIDER-FRAUDSCORE
// 注意，IP2Proxy数据可能有 16 列，第 1、2 列为 ip 起止地址？
// 数据类似如下：
// "16812039","16812039","PUB","TH","Thailand","Ratchaburi","Suan Phueng","TOT Public Company Limited","tot.co.th","ISP/MOB","23969","TOT Public Company Limited","1","-","-","36"
// "16812048","16812048","PUB","TH","Thailand","Ratchaburi","Suan Phueng","TOT Public Company Limited","tot.co.th","ISP/MOB","23969","TOT Public Company Limited","1","BOTNET","-","36"
func ParseIP2ProxyCSV(filePath string) ([]model.IPInfo, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	var ipInfos []model.IPInfo

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("Error reading CSV row: %v\n", err)
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}
		utils.ShowProcess(processedCount, nil)
		// 实际是 16 列
		if len(record) != 16 {
			fmt.Printf("Invalid CSV row, expected 16 fields, got %d\n", len(record))
			continue
		}

		// 解析 IP 范围
		ipFrom, err := parseUint32(record[0])
		if err != nil {
			fmt.Printf("Invalid IP_FROM: %v\n", err)
			continue
		}
		ipTo, err := parseUint32(record[1])
		if err != nil {
			fmt.Printf("Invalid IP_TO: %v\n", err)
			continue
		}

		// 解析 FraudScore (第16列，索引15)
		fraudScore, err := parseUint32(record[15])
		if err != nil {
			fmt.Printf("Invalid FRAUDSCORE: %v\n", err)
			continue
		}

		// 定义 IP2Proxy 记录结构体
		type IP2ProxyRecord struct {
			IPFrom      uint32
			IPTo        uint32
			ProxyType   string
			CountryCode string
			CountryName string
			RegionName  string
			CityName    string
			ISP         string
			Domain      string
			UsageType   string
			ASN         string
			LastSeen    string
			Threat      string
			Residential string
			Provider    string
			FraudScore  uint32
		}

		ip2pRecord := IP2ProxyRecord{
			IPFrom:      ipFrom,
			IPTo:        ipTo,
			ProxyType:   record[2],
			CountryCode: record[3],
			CountryName: record[4],
			RegionName:  record[5],
			CityName:    record[6],
			ISP:         record[7],
			Domain:      record[8],
			UsageType:   record[9],
			ASN:         record[10],
			LastSeen:    record[12],
			Threat:      record[13],
			Residential: record[14],
			Provider:    record[11], // 第11列是ISP名称/Provider
			FraudScore:  fraudScore,
		}

		// 转换为 CIDR
		startIP := decimalToIP(ip2pRecord.IPFrom)
		endIP := decimalToIP(ip2pRecord.IPTo)
		cidrs, err := ipRangeToCIDR(startIP, endIP)
		if err != nil {
			fmt.Printf("Failed to convert IP range to CIDR: %v\n", err)
			continue
		}

		for _, cidrStr := range cidrs {
			_, ipNet, err := net.ParseCIDR(cidrStr)
			if err != nil {
				fmt.Printf("Invalid CIDR %s: %v\n", cidrStr, err)
				continue
			}

			// 判断是否为匿名或代理
			isProxy := ip2pRecord.ProxyType != "-" && ip2pRecord.ProxyType != ""
			isAnonymous := isProxy && (ip2pRecord.ProxyType == "VPN" || ip2pRecord.ProxyType == "TOR")

			ipInfo := model.IPInfo{
				IPRange: model.IPRange{
					CIDR:      ipNet.String(),
					IPVersion: datasource.GetIPVersion(ipNet),
					StartIP:   ipNet.IP.String(),
					EndIP:     datasource.GetEndIP(ipNet),
					Netmask:   net.IP(ipNet.Mask).String(),
				},
				Geolocation: model.Geolocation{
					Continent: model.Continent{
						Code: "", // IP2Proxy 不提供洲信息
						Name: "",
					},
					Country: model.Country{
						Code:              ip2pRecord.CountryCode,
						Name:              ip2pRecord.CountryName,
						IsInEuropeanUnion: nil,
						GeonameID:         nil,
					},
					Region: model.Region{
						Code:      "",
						Name:      ip2pRecord.RegionName,
						GeonameID: nil,
					},
					Subdivisions:   []model.Subdivision{},
					City:           ip2pRecord.CityName,
					PostalCode:     "",  // IP2Proxy 不提供邮编
					Latitude:       nil, // IP2Proxy 不提供经纬度
					Longitude:      nil,
					AccuracyRadius: nil,
					GeonameID:      nil,
				},
				Network: model.Network{
					ASN:                ip2pRecord.ASN,
					Organization:       ip2pRecord.ISP,
					AutonomousSystemID: nil, // 可选：如果需要解析 ASN 为数字
				},
				Timezone: model.Timezone{
					Name: "", // IP2Proxy 不提供时区
				},
				Security: model.Security{
					IsProxy:     &isProxy,
					IsAnonymous: &isAnonymous,
				},
				Metadata: model.Metadata{
					Source:      "ip2location",
					LastUpdated: time.Now().Format(time.RFC3339),
					Confidence:  datasource.PtrInt(85), // 假设置信度稍高
				},
				Extended: model.Extended{
					CustomFields: map[string]interface{}{
						"ProxyType":   ip2pRecord.ProxyType,
						"UsageType":   ip2pRecord.UsageType,
						"Domain":      ip2pRecord.Domain,
						"LastSeen":    ip2pRecord.LastSeen,
						"Threat":      ip2pRecord.Threat,
						"Residential": ip2pRecord.Residential,
						"Provider":    ip2pRecord.Provider,
						"FraudScore":  ip2pRecord.FraudScore,
					},
				},
			}
			ipInfos = append(ipInfos, ipInfo)
			processedCount++
		}
	}
	return ipInfos, nil
}

// 解析 IP2Proxy LITE 数据库, 字段为: IP-PROXYTYPE-COUNTRY-REGION-CITY-ISP-DOMAIN-USAGETYPE-ASN-LASTSEEN-THREAT-RESIDENTIAL-PROVIDER-FRAUDSCORE
// ParseIP2ASNCSV 解析 IP2ASN 数据库
// 字段: IP-ASN
// ParseIP2ASNCSV 解析 IP2ASN 数据库
// 字段: IPFrom,IPTo,CIDR,ASN,Organization
func ParseIP2ASNCSV(filePath string) ([]model.IPInfo, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	var ipInfos []model.IPInfo

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()
	processedCount := 0

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("Error reading CSV row: %v\n", err)
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			fmt.Printf("Debug mode: processing limited, processed: %d, limit: %d\n", processedCount, debugLimit)
			break
		}
		utils.ShowProcess(processedCount, nil)
		if len(record) != 5 {
			fmt.Printf("Invalid CSV row, expected 5 fields, got %d\n", len(record))
			continue
		}

		ipFrom, err := parseUint32(record[0])
		if err != nil {
			fmt.Printf("Invalid IP_FROM: %v\n", err)
			continue
		}
		ipTo, err := parseUint32(record[1])
		if err != nil {
			fmt.Printf("Invalid IP_TO: %v\n", err)
			continue
		}

		cidrStr := record[2]
		// 跳过无效的CIDR值
		if cidrStr == "-" || cidrStr == "" {
			continue
		}
		_, ipNet, err := net.ParseCIDR(cidrStr)
		if err != nil {
			fmt.Printf("Invalid CIDR %s: %v\n", cidrStr, err)
			continue
		}

		asn := record[3]
		organization := record[4]
		if asn == "-" {
			asn = "" // 转换为空字符串，保持一致性
		}
		if organization == "-" {
			organization = ""
		}

		type IP2ASNRecord struct {
			IPFrom       uint32
			IPTo         uint32
			CIDR         string
			ASN          string
			Organization string
		}

		ip2asnRecord := IP2ASNRecord{
			IPFrom:       ipFrom,
			IPTo:         ipTo,
			CIDR:         cidrStr,
			ASN:          asn,
			Organization: organization,
		}

		startIP := decimalToIP(ip2asnRecord.IPFrom)
		endIP := decimalToIP(ip2asnRecord.IPTo)
		if !ipNet.Contains(net.ParseIP(startIP)) || !ipNet.Contains(net.ParseIP(endIP)) {
			fmt.Printf("CIDR %s does not match IP range %s-%s\n", cidrStr, startIP, endIP)
			continue
		}

		ipInfo := model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      ipNet.String(),
				IPVersion: datasource.GetIPVersion(ipNet),
				StartIP:   ipNet.IP.String(),
				EndIP:     datasource.GetEndIP(ipNet),
				Netmask:   net.IP(ipNet.Mask).String(),
			},
			Geolocation: model.Geolocation{
				Continent: model.Continent{
					Code: "",
					Name: "",
				},
				Country: model.Country{
					Code:              "",
					Name:              "",
					IsInEuropeanUnion: nil,
					GeonameID:         nil,
				},
				Region: model.Region{
					Code:      "",
					Name:      "",
					GeonameID: nil,
				},
				Subdivisions:   []model.Subdivision{},
				City:           "",
				PostalCode:     "",
				Latitude:       nil,
				Longitude:      nil,
				AccuracyRadius: nil,
				GeonameID:      nil,
			},
			Network: model.Network{
				ASN:                ip2asnRecord.ASN,
				Organization:       ip2asnRecord.Organization,
				AutonomousSystemID: nil, // 可选：如果需要将 ASN 解析为数字
			},
			Timezone: model.Timezone{
				Name: "",
			},
			Security: model.Security{
				IsProxy:     nil,
				IsAnonymous: nil,
			},
			Metadata: model.Metadata{
				Source:      "ip2asn",
				LastUpdated: time.Now().Format(time.RFC3339),
				Confidence:  datasource.PtrInt(90), // ASN 数据置信度较高
			},
			Extended: model.Extended{
				CustomFields: make(map[string]interface{}),
			},
		}
		ipInfos = append(ipInfos, ipInfo)
		processedCount++
	}
	return ipInfos, nil
}

// 辅助函数：将十进制 IP 转换为 IPv4 地址
func decimalToIP(ipNum uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		ipNum>>24&0xff, ipNum>>16&0xff, ipNum>>8&0xff, ipNum&0xff)
}

// 辅助函数：将 IP 范围转换为 CIDR
func ipRangeToCIDR(startIP, endIP string) ([]string, error) {
	start, err := netaddr.ParseIP(startIP)
	if err != nil {
		return nil, fmt.Errorf("invalid start IP %s: %w", startIP, err)
	}
	end, err := netaddr.ParseIP(endIP)
	if err != nil {
		return nil, fmt.Errorf("invalid end IP %s: %w", endIP, err)
	}

	if start.Compare(end) > 0 {
		return nil, fmt.Errorf("start IP %s is greater than end IP %s", startIP, endIP)
	}
	r := netaddr.IPRangeFrom(start, end)
	cidrSet := r.Prefixes()

	var cidrStrs []string
	for _, prefix := range cidrSet {
		cidrStrs = append(cidrStrs, prefix.String())
	}
	return cidrStrs, nil
}

// 辅助函数：解析 uint32
func parseUint32(s string) (uint32, error) {
	n, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint32(n), nil
}

// 辅助函数：解析 float64
func parseFloat64(s string) (float64, error) {
	return strconv.ParseFloat(s, 64)
}
