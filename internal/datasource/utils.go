package datasource

import (
	"fmt"
	"net"
	"sort"

	"github.com/cosin2077/ipInsight/internal/model"
	"inet.af/netaddr"
)

func MergeIPInfos(infos []model.IPInfo) []model.IPInfo {
	ipMap := make(map[string]model.IPInfo)

	// 按 CIDR 聚合
	for _, info := range infos {
		existing, exists := ipMap[info.IPRange.CIDR]
		if !exists {
			ipMap[info.IPRange.CIDR] = info
			continue
		}

		// 冲突解决：优先级 MaxMind > IP2Location > DB-IP
		sourcePriority := map[string]int{
			"maxmind":     4,
			"ip2location": 3,
			"dbip":        2,
		}
		currentPriority := sourcePriority[info.Metadata.Source]
		existingPriority := sourcePriority[existing.Metadata.Source]

		if currentPriority > existingPriority || (currentPriority == existingPriority && info.Metadata.Confidence != nil && existing.Metadata.Confidence != nil && *info.Metadata.Confidence > *existing.Metadata.Confidence) {
			ipMap[info.IPRange.CIDR] = info
		} else if currentPriority == existingPriority && info.Metadata.Confidence != nil && existing.Metadata.Confidence != nil && *info.Metadata.Confidence == *existing.Metadata.Confidence {
			// 合并字段
			if existing.Geolocation.City == "" && info.Geolocation.City != "" {
				existing.Geolocation.City = info.Geolocation.City
			}
			if existing.Geolocation.Latitude == nil && info.Geolocation.Latitude != nil {
				existing.Geolocation.Latitude = info.Geolocation.Latitude
				existing.Geolocation.Longitude = info.Geolocation.Longitude
			}
			if existing.Timezone.Name == "" && info.Timezone.Name != "" {
				existing.Timezone.Name = info.Timezone.Name
			}
			if info.Metadata.LastUpdated > existing.Metadata.LastUpdated {
				existing.Metadata.LastUpdated = info.Metadata.LastUpdated
			}
			ipMap[info.IPRange.CIDR] = existing
		}
	}

	// 转换为列表
	var result []model.IPInfo
	for _, info := range ipMap {
		result = append(result, info)
	}

	// 按 CIDR 排序
	sort.Slice(result, func(i, j int) bool {
		return result[i].IPRange.CIDR < result[j].IPRange.CIDR
	})

	return result
}

// RangeToCIDR converts an IP range (startIP to endIP) to a list of CIDR blocks, supports IPv4/IPv6
func RangeToCIDR(startIP, endIP string) ([]string, error) {
	start, err := netaddr.ParseIP(startIP)
	if err != nil {
		return nil, fmt.Errorf("invalid start IP %s: %w", startIP, err)
	}
	end, err := netaddr.ParseIP(endIP)
	if err != nil {
		return nil, fmt.Errorf("invalid end IP %s: %w", endIP, err)
	}
	if start.Compare(end) > 0 {
		return nil, fmt.Errorf("start IP %s is greater than end IP %s", startIP, endIP)
	}
	r := netaddr.IPRangeFrom(start, end)
	cidrSet := r.Prefixes()
	var cidrStrs []string
	for _, prefix := range cidrSet {
		cidrStrs = append(cidrStrs, prefix.String())
	}
	return cidrStrs, nil
}

// IntToIP 支持 IPv4/IPv6，将 int64/uint64 转为 net.IP
func IntToIP(i uint64, isIPv6 bool) net.IP {
	if isIPv6 {
		b := make([]byte, 16)
		for j := 15; j >= 0; j-- {
			b[j] = byte(i & 0xff)
			i >>= 8
		}
		return net.IP(b)
	}
	return net.IPv4(byte(i>>24), byte(i>>16), byte(i>>8), byte(i)).To4()
}
