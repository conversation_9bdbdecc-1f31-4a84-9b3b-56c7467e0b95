package datasource

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"go.uber.org/zap"
)

type FileProcessor struct {
	logger *zap.Logger
}

func NewFileProcessor(logger *zap.Logger) *FileProcessor {
	return &FileProcessor{
		logger: logger,
	}
}

// FileType 文件类型枚举
type FileType int

const (
	FileTypeUnknown FileType = iota
	FileTypeCSV
	FileTypeMMDB
	FileTypeZip
	FileTypeTarGz
	FileTypeGz
	FileTypeTar
)

// DetectFileType 检测文件类型
func (fp *FileProcessor) DetectFileType(filename string) FileType {
	filename = strings.ToLower(filename)

	if strings.HasSuffix(filename, ".csv") {
		return FileTypeCSV
	}
	if strings.HasSuffix(filename, ".mmdb") {
		return FileTypeMMDB
	}
	if strings.HasSuffix(filename, ".zip") {
		return FileTypeZip
	}
	if strings.HasSuffix(filename, ".tar.gz") || strings.HasSuffix(filename, ".tgz") {
		return FileTypeTarGz
	}
	if strings.HasSuffix(filename, ".gz") {
		return FileTypeGz
	}
	if strings.HasSuffix(filename, ".tar") {
		return FileTypeTar
	}

	return FileTypeUnknown
}

// ProcessFile 处理文件（解压缩等）
func (fp *FileProcessor) ProcessFile(filePath, outputDir string) ([]string, error) {
	fileType := fp.DetectFileType(filePath)

	switch fileType {
	case FileTypeCSV, FileTypeMMDB:
		// 直接返回原文件路径
		return []string{filePath}, nil
	case FileTypeZip:
		return fp.extractZip(filePath, outputDir)
	case FileTypeTarGz:
		return fp.extractTarGz(filePath, outputDir)
	case FileTypeGz:
		return fp.extractGz(filePath, outputDir)
	case FileTypeTar:
		return fp.extractTar(filePath, outputDir)
	default:
		return nil, fmt.Errorf("unsupported file type: %s", filePath)
	}
}

// extractZip 解压ZIP文件
func (fp *FileProcessor) extractZip(zipPath, outputDir string) ([]string, error) {
	fp.logger.Info("Extracting ZIP file", zap.String("file", zipPath))

	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	var extractedFiles []string

	for _, file := range reader.File {
		if file.FileInfo().IsDir() {
			continue
		}

		extractedPath := filepath.Join(outputDir, file.Name)
		if err := os.MkdirAll(filepath.Dir(extractedPath), 0755); err != nil {
			return nil, err
		}

		if err := fp.extractZipFile(file, extractedPath); err != nil {
			fp.logger.Error("Failed to extract file", zap.String("file", file.Name), zap.Error(err))
			continue
		}

		extractedFiles = append(extractedFiles, extractedPath)
		fp.logger.Debug("Extracted file", zap.String("file", extractedPath))
	}

	return extractedFiles, nil
}

// extractZipFile 解压单个ZIP文件
func (fp *FileProcessor) extractZipFile(file *zip.File, extractedPath string) error {
	rc, err := file.Open()
	if err != nil {
		return err
	}
	defer rc.Close()

	outFile, err := os.Create(extractedPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	_, err = io.Copy(outFile, rc)
	return err
}

// extractTarGz 解压tar.gz文件
func (fp *FileProcessor) extractTarGz(tarGzPath, outputDir string) ([]string, error) {
	fp.logger.Info("Extracting tar.gz file", zap.String("file", tarGzPath))

	file, err := os.Open(tarGzPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, err
	}
	defer gzReader.Close()

	return fp.extractTarReader(tar.NewReader(gzReader), outputDir)
}

// extractTar 解压tar文件
func (fp *FileProcessor) extractTar(tarPath, outputDir string) ([]string, error) {
	fp.logger.Info("Extracting tar file", zap.String("file", tarPath))

	file, err := os.Open(tarPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return fp.extractTarReader(tar.NewReader(file), outputDir)
}

// extractTarReader 从tar reader解压文件
func (fp *FileProcessor) extractTarReader(tarReader *tar.Reader, outputDir string) ([]string, error) {
	var extractedFiles []string

	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		if header.Typeflag != tar.TypeReg {
			continue
		}

		extractedPath := filepath.Join(outputDir, header.Name)
		if err := os.MkdirAll(filepath.Dir(extractedPath), 0755); err != nil {
			return nil, err
		}

		outFile, err := os.Create(extractedPath)
		if err != nil {
			fp.logger.Error("Failed to create file", zap.String("file", extractedPath), zap.Error(err))
			continue
		}

		if _, err := io.Copy(outFile, tarReader); err != nil {
			outFile.Close()
			fp.logger.Error("Failed to extract file", zap.String("file", extractedPath), zap.Error(err))
			continue
		}
		outFile.Close()

		extractedFiles = append(extractedFiles, extractedPath)
		fp.logger.Debug("Extracted file", zap.String("file", extractedPath))
	}

	return extractedFiles, nil
}

// extractGz 解压gz文件
func (fp *FileProcessor) extractGz(gzPath, outputDir string) ([]string, error) {
	fp.logger.Info("Extracting gz file", zap.String("file", gzPath))

	file, err := os.Open(gzPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, err
	}
	defer gzReader.Close()

	// 生成输出文件名（去掉.gz后缀）
	outputFileName := strings.TrimSuffix(filepath.Base(gzPath), ".gz")
	outputPath := filepath.Join(outputDir, outputFileName)

	outFile, err := os.Create(outputPath)
	if err != nil {
		return nil, err
	}
	defer outFile.Close()

	if _, err := io.Copy(outFile, gzReader); err != nil {
		return nil, err
	}

	return []string{outputPath}, nil
}

// FilterFilesByType 根据文件类型过滤文件列表
func (fp *FileProcessor) FilterFilesByType(files []string, targetTypes ...FileType) []string {
	var filtered []string

	for _, file := range files {
		fileType := fp.DetectFileType(file)
		for _, targetType := range targetTypes {
			if fileType == targetType {
				filtered = append(filtered, file)
				break
			}
		}
	}

	return filtered
}
