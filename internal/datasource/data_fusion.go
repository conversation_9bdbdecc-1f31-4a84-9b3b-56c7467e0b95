package datasource

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
)

type DataFusionEngine struct {
	logger       *zap.Logger
	apiValidator APIValidator // 接口类型，避免循环依赖
}

// APIValidator 接口定义，避免循环依赖
type APIValidator interface {
	ValidateIPInfo(ctx context.Context, ip string, conflictData []model.IPInfo) (*model.IPInfo, error)
}

type SourcePriority struct {
	Source   string
	Priority int // 数字越大优先级越高
}

// 数据源优先级配置
var DefaultSourcePriorities = []SourcePriority{
	{"maxmind", 90},       // MaxMind 数据质量最高
	{"ip2location", 85},   // IP2Location 商业数据源
	{"iplocate", 80},      // IPLocate MMDB数据
	{"qqwry", 78},         // QQWry 纯真IP数据库（中文地理位置优势）
	{"dbip", 75},          // DB-IP 免费数据
	{"ipapi", 70},         // IP-API 免费API
	{"ipinfodb", 60},      // IPInfoDB 数据
	{"ipinfo", 55},        // IPInfo API（免费版限制较多）
	{"ipgeolocation", 50}, // IPGeolocation API
	{"ipstack", 45},       // IPStack API
}

func NewDataFusionEngine(logger *zap.Logger) *DataFusionEngine {
	return &DataFusionEngine{
		logger: logger,
	}
}

// SetAPIValidator 设置API验证器
func (dfe *DataFusionEngine) SetAPIValidator(validator APIValidator) {
	dfe.apiValidator = validator
}

// FuseIPInfos 融合多个IPInfo数据
func (dfe *DataFusionEngine) FuseIPInfos(ipInfos []model.IPInfo) model.IPInfo {
	if len(ipInfos) == 0 {
		return model.IPInfo{}
	}

	if len(ipInfos) == 1 {
		return ipInfos[0]
	}

	// 检测数据冲突
	conflicts := dfe.detectConflicts(ipInfos)

	// 如果有严重冲突且配置了API验证器，进行API交叉验证
	if len(conflicts) > 0 && dfe.apiValidator != nil {
		ip := dfe.extractIP(ipInfos[0])
		if ip != "" {
			dfe.logger.Info("Detected data conflicts, performing API validation",
				zap.String("ip", ip),
				zap.Int("conflicts", len(conflicts)))

			// 使用context.Background()，实际使用时应该传入合适的context
			if validatedInfo, err := dfe.apiValidator.ValidateIPInfo(context.Background(), ip, ipInfos); err == nil {
				dfe.logger.Info("API validation successful", zap.String("ip", ip))
				return *validatedInfo
			} else {
				dfe.logger.Warn("API validation failed, using standard fusion",
					zap.String("ip", ip),
					zap.Error(err))
			}
		}
	}

	// 按优先级排序
	sortedInfos := dfe.sortByPriority(ipInfos)

	// 以最高优先级的数据为基础
	result := sortedInfos[0]

	// 逐个融合其他数据源的信息
	for i := 1; i < len(sortedInfos); i++ {
		result = dfe.mergeIPInfo(result, sortedInfos[i])
	}

	// 更新元数据
	result.Metadata.LastUpdated = time.Now().Format(time.RFC3339)
	result.Metadata.Source = dfe.buildSourceList(sortedInfos)

	return result
}

// sortByPriority 按优先级排序IPInfo
func (dfe *DataFusionEngine) sortByPriority(ipInfos []model.IPInfo) []model.IPInfo {
	priorityMap := make(map[string]int)
	for _, sp := range DefaultSourcePriorities {
		priorityMap[sp.Source] = sp.Priority
	}

	// 创建副本并排序
	sorted := make([]model.IPInfo, len(ipInfos))
	copy(sorted, ipInfos)

	// 简单的冒泡排序（按优先级降序）
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-i-1; j++ {
			priority1 := priorityMap[sorted[j].Metadata.Source]
			priority2 := priorityMap[sorted[j+1].Metadata.Source]
			if priority1 < priority2 {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}

	return sorted
}

// mergeIPInfo 合并两个IPInfo，优先保留base中的非空数据
func (dfe *DataFusionEngine) mergeIPInfo(base, additional model.IPInfo) model.IPInfo {
	result := base

	// 融合地理位置信息
	result.Geolocation = dfe.mergeGeolocation(base.Geolocation, additional.Geolocation)

	// 融合网络信息
	result.Network = dfe.mergeNetwork(base.Network, additional.Network)

	// 融合时区信息
	result.Timezone = dfe.mergeTimezone(base.Timezone, additional.Timezone)

	// 融合安全信息
	result.Security = dfe.mergeSecurity(base.Security, additional.Security)

	// 融合扩展信息
	result.Extended = dfe.mergeExtended(base.Extended, additional.Extended)

	// 更新置信度（取较高值）
	if additional.Metadata.Confidence != nil {
		if result.Metadata.Confidence == nil || *additional.Metadata.Confidence > *result.Metadata.Confidence {
			result.Metadata.Confidence = additional.Metadata.Confidence
		}
	}

	return result
}

// mergeGeolocation 融合地理位置信息
func (dfe *DataFusionEngine) mergeGeolocation(base, additional model.Geolocation) model.Geolocation {
	result := base

	// 大洲信息
	if result.Continent.Code == "" && additional.Continent.Code != "" {
		result.Continent.Code = additional.Continent.Code
	}
	if result.Continent.Name == "" && additional.Continent.Name != "" {
		result.Continent.Name = additional.Continent.Name
	}

	// 国家信息
	if result.Country.Code == "" && additional.Country.Code != "" {
		result.Country.Code = additional.Country.Code
	}
	if result.Country.Name == "" && additional.Country.Name != "" {
		result.Country.Name = additional.Country.Name
	}
	if result.Country.IsInEuropeanUnion == nil && additional.Country.IsInEuropeanUnion != nil {
		result.Country.IsInEuropeanUnion = additional.Country.IsInEuropeanUnion
	}
	if result.Country.GeonameID == nil && additional.Country.GeonameID != nil {
		result.Country.GeonameID = additional.Country.GeonameID
	}

	// 地区信息
	if result.Region.Code == "" && additional.Region.Code != "" {
		result.Region.Code = additional.Region.Code
	}
	if result.Region.Name == "" && additional.Region.Name != "" {
		result.Region.Name = additional.Region.Name
	}
	if result.Region.GeonameID == nil && additional.Region.GeonameID != nil {
		result.Region.GeonameID = additional.Region.GeonameID
	}

	// 城市信息
	if result.City == "" && additional.City != "" {
		result.City = additional.City
	}

	// 邮政编码
	if result.PostalCode == "" && additional.PostalCode != "" {
		result.PostalCode = additional.PostalCode
	}

	// 坐标信息（优先使用精度更高的）
	if result.Latitude == nil && additional.Latitude != nil {
		result.Latitude = additional.Latitude
	}
	if result.Longitude == nil && additional.Longitude != nil {
		result.Longitude = additional.Longitude
	}

	// 精度半径（取较小值，表示更精确）
	if result.AccuracyRadius == nil && additional.AccuracyRadius != nil {
		result.AccuracyRadius = additional.AccuracyRadius
	} else if result.AccuracyRadius != nil && additional.AccuracyRadius != nil {
		if *additional.AccuracyRadius < *result.AccuracyRadius {
			result.AccuracyRadius = additional.AccuracyRadius
		}
	}

	// GeonameID
	if result.GeonameID == nil && additional.GeonameID != nil {
		result.GeonameID = additional.GeonameID
	}

	// 细分区域信息
	if len(result.Subdivisions) == 0 && len(additional.Subdivisions) > 0 {
		result.Subdivisions = additional.Subdivisions
	}

	return result
}

// mergeNetwork 融合网络信息
func (dfe *DataFusionEngine) mergeNetwork(base, additional model.Network) model.Network {
	result := base

	if result.ISP == "" && additional.ISP != "" {
		result.ISP = additional.ISP
	}
	if result.ASN == "" && additional.ASN != "" {
		result.ASN = additional.ASN
	}
	if result.Organization == "" && additional.Organization != "" {
		result.Organization = additional.Organization
	}
	if result.Domain == "" && additional.Domain != "" {
		result.Domain = additional.Domain
	}
	if result.ConnectionType == "" && additional.ConnectionType != "" {
		result.ConnectionType = additional.ConnectionType
	}
	if result.MCC == "" && additional.MCC != "" {
		result.MCC = additional.MCC
	}
	if result.MNC == "" && additional.MNC != "" {
		result.MNC = additional.MNC
	}
	if result.Carrier == "" && additional.Carrier != "" {
		result.Carrier = additional.Carrier
	}
	if result.HostingProvider == "" && additional.HostingProvider != "" {
		result.HostingProvider = additional.HostingProvider
	}
	if result.AutonomousSystemID == nil && additional.AutonomousSystemID != nil {
		result.AutonomousSystemID = additional.AutonomousSystemID
	}

	return result
}

// mergeTimezone 融合时区信息
func (dfe *DataFusionEngine) mergeTimezone(base, additional model.Timezone) model.Timezone {
	result := base

	if result.Name == "" && additional.Name != "" {
		result.Name = additional.Name
	}
	if result.Offset == "" && additional.Offset != "" {
		result.Offset = additional.Offset
	}

	return result
}

// mergeSecurity 融合安全信息
func (dfe *DataFusionEngine) mergeSecurity(base, additional model.Security) model.Security {
	result := base

	// 对于布尔值，优先使用非nil的值
	if result.IsProxy == nil && additional.IsProxy != nil {
		result.IsProxy = additional.IsProxy
	}
	if result.ProxyType == "" && additional.ProxyType != "" {
		result.ProxyType = additional.ProxyType
	}
	if result.ThreatLevel == "" && additional.ThreatLevel != "" {
		result.ThreatLevel = additional.ThreatLevel
	}
	if result.IsTor == nil && additional.IsTor != nil {
		result.IsTor = additional.IsTor
	}
	if result.IsVPN == nil && additional.IsVPN != nil {
		result.IsVPN = additional.IsVPN
	}
	if result.IsDataCenter == nil && additional.IsDataCenter != nil {
		result.IsDataCenter = additional.IsDataCenter
	}
	if result.IsAnonymous == nil && additional.IsAnonymous != nil {
		result.IsAnonymous = additional.IsAnonymous
	}
	if result.IsBot == nil && additional.IsBot != nil {
		result.IsBot = additional.IsBot
	}
	if result.ThreatScore == nil && additional.ThreatScore != nil {
		result.ThreatScore = additional.ThreatScore
	}

	return result
}

// mergeExtended 融合扩展信息
func (dfe *DataFusionEngine) mergeExtended(base, additional model.Extended) model.Extended {
	result := base

	// 货币信息
	if result.Currency.Code == "" && additional.Currency.Code != "" {
		result.Currency.Code = additional.Currency.Code
	}
	if result.Currency.Name == "" && additional.Currency.Name != "" {
		result.Currency.Name = additional.Currency.Name
	}

	// 语言信息
	if len(result.Languages) == 0 && len(additional.Languages) > 0 {
		result.Languages = additional.Languages
	}

	// 自定义字段
	if result.CustomFields == nil {
		result.CustomFields = make(map[string]interface{})
	}
	for key, value := range additional.CustomFields {
		if _, exists := result.CustomFields[key]; !exists {
			result.CustomFields[key] = value
		}
	}

	// 人口信息
	if result.Population == nil && additional.Population != nil {
		result.Population = additional.Population
	}

	// 电话区号
	if result.CallingCode == "" && additional.CallingCode != "" {
		result.CallingCode = additional.CallingCode
	}

	// 国旗
	if result.Flag == "" && additional.Flag != "" {
		result.Flag = additional.Flag
	}

	return result
}

// buildSourceList 构建数据源列表
func (dfe *DataFusionEngine) buildSourceList(ipInfos []model.IPInfo) string {
	var sources []string
	seen := make(map[string]bool)

	for _, info := range ipInfos {
		if !seen[info.Metadata.Source] {
			sources = append(sources, info.Metadata.Source)
			seen[info.Metadata.Source] = true
		}
	}

	return fmt.Sprintf("fused(%s)", strings.Join(sources, ","))
}

// detectConflicts 检测数据冲突
func (dfe *DataFusionEngine) detectConflicts(ipInfos []model.IPInfo) []string {
	var conflicts []string

	if len(ipInfos) < 2 {
		return conflicts
	}

	// 检查国家代码冲突
	countrySet := make(map[string]bool)
	for _, info := range ipInfos {
		if info.Geolocation.Country.Code != "" {
			countrySet[info.Geolocation.Country.Code] = true
		}
	}
	if len(countrySet) > 1 {
		conflicts = append(conflicts, "country_code")
	}

	// 检查ISP冲突
	ispSet := make(map[string]bool)
	for _, info := range ipInfos {
		if info.Network.ISP != "" {
			ispSet[info.Network.ISP] = true
		}
	}
	if len(ispSet) > 1 {
		conflicts = append(conflicts, "isp")
	}

	// 检查城市冲突
	citySet := make(map[string]bool)
	for _, info := range ipInfos {
		if info.Geolocation.City != "" {
			citySet[info.Geolocation.City] = true
		}
	}
	if len(citySet) > 1 {
		conflicts = append(conflicts, "city")
	}

	return conflicts
}

// extractIP 从IPInfo中提取IP地址
func (dfe *DataFusionEngine) extractIP(ipInfo model.IPInfo) string {
	if ipInfo.IPRange.StartIP != "" {
		return ipInfo.IPRange.StartIP
	}

	// 如果有CIDR，尝试提取IP
	if ipInfo.IPRange.CIDR != "" {
		// 简单处理：取CIDR的网络地址
		parts := strings.Split(ipInfo.IPRange.CIDR, "/")
		if len(parts) > 0 {
			return parts[0]
		}
	}

	return ""
}
