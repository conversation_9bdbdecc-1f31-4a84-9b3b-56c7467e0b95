package ipinfodb

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestIPInfoDBParse(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	dataDir := "./testdata"
	os.Mkdir<PERSON>ll(dataDir, 0755)
	defer os.RemoveAll(dataDir)

	csvContent := `16777216,16777471,"AU","Australia","Queensland","Brisbane","-27.4705","153.0260","4000","Cloudflare"`
	csvFile := filepath.Join(dataDir, "ipinfodb.csv")
	os.WriteFile(csvFile, []byte(csvContent), 0644)

	ipinfodb := &IPInfoDB{
		config:  config.DatasourceConfig{URL: []string{}, Schedule: ""},
		logger:  logger,
		dataDir: dataDir,
	}

	ipInfos, err := ipinfodb.Parse(context.Background())
	assert.NoError(t, err)
	assert.Len(t, ipInfos, 1)

	ipInfo := ipInfos[0]
	assert.Equal(t, "*******", ipInfo.IPRange.StartIP)
	assert.Equal(t, "*********", ipInfo.IPRange.EndIP)
	assert.Equal(t, "AU", ipInfo.Geolocation.Country.Code)
	assert.Equal(t, "Australia", ipInfo.Geolocation.Country.Name)
	assert.Equal(t, "Queensland", ipInfo.Geolocation.Region.Name)
	assert.Equal(t, "Brisbane", ipInfo.Geolocation.City)
	assert.Equal(t, "4000", ipInfo.Geolocation.PostalCode)
	assert.Equal(t, -27.4705, ipInfo.Geolocation.Latitude)
	assert.Equal(t, 153.0260, ipInfo.Geolocation.Longitude)
	assert.Equal(t, "Cloudflare", ipInfo.Network.ISP)
	assert.Equal(t, "ipinfodb", ipInfo.Metadata.Source)
}
