package ipinfodb

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/utils"
	"github.com/klauspost/compress/zip"
	"go.uber.org/zap"
)

// 添加流式处理接口标识
type StreamProcessor interface {
	ParseAndUpdate(ctx context.Context) error
}

type IPInfoDB struct {
	config    config.DatasourceConfig
	logger    *zap.Logger
	dataDir   string
	dbAdapter database.DatabaseInterface // 共享数据库接口
}

func NewIPInfoDB(config config.DatasourceConfig, logger *zap.Logger, dbAdapter database.DatabaseInterface) *IPInfoDB {
	return &IPInfoDB{
		config:    config,
		logger:    logger,
		dataDir:   "./data/ipinfodb",
		dbAdapter: dbAdapter,
	}
}

func (i *IPInfoDB) Name() string {
	return "ipinfodb"
}

func (i *IPInfoDB) Fetch(ctx context.Context) error {
	if err := os.MkdirAll(i.dataDir, 0755); err != nil {
		i.logger.Error("Failed to create data directory", zap.Error(err))
		return err
	}

	resp, err := http.Get(i.config.URL[0])
	if err != nil {
		i.logger.Error("Failed to fetch IPInfoDB data", zap.Error(err))
		return err
	}
	defer resp.Body.Close()

	zipPath := filepath.Join(i.dataDir, "ipinfodb.zip")
	out, err := os.Create(zipPath)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}

	return i.unzip(zipPath, i.dataDir)
}

func (i *IPInfoDB) unzip(zipPath, destDir string) error {
	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return err
	}
	defer r.Close()

	for _, f := range r.File {
		fpath := filepath.Join(destDir, f.Name)
		if !strings.HasPrefix(fpath, filepath.Clean(destDir)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path: %s", fpath)
		}

		if f.FileInfo().IsDir() {
			os.MkdirAll(fpath, os.ModePerm)
			continue
		}

		if err := os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
			return err
		}

		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return err
		}

		rc, err := f.Open()
		if err != nil {
			return err
		}

		_, err = io.Copy(outFile, rc)
		outFile.Close()
		rc.Close()

		if err != nil {
			return err
		}
	}
	return nil
}

func (i *IPInfoDB) Parse(ctx context.Context) ([]model.IPInfo, error) {
	// 不再返回大量数据，改为直接处理
	return nil, fmt.Errorf("Parse method deprecated, use ParseAndUpdate for stream processing")
}

// ParseAndUpdate 流式解析并直接更新数据库，避免OOM
func (i *IPInfoDB) ParseAndUpdate(ctx context.Context) error {
	csvFile := filepath.Join(i.dataDir, "ipinfodb.csv")
	return i.parseCSVFileStream(ctx, csvFile, i.dbAdapter)
}

// parseCSVFileStream 流式解析CSV文件并直接写入数据库
func (i *IPInfoDB) parseCSVFileStream(ctx context.Context, csvFile string, dbAdapter database.DatabaseInterface) error {
	file, err := os.Open(csvFile)
	if err != nil {
		i.logger.Error("Failed to open file", zap.String("datasource", i.Name()), zap.String("file", csvFile), zap.Error(err))
		return fmt.Errorf("parse ipinfodb %s: %w", csvFile, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	
	// 批量处理设置
	batchSize := 1000
	batch := make([]model.IPInfo, 0, batchSize)
	processedCount := 0
	totalBatches := 0

	// 调试模式限制（-1表示无限制）
	debugLimit := utils.SetDebugLimit()

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			i.logger.Warn("CSV row parsing failed, skipping row",
				zap.String("file", csvFile),
				zap.Error(err))
			continue
		}

		// 调试模式限制
		if debugLimit > 0 && processedCount >= debugLimit {
			i.logger.Info("Debug mode: processing limited",
				zap.String("file", csvFile),
				zap.Int("processed", processedCount),
				zap.Int("debug_limit", debugLimit))
			break
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		utils.ShowProcess(processedCount, nil)
		if len(record) < 10 {
			i.logger.Warn("Invalid record length", zap.String("datasource", i.Name()), zap.String("file", csvFile), zap.Int("len", len(record)))
			continue
		}
		startIP, err1 := strconv.ParseInt(record[0], 10, 64)
		endIP, err2 := strconv.ParseInt(record[1], 10, 64)
		if err1 != nil || err2 != nil {
			i.logger.Warn("Invalid IP int", zap.String("datasource", i.Name()), zap.String("file", csvFile), zap.Int64("startIP", startIP), zap.Int64("endIP", endIP), zap.Error(err1), zap.Error(err2))
			continue
		}
		latitude, _ := strconv.ParseFloat(record[6], 64)
		longitude, _ := strconv.ParseFloat(record[7], 64)
		startIPStr := datasource.IntToIP(uint64(startIP), false).String()
		endIPStr := datasource.IntToIP(uint64(endIP), false).String()

		// 转换IP范围为CIDR
		cidrs, err := datasource.RangeToCIDR(startIPStr, endIPStr)
		if err != nil {
			i.logger.Warn("Failed to convert IP range to CIDR", zap.Error(err))
			continue
		}

		// 为每个CIDR创建IPInfo记录
		for _, cidr := range cidrs {
			ipInfo := model.IPInfo{
				IPRange: model.IPRange{
					CIDR:      cidr,
					StartIP:   startIPStr,
					EndIP:     endIPStr,
					IPVersion: "IPv4",
				},
				Geolocation: model.Geolocation{
					Country: model.Country{
						Code: record[2],
						Name: record[3],
					},
					Region: model.Region{
						Name: record[4],
					},
					City:       record[5],
					PostalCode: record[8],
					Latitude:   &latitude,
					Longitude:  &longitude,
				},
				Network: model.Network{
					ISP: record[9],
				},
				Metadata: model.Metadata{
					Source:      "ipinfodb",
					LastUpdated: time.Now().Format(time.RFC3339),
					Confidence:  ptrInt(75),
				},
			}
			batch = append(batch, ipInfo)
		}
		processedCount++

		// 当批次满了，写入数据库
		if len(batch) >= batchSize {
			_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
			if err != nil {
				return fmt.Errorf("failed to write batch to database: %w", err)
			}

			totalBatches++
			i.logger.Info("Wrote IPInfoDB batch to database",
				zap.Int("batch_number", totalBatches),
				zap.Int("batch_size", len(batch)),
				zap.Int("total_processed", processedCount))

			// 重置批次，释放内存
			batch = make([]model.IPInfo, 0, batchSize)
		}

		// 每处理1万条记录报告一次进度
		if processedCount%10000 == 0 {
			i.logger.Info("IPInfoDB parsing progress",
				zap.String("file", csvFile),
				zap.Int("processed", processedCount))
		}
	}

	// 处理最后一批数据
	if len(batch) > 0 {
		_, err := dbAdapter.BatchUpsertIPs(ctx, batch)
		if err != nil {
			return fmt.Errorf("failed to write final batch to database: %w", err)
		}
		totalBatches++
		i.logger.Info("Wrote final IPInfoDB batch to database",
			zap.Int("batch_number", totalBatches),
			zap.Int("batch_size", len(batch)),
			zap.Int("total_processed", processedCount))
	}

	i.logger.Info("Completed IPInfoDB streaming processing",
		zap.String("file", csvFile),
		zap.Int("total_records", processedCount),
		zap.Int("total_batches", totalBatches))

	return nil
}

func (i *IPInfoDB) Update(ctx context.Context, data []model.IPInfo) error {
	// 使用共享的数据库适配器
	_, err := i.dbAdapter.BatchUpsertIPs(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to batch upsert IP data: %w", err)
	}

	i.logger.Info("Successfully updated IPInfoDB data",
		zap.Int("records", len(data)),
		zap.String("source", "ipinfodb"))

	return nil
}

func ptrInt(i int) *int {
	return &i
}
