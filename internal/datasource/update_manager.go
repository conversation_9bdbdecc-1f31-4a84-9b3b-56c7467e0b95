package datasource

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
)

type UpdateManager struct {
	datasources   []Datasource
	db            database.DatabaseInterface // 使用接口
	fileProcessor *FileProcessor
	fusionEngine  *DataFusionEngine
	logger        *zap.Logger
	config        *config.Config
	updateStatus  map[string]*UpdateStatus
	statusMutex   sync.RWMutex
}

// APIValidatorInterface 定义API验证器接口，避免循环依赖
type APIValidatorInterface interface {
	ValidateIPInfo(ctx context.Context, ip string, conflictData []model.IPInfo) (*model.IPInfo, error)
}

// NewUpdateManager 创建标准更新管理器
func NewUpdateManager(datasources []Datasource, db database.DatabaseInterface, fileProcessor *FileProcessor, fusionEngine *DataFusionEngine, logger *zap.Logger, config *config.Config) *UpdateManager {
	return &UpdateManager{
		datasources:   datasources,
		db:            db,
		fileProcessor: fileProcessor,
		fusionEngine:  fusionEngine,
		logger:        logger,
		config:        config,
		updateStatus:  make(map[string]*UpdateStatus),
	}
}

type UpdateStatus struct {
	Source      string    `json:"source"`
	Status      string    `json:"status"` // "pending", "running", "completed", "failed"
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	Progress    int       `json:"progress"` // 0-100
	Records     int64     `json:"records"`  // 处理的记录数
	Error       string    `json:"error"`    // 错误信息
	LastUpdated time.Time `json:"last_updated"`
}

type UpdateResult struct {
	Source   string        `json:"source"`
	Success  bool          `json:"success"`
	Records  int64         `json:"records"`
	Duration time.Duration `json:"duration"`
	Error    string        `json:"error,omitempty"`
}

// 删除重复的NewUpdateManager函数，使用上面的版本

// SetAPIValidator 设置API验证器到数据融合引擎
func (um *UpdateManager) SetAPIValidator(validator APIValidatorInterface) {
	um.fusionEngine.SetAPIValidator(validator)
}

// UpdateAllDatasources 更新所有数据源
func (um *UpdateManager) UpdateAllDatasources(ctx context.Context) ([]UpdateResult, error) {
	um.logger.Info("Starting update for all datasources", zap.Int("count", len(um.datasources)))

	var results []UpdateResult
	var wg sync.WaitGroup
	resultsChan := make(chan UpdateResult, len(um.datasources))

	// 并发更新所有数据源
	for _, ds := range um.datasources {
		wg.Add(1)
		go func(datasource Datasource) {
			defer wg.Done()
			result := um.updateSingleDatasource(ctx, datasource)
			resultsChan <- result
		}(ds)
	}

	// 等待所有更新完成
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// 收集结果
	for result := range resultsChan {
		results = append(results, result)
	}

	// 统计结果
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	um.logger.Info("Datasource update completed",
		zap.Int("total", len(results)),
		zap.Int("success", successCount),
		zap.Int("failed", len(results)-successCount))

	return results, nil
}

// updateSingleDatasource 更新单个数据源
func (um *UpdateManager) updateSingleDatasource(ctx context.Context, ds Datasource) UpdateResult {
	sourceName := ds.Name()
	startTime := time.Now()

	// 初始化状态
	um.setUpdateStatus(sourceName, &UpdateStatus{
		Source:      sourceName,
		Status:      "running",
		StartTime:   startTime,
		Progress:    0,
		LastUpdated: time.Now(),
	})

	um.logger.Info("Starting datasource update", zap.String("source", sourceName))

	// 1. 下载数据
	um.updateProgress(sourceName, 10, "Downloading data...")
	if err := ds.Fetch(ctx); err != nil {
		return um.handleUpdateError(sourceName, startTime, fmt.Errorf("fetch failed: %w", err))
	}

	// 2. 解析数据（检查是否支持流式处理）
	um.updateProgress(sourceName, 30, "Parsing data...")
	
	// 检查是否支持流式处理
	if streamProcessor, ok := ds.(interface{ ParseAndUpdate(ctx context.Context) error }); ok {
		um.logger.Info("Using stream processing for large dataset", zap.String("source", sourceName))
		err := streamProcessor.ParseAndUpdate(ctx)
		if err != nil {
			return um.handleUpdateError(sourceName, startTime, fmt.Errorf("stream processing failed: %w", err))
		}
		
		// 流式处理已经直接写入数据库，跳过后续步骤
		um.updateProgress(sourceName, 100, "Stream processing completed")
		
		duration := time.Since(startTime)
		um.setUpdateStatus(sourceName, &UpdateStatus{
			Source:      sourceName,
			Status:      "completed",
			StartTime:   startTime,
			EndTime:     time.Now(),
			Progress:    100,
			Records:     0, // 流式处理不统计具体记录数
			LastUpdated: time.Now(),
		})
		
		um.logger.Info("Datasource update completed with streaming",
			zap.String("source", sourceName),
			zap.Duration("duration", duration))
		
		return UpdateResult{
			Source:   sourceName,
			Success:  true,
			Records:  0, // 流式处理不统计具体记录数
			Duration: duration,
		}
	}
	
	// 传统处理方式
	ipInfos, err := ds.Parse(ctx)
	if err != nil {
		return um.handleUpdateError(sourceName, startTime, fmt.Errorf("parse failed: %w", err))
	}

	if len(ipInfos) == 0 {
		um.logger.Warn("No data parsed from datasource", zap.String("source", sourceName))
		return um.handleUpdateError(sourceName, startTime, fmt.Errorf("no data parsed"))
	}

	// 3. 数据融合
	um.updateProgress(sourceName, 50, "Performing data fusion...")
	fusedIPInfos, err := um.performDataFusion(ctx, ipInfos)
	if err != nil {
		um.logger.Warn("Data fusion failed, using original data",
			zap.String("source", sourceName),
			zap.Error(err))
		fusedIPInfos = ipInfos
	}

	// 4. 存储到数据库
	um.updateProgress(sourceName, 80, "Storing to database...")

	// 存储到数据库
	um.logger.Debug("Storing data to database", zap.String("source", sourceName))
	batchResult, err := um.db.BatchUpsertIPs(ctx, fusedIPInfos)
	if err != nil {
		return um.handleUpdateError(sourceName, startTime, fmt.Errorf("batch upsert failed: %w", err))
	}

	affectedRows := int64(batchResult.SuccessRecords)
	if batchResult.FailedRecords > 0 {
		um.logger.Warn("Some records failed during batch upsert",
			zap.String("source", sourceName),
			zap.Int("failed", batchResult.FailedRecords),
			zap.Strings("errors", batchResult.Errors))
	}

	um.logger.Info("Data stored successfully",
		zap.String("source", sourceName),
		zap.Int("success", batchResult.SuccessRecords),
		zap.Int("failed", batchResult.FailedRecords))

	// 4. 完成
	duration := time.Since(startTime)
	um.setUpdateStatus(sourceName, &UpdateStatus{
		Source:      sourceName,
		Status:      "completed",
		StartTime:   startTime,
		EndTime:     time.Now(),
		Progress:    100,
		Records:     affectedRows,
		LastUpdated: time.Now(),
	})

	um.logger.Info("Datasource update completed successfully",
		zap.String("source", sourceName),
		zap.Int64("records", affectedRows),
		zap.Duration("duration", duration))

	return UpdateResult{
		Source:   sourceName,
		Success:  true,
		Records:  affectedRows,
		Duration: duration,
	}
}

// handleUpdateError 处理更新错误
func (um *UpdateManager) handleUpdateError(sourceName string, startTime time.Time, err error) UpdateResult {
	duration := time.Since(startTime)

	um.setUpdateStatus(sourceName, &UpdateStatus{
		Source:      sourceName,
		Status:      "failed",
		StartTime:   startTime,
		EndTime:     time.Now(),
		Progress:    0,
		Error:       err.Error(),
		LastUpdated: time.Now(),
	})

	um.logger.Error("Datasource update failed",
		zap.String("source", sourceName),
		zap.Error(err),
		zap.Duration("duration", duration))

	return UpdateResult{
		Source:   sourceName,
		Success:  false,
		Duration: duration,
		Error:    err.Error(),
	}
}

// updateProgress 更新进度
func (um *UpdateManager) updateProgress(sourceName string, progress int, message string) {
	um.statusMutex.Lock()
	defer um.statusMutex.Unlock()

	if status, exists := um.updateStatus[sourceName]; exists {
		status.Progress = progress
		status.LastUpdated = time.Now()
		um.logger.Debug("Update progress",
			zap.String("source", sourceName),
			zap.Int("progress", progress),
			zap.String("message", message))
	}
}

// setUpdateStatus 设置更新状态
func (um *UpdateManager) setUpdateStatus(sourceName string, status *UpdateStatus) {
	um.statusMutex.Lock()
	defer um.statusMutex.Unlock()
	um.updateStatus[sourceName] = status
}

// GetUpdateStatus 获取更新状态
func (um *UpdateManager) GetUpdateStatus(sourceName string) (*UpdateStatus, bool) {
	um.statusMutex.RLock()
	defer um.statusMutex.RUnlock()
	status, exists := um.updateStatus[sourceName]
	return status, exists
}

// GetAllUpdateStatus 获取所有更新状态
func (um *UpdateManager) GetAllUpdateStatus() map[string]*UpdateStatus {
	um.statusMutex.RLock()
	defer um.statusMutex.RUnlock()

	// 创建副本以避免并发问题
	result := make(map[string]*UpdateStatus)
	for k, v := range um.updateStatus {
		statusCopy := *v
		result[k] = &statusCopy
	}
	return result
}

// UpdateSpecificDatasources 更新指定的数据源
func (um *UpdateManager) UpdateSpecificDatasources(ctx context.Context, sourceNames []string) ([]UpdateResult, error) {
	return um.UpdateSpecificDatasourcesWithForce(ctx, sourceNames, false)
}

// UpdateSpecificDatasourcesWithForce 更新指定的数据源（支持强制下载）
func (um *UpdateManager) UpdateSpecificDatasourcesWithForce(ctx context.Context, sourceNames []string, force bool) ([]UpdateResult, error) {
	var targetDatasources []Datasource

	// 查找指定的数据源
	for _, sourceName := range sourceNames {
		found := false
		for _, ds := range um.datasources {
			if ds.Name() == sourceName {
				// 设置强制下载标志
				if forceDownloadable, ok := ds.(ForceDownloadable); ok {
					forceDownloadable.SetForceDownload(force)
				}
				targetDatasources = append(targetDatasources, ds)
				found = true
				break
			}
		}
		if !found {
			um.logger.Warn("Datasource not found", zap.String("source", sourceName))
		}
	}

	if len(targetDatasources) == 0 {
		return nil, fmt.Errorf("no valid datasources found")
	}

	um.logger.Info("Starting update for specific datasources",
		zap.Strings("sources", sourceNames),
		zap.Bool("force", force),
		zap.Int("count", len(targetDatasources)))

	// 临时创建一个管理器来处理指定的数据源
	tempManager := &UpdateManager{
		datasources:   targetDatasources,
		db:            um.db,
		fileProcessor: um.fileProcessor,
		fusionEngine:  um.fusionEngine,
		logger:        um.logger,
		config:        um.config,
		updateStatus:  um.updateStatus,
		statusMutex:   sync.RWMutex{}, // 创建新的mutex避免复制
	}

	return tempManager.UpdateAllDatasources(ctx)
}

// performDataFusion 执行数据融合
func (um *UpdateManager) performDataFusion(ctx context.Context, newIPInfos []model.IPInfo) ([]model.IPInfo, error) {
	if len(newIPInfos) == 0 {
		return newIPInfos, nil
	}

	// 提取所有CIDR
	cidrs := make([]string, 0, len(newIPInfos))
	newDataMap := make(map[string]model.IPInfo)

	for _, ipInfo := range newIPInfos {
		cidr := ipInfo.IPRange.CIDR
		if cidr == "" {
			// 如果没有CIDR，尝试从StartIP和EndIP生成
			if ipInfo.IPRange.StartIP != "" && ipInfo.IPRange.EndIP != "" {
				cidrList, err := RangeToCIDR(ipInfo.IPRange.StartIP, ipInfo.IPRange.EndIP)
				if err == nil && len(cidrList) > 0 {
					cidr = cidrList[0]
					ipInfo.IPRange.CIDR = cidr
				}
			}
		}

		if cidr != "" {
			cidrs = append(cidrs, cidr)
			newDataMap[cidr] = ipInfo
		}
	}

	// 简化数据融合：暂时跳过与现有数据的融合
	// 在新架构中，我们直接使用新数据，依靠数据库的UPSERT逻辑处理冲突
	um.logger.Debug("Performing simplified data fusion (new architecture)",
		zap.Int("records", len(newIPInfos)))

	// 在新架构中，直接返回新数据
	// 数据库层会处理UPSERT逻辑，确保数据正确更新
	um.logger.Info("Data fusion completed (simplified for new architecture)",
		zap.Int("records", len(newIPInfos)))

	return newIPInfos, nil
}
