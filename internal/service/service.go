package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/cache"
	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/datasource"
	"github.com/cosin2077/ipInsight/internal/datasource/api/ipgeolocation"
	"github.com/cosin2077/ipInsight/internal/datasource/api/ipinfo"
	"github.com/cosin2077/ipInsight/internal/datasource/ipstack"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/monitoring"
	"go.uber.org/zap"
)

// DatabaseInterfaceHealthChecker 数据库接口健康检查器
type DatabaseInterfaceHealthChecker struct {
	db database.DatabaseInterface
}

// Check 执行健康检查
func (h *DatabaseInterfaceHealthChecker) Check(ctx context.Context) error {
	return h.db.Ping(ctx)
}

// Name 返回检查器名称
func (h *DatabaseInterfaceHealthChecker) Name() string {
	return "database_interface"
}

type Service struct {
	db            database.DatabaseInterface // 使用接口而不是具体实现
	cache         *cache.Cache
	ipInfo        *ipinfo.IPInfo
	ipGeolocation *ipgeolocation.IPGeolocation
	ipStack       *ipstack.IPStack
	logger        *zap.Logger
	once          sync.Once
	cfg           *config.Config
	datasources   map[string]datasource.Datasource // 存储数据源实例
	updateManager *datasource.UpdateManager
	apiValidator  *APIValidator
	queryEngine   *QueryEngine
	monitor       *monitoring.Monitor
}

func NewService(db database.DatabaseInterface, cache *cache.Cache, ipInfo *ipinfo.IPInfo, ipGeolocation *ipgeolocation.IPGeolocation, ipStack *ipstack.IPStack, logger *zap.Logger, cfg *config.Config) *Service {
	apiValidator := NewAPIValidator(logger)
	queryEngine := NewQueryEngine(db, cache, apiValidator, logger)
	monitor := monitoring.NewMonitor(logger)

	service := &Service{
		db:            db,
		cache:         cache,
		ipInfo:        ipInfo,
		ipGeolocation: ipGeolocation,
		ipStack:       ipStack,
		logger:        logger,
		cfg:           cfg,
		datasources:   make(map[string]datasource.Datasource),
		apiValidator:  apiValidator,
		queryEngine:   queryEngine,
		monitor:       monitor,
	}

	// 设置监控器到查询引擎
	queryEngine.SetMonitor(monitor)

	// 注册健康检查器
	// 创建数据库接口健康检查器
	dbHealthChecker := &DatabaseInterfaceHealthChecker{db: db}
	monitor.RegisterHealthChecker(dbHealthChecker)
	if cache != nil {
		monitor.RegisterHealthChecker(monitoring.NewCacheHealthChecker(cache))
	}

	return service
}

// SetDatasources 设置数据源实例
func (s *Service) SetDatasources(datasources []datasource.Datasource) {
	for _, ds := range datasources {
		s.datasources[ds.Name()] = ds
	}
}

// SetUpdateManager 设置更新管理器
func (s *Service) SetUpdateManager(updateManager *datasource.UpdateManager) {
	s.updateManager = updateManager
}

// GetAPIValidator 获取API验证器
func (s *Service) GetAPIValidator() *APIValidator {
	return s.apiValidator
}

// newDatasource 根据名称获取数据源实例
func (s *Service) newDatasource(name string) (datasource.Datasource, error) {
	ds, exists := s.datasources[name]
	if !exists {
		return nil, fmt.Errorf("datasource %s not found", name)
	}
	return ds, nil
}

func (s *Service) QueryIP(ctx context.Context, ip string) (*model.IPInfo, error) {
	// 查缓存
	ipInfo, err := s.cache.Get(ctx, ip)
	if err != nil {
		s.logger.Warn("Cache error", zap.String("ip", ip), zap.Error(err))
	}
	if ipInfo != nil {
		return ipInfo, nil
	}

	// 查数据库
	ipInfo, err = s.db.Query(ctx, ip)
	if err == nil {
		// 写入缓存
		if err := s.cache.Set(ctx, ip, ipInfo); err != nil {
			s.logger.Warn("Failed to set cache", zap.Error(err))
		}
		return ipInfo, nil
	}

	// 并发查询所有 API
	type apiResult struct {
		info *model.IPInfo
		err  error
		src  string
	}
	apis := []struct {
		name  string
		query func(context.Context, string) (*model.IPInfo, error)
	}{
		{"ipinfo", s.ipInfo.QueryIP},
		{"ipgeolocation", s.ipGeolocation.QueryIP},
		{"ipstack", s.ipStack.QueryIP},
	}
	resultsCh := make(chan apiResult, len(apis))
	var wg sync.WaitGroup
	for _, api := range apis {
		wg.Add(1)
		go func(apiName string, query func(context.Context, string) (*model.IPInfo, error)) {
			defer wg.Done()
			info, err := query(ctx, ip)
			resultsCh <- apiResult{info, err, apiName}
		}(api.name, api.query)
	}
	wg.Wait()
	close(resultsCh)

	var infos []model.IPInfo
	for res := range resultsCh {
		if res.err == nil && res.info != nil {
			infos = append(infos, *res.info)
		}
	}

	if len(infos) == 0 {
		s.logger.Warn("All API queries failed", zap.String("ip", ip))
		return nil, model.ErrIPNotFound
	}

	// 融合结果，优先级：ipinfo > ipgeolocation > ipstack
	sourcePriority := map[string]int{
		"ipinfo":        3,
		"ipgeolocation": 2,
		"ipstack":       1,
	}
	best := infos[0]
	for _, info := range infos[1:] {
		if sourcePriority[info.Metadata.Source] > sourcePriority[best.Metadata.Source] {
			best = info
		}
	}

	// 异步写入数据库
	go func(ipInfo model.IPInfo) {
		if err := s.db.Insert(context.Background(), ipInfo); err != nil {
			s.logger.Warn("Failed to insert API result", zap.String("source", ipInfo.Metadata.Source), zap.Error(err))
		}
	}(best)

	// 写入缓存
	if err := s.cache.Set(ctx, ip, &best); err != nil {
		s.logger.Warn("Failed to set cache", zap.Error(err))
	}

	return &best, nil
}

func (s *Service) ManualFetchAndUpdate(ctx context.Context, sources []string, force bool) (map[string]int64, error) {
	if s.updateManager == nil {
		// 回退到原有逻辑
		return s.manualFetchAndUpdateLegacy(ctx, sources, force)
	}

	// 使用UpdateManager
	results, err := s.updateManager.UpdateSpecificDatasourcesWithForce(ctx, sources, force)
	if err != nil {
		return nil, err
	}

	updated := make(map[string]int64)
	for _, result := range results {
		if result.Success {
			updated[result.Source] = result.Records
		} else {
			return nil, fmt.Errorf("update failed for %s: %s", result.Source, result.Error)
		}
	}

	return updated, nil
}

// manualFetchAndUpdateLegacy 原有的手动更新逻辑（作为回退）
func (s *Service) manualFetchAndUpdateLegacy(ctx context.Context, sources []string, force bool) (map[string]int64, error) {
	updated := make(map[string]int64)
	for _, src := range sources {
		ds, err := s.newDatasource(src)
		if err != nil {
			return nil, fmt.Errorf("new datasource %s: %w", src, err)
		}

		// 设置强制下载标志
		if forceDownloadable, ok := ds.(datasource.ForceDownloadable); ok {
			forceDownloadable.SetForceDownload(force)
		}

		// 先获取数据
		if err := ds.Fetch(ctx); err != nil {
			return nil, fmt.Errorf("fetch %s: %w", src, err)
		}

		// 然后解析数据
		ips, err := ds.Parse(ctx)
		if err != nil {
			return nil, fmt.Errorf("parse %s: %w", src, err)
		}

		// 最后存入数据库
		result, err := s.db.BatchUpsertIPs(ctx, ips)
		if err != nil {
			return nil, fmt.Errorf("upsert %s: %w", src, err)
		}
		affectedRows := int64(result.SuccessRecords)
		updated[src] = affectedRows

		s.logger.Info("Manual fetch and update completed",
			zap.String("source", src),
			zap.Int("parsed_records", len(ips)),
			zap.Int64("affected_rows", affectedRows),
			zap.Bool("force", force))
	}
	return updated, nil
}

// ValidateDatasources 验证数据源配置
func (s *Service) ValidateDatasources(ctx context.Context, sources []string) ([]datasource.ValidationResult, error) {
	validator := datasource.NewDatasourceValidator(s.logger, s.cfg)

	if len(sources) == 0 {
		// 验证所有数据源
		return validator.ValidateAllDatasources(ctx)
	} else {
		// 验证指定的数据源
		return validator.ValidateSpecificDatasources(ctx, sources)
	}
}

// GetIPInfo 查询IP信息，使用优化的三层查询架构
func (s *Service) GetIPInfo(ctx context.Context, ipStr string) (*model.IPInfo, error) {
	result, err := s.queryEngine.Query(ctx, ipStr)
	if err != nil {
		return nil, err
	}

	// 记录查询来源
	s.logger.Debug("IP query completed",
		zap.String("ip", ipStr),
		zap.String("source", result.Source),
		zap.Duration("duration", result.Duration),
		zap.Bool("from_cache", result.FromCache))

	return result.IPInfo, nil
}

// BatchGetIPInfo 批量查询IP信息
func (s *Service) BatchGetIPInfo(ctx context.Context, ips []string) (map[string]*model.IPInfo, error) {
	results, err := s.queryEngine.BatchQuery(ctx, ips)
	if err != nil {
		return nil, err
	}

	// 转换结果格式
	ipInfos := make(map[string]*model.IPInfo)
	for ip, result := range results {
		if result.Error == nil && result.IPInfo != nil {
			ipInfos[ip] = result.IPInfo
		}
	}

	s.logger.Info("Batch IP query completed",
		zap.Int("requested", len(ips)),
		zap.Int("successful", len(ipInfos)))

	return ipInfos, nil
}

// GetQueryStats 获取查询统计信息
func (s *Service) GetQueryStats() map[string]interface{} {
	return s.queryEngine.GetStats()
}

// GetHotIPStats 获取热门IP统计信息
func (s *Service) GetHotIPStats() map[string]interface{} {
	return s.cache.GetHotIPStats()
}

// GetHotIPs 获取热门IP列表
func (s *Service) GetHotIPs() interface{} {
	return s.cache.GetHotIPs()
}

// GetBatchStats 获取批量操作统计信息
func (s *Service) GetBatchStats() map[string]interface{} {
	return s.db.GetStats(context.Background())
}

// BatchUpsertIPs 批量插入或更新IP信息
func (s *Service) BatchUpsertIPs(ctx context.Context, ipInfos []model.IPInfo) (interface{}, error) {
	return s.db.BatchUpsertIPs(ctx, ipInfos)
}

// GetMonitoringMetrics 获取监控指标
func (s *Service) GetMonitoringMetrics() map[string]interface{} {
	return s.monitor.GetMetrics()
}

// GetHealthStatus 获取健康状态
func (s *Service) GetHealthStatus(ctx context.Context) map[string]interface{} {
	healthStatus := s.monitor.GetHealthStatus(ctx)
	result := make(map[string]interface{})
	for name, status := range healthStatus {
		result[name] = status
	}
	return result
}

// DataSourceStatus 数据源状态结构
type DataSourceStatus struct {
	Name         string `json:"name"`
	Enabled      bool   `json:"enabled"`
	LastUpdate   int64  `json:"last_update"`
	NextUpdate   int64  `json:"next_update"`
	Status       string `json:"status"` // "active", "inactive", "error", "updating"
	Error        string `json:"error,omitempty"`
	RecordsCount int64  `json:"records_count,omitempty"`
}

// GetDataSourcesStatus 获取所有数据源状态
func (s *Service) GetDataSourcesStatus() []DataSourceStatus {
	var statuses []DataSourceStatus

	// 定义数据源配置
	dataSourceConfigs := map[string]struct {
		enabled   bool
		dataDir   string
		fileNames []string
	}{
		"maxmind": {
			enabled:   s.cfg.Datasources.Maxmind.Enabled,
			dataDir:   "data/maxmind",
			fileNames: []string{"GeoLite2-Country.mmdb", "GeoLite2-City.mmdb", "GeoLite2-ASN.mmdb"},
		},
		"ip2location": {
			enabled:   s.cfg.Datasources.IP2Location.Enabled,
			dataDir:   "data/ip2location",
			fileNames: []string{"IP2LOCATION-LITE-DB1.CSV", "IP2LOCATION-LITE-DB11.CSV"},
		},
		"dbip": {
			enabled:   s.cfg.Datasources.DBIP.Enabled,
			dataDir:   "data/dbip",
			fileNames: []string{"dbip-city-lite.mmdb", "dbip-country-lite.mmdb"},
		},
		"iplocate": {
			enabled:   s.cfg.Datasources.IPLocate.Enabled,
			dataDir:   "data/iplocate",
			fileNames: []string{"ip-to-asn.mmdb", "ip-to-country.mmdb"},
		},
		"ipapi": {
			enabled:   s.cfg.Datasources.IPAPI.Enabled,
			dataDir:   "data/ipapi",
			fileNames: []string{"geolocationDatabaseIPv4.csv", "geolocationDatabaseIPv6.csv"},
		},
		"qqwry": {
			enabled:   s.cfg.Datasources.QQWry.Enabled,
			dataDir:   "data/qqwry",
			fileNames: []string{"qqwry.dat"},
		},
	}

	for name, config := range dataSourceConfigs {
		status := s.getDataSourceStatus(name, config.enabled, config.dataDir, config.fileNames)
		statuses = append(statuses, status)
	}

	return statuses
}

// getDataSourceStatus 获取单个数据源状态
func (s *Service) getDataSourceStatus(name string, enabled bool, dataDir string, fileNames []string) DataSourceStatus {
	status := DataSourceStatus{
		Name:    name,
		Enabled: enabled,
		Status:  "inactive",
	}

	if !enabled {
		status.Error = "数据源已禁用"
		return status
	}

	// 检查更新管理器中的状态
	if s.updateManager != nil {
		if updateStatus, exists := s.updateManager.GetUpdateStatus(name); exists {
			switch updateStatus.Status {
			case "running":
				status.Status = "updating"
			case "failed":
				status.Status = "error"
				status.Error = updateStatus.Error
			case "completed":
				status.Status = "active"
				status.RecordsCount = updateStatus.Records
			}
			status.LastUpdate = updateStatus.EndTime.Unix()
		}
	}

	// 检查数据文件状态
	var latestModTime time.Time
	var totalSize int64
	var hasValidFiles bool

	for _, fileName := range fileNames {
		filePath := filepath.Join(dataDir, fileName)
		if info, err := os.Stat(filePath); err == nil {
			hasValidFiles = true
			if info.ModTime().After(latestModTime) {
				latestModTime = info.ModTime()
			}
			totalSize += info.Size()
		}
	}

	if hasValidFiles {
		status.LastUpdate = latestModTime.Unix()
		// 计算下次更新时间（假设每24小时更新一次）
		status.NextUpdate = latestModTime.Add(24 * time.Hour).Unix()

		if status.Status == "inactive" {
			status.Status = "active"
		}

		// 如果没有记录数，尝试从数据库获取
		if status.RecordsCount == 0 {
			if count, err := s.getRecordsCountFromDB(name); err == nil {
				status.RecordsCount = count
			}
		}
	} else {
		status.Error = "数据文件不存在"
		status.Status = "error"
	}

	return status
}

// getRecordsCountFromDB 从数据库获取记录数（简化实现）
func (s *Service) getRecordsCountFromDB(source string) (int64, error) {
	// 这里可以实现从数据库查询特定数据源的记录数
	// 暂时返回模拟数据
	mockCounts := map[string]int64{
		"maxmind":     3500000,
		"ip2location": 2800000,
		"dbip":        4200000,
		"iplocate":    1500000,
		"ipapi":       1000000,
		"qqwry":       800000,
	}

	if count, exists := mockCounts[source]; exists {
		return count, nil
	}
	return 0, fmt.Errorf("unknown source: %s", source)
}

// GetMonitoringSummary 获取监控摘要
func (s *Service) GetMonitoringSummary() map[string]interface{} {
	return s.monitor.GetSummary()
}

// GetMonitor 获取监控器实例
func (s *Service) GetMonitor() *monitoring.Monitor {
	return s.monitor
}

// queryFromAPIs 从第三方API查询IP信息，复用QueryIP中的逻辑
func (s *Service) queryFromAPIs(ctx context.Context, ip string) (*model.IPInfo, error) {
	// 并发查询所有 API
	type apiResult struct {
		info *model.IPInfo
		err  error
		src  string
	}
	apis := []struct {
		name  string
		query func(context.Context, string) (*model.IPInfo, error)
	}{
		{"ipinfo", s.ipInfo.QueryIP},
		{"ipgeolocation", s.ipGeolocation.QueryIP},
		{"ipstack", s.ipStack.QueryIP},
	}
	resultsCh := make(chan apiResult, len(apis))
	var wg sync.WaitGroup
	for _, api := range apis {
		wg.Add(1)
		go func(apiName string, query func(context.Context, string) (*model.IPInfo, error)) {
			defer wg.Done()
			info, err := query(ctx, ip)
			resultsCh <- apiResult{info, err, apiName}
		}(api.name, api.query)
	}
	wg.Wait()
	close(resultsCh)

	var infos []model.IPInfo
	for res := range resultsCh {
		if res.err == nil && res.info != nil {
			infos = append(infos, *res.info)
		}
	}

	if len(infos) == 0 {
		s.logger.Warn("All API queries failed", zap.String("ip", ip))
		return nil, model.ErrIPNotFound
	}

	// 融合结果，优先级：ipinfo > ipgeolocation > ipstack
	sourcePriority := map[string]int{
		"ipinfo":        3,
		"ipgeolocation": 2,
		"ipstack":       1,
	}
	best := infos[0]
	for _, info := range infos[1:] {
		if sourcePriority[info.Metadata.Source] > sourcePriority[best.Metadata.Source] {
			best = info
		}
	}

	return &best, nil
}
