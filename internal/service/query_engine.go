package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/cache"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/cosin2077/ipInsight/internal/monitoring"
	"go.uber.org/zap"
)

type QueryEngine struct {
	db           database.DatabaseInterface // 使用接口
	cache        *cache.Cache
	apiValidator *APIValidator
	logger       *zap.Logger
	monitor      *monitoring.Monitor

	// 查询统计
	stats struct {
		sync.RWMutex
		CacheHits    int64 `json:"cache_hits"`
		DatabaseHits int64 `json:"database_hits"`
		APIFallbacks int64 `json:"api_fallbacks"`
		TotalQueries int64 `json:"total_queries"`
		Errors       int64 `json:"errors"`
	}

	// 查询配置
	config QueryConfig
}

type QueryConfig struct {
	CacheEnabled       bool          `json:"cache_enabled"`
	DatabaseEnabled    bool          `json:"database_enabled"`
	APIFallbackEnabled bool          `json:"api_fallback_enabled"`
	CacheTTL           time.Duration `json:"cache_ttl"`
	QueryTimeout       time.Duration `json:"query_timeout"`
	MaxConcurrentAPIs  int           `json:"max_concurrent_apis"`
	EnableAsyncWrite   bool          `json:"enable_async_write"`
}

type QueryResult struct {
	IPInfo    *model.IPInfo `json:"ip_info"`
	Source    string        `json:"source"` // "cache", "database", "api"
	Duration  time.Duration `json:"duration"`
	FromCache bool          `json:"from_cache"`
	Error     error         `json:"error,omitempty"`
}

func NewQueryEngine(db database.DatabaseInterface, cache *cache.Cache, apiValidator *APIValidator, logger *zap.Logger) *QueryEngine {
	return &QueryEngine{
		db:           db,
		cache:        cache,
		apiValidator: apiValidator,
		logger:       logger,
		config: QueryConfig{
			CacheEnabled:       true,
			DatabaseEnabled:    true,
			APIFallbackEnabled: true,
			CacheTTL:           24 * time.Hour,
			QueryTimeout:       30 * time.Second,
			MaxConcurrentAPIs:  3,
			EnableAsyncWrite:   true,
		},
	}
}

// SetMonitor 设置监控器
func (qe *QueryEngine) SetMonitor(monitor *monitoring.Monitor) {
	qe.monitor = monitor
}

// Query 执行三层查询：缓存 -> 数据库 -> API回源
func (qe *QueryEngine) Query(ctx context.Context, ip string) (*QueryResult, error) {
	startTime := time.Now()
	queryID := fmt.Sprintf("query_%d", time.Now().UnixNano())

	// 增加查询计数
	qe.incrementStat("total_queries")

	// 创建带超时的context
	queryCtx, cancel := context.WithTimeout(ctx, qe.config.QueryTimeout)
	defer cancel()

	qe.logger.Info("Starting three-layer IP query",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Time("start_time", startTime),
		zap.Duration("timeout", qe.config.QueryTimeout))

	// 第一层：查询缓存
	if qe.config.CacheEnabled {
		qe.logger.Debug("Layer 1: Attempting cache query",
			zap.String("query_id", queryID),
			zap.String("ip", ip))

		if result := qe.queryCache(queryCtx, ip, queryID); result != nil {
			totalDuration := time.Since(startTime)
			qe.logger.Info("Query completed successfully from cache",
				zap.String("query_id", queryID),
				zap.String("ip", ip),
				zap.String("source", "cache"),
				zap.Duration("total_duration", totalDuration),
				zap.Duration("cache_duration", result.Duration))
			return result, nil
		}

		qe.logger.Debug("Layer 1: Cache miss, proceeding to database",
			zap.String("query_id", queryID),
			zap.String("ip", ip))
	} else {
		qe.logger.Debug("Layer 1: Cache disabled, skipping to database",
			zap.String("query_id", queryID),
			zap.String("ip", ip))
	}

	// 第二层：查询数据库
	if qe.config.DatabaseEnabled {
		qe.logger.Debug("Layer 2: Attempting database query",
			zap.String("query_id", queryID),
			zap.String("ip", ip))

		if result := qe.queryDatabase(queryCtx, ip, queryID); result != nil {
			totalDuration := time.Since(startTime)
			qe.logger.Info("Query completed successfully from database",
				zap.String("query_id", queryID),
				zap.String("ip", ip),
				zap.String("source", "database"),
				zap.Duration("total_duration", totalDuration),
				zap.Duration("database_duration", result.Duration))

			// 异步写入缓存
			if qe.config.CacheEnabled && qe.config.EnableAsyncWrite {
				qe.logger.Debug("Initiating async cache write",
					zap.String("query_id", queryID),
					zap.String("ip", ip))
				go qe.asyncCacheWrite(ip, result.IPInfo, queryID)
			}
			return result, nil
		}

		qe.logger.Debug("Layer 2: Database miss, proceeding to API fallback",
			zap.String("query_id", queryID),
			zap.String("ip", ip))
	} else {
		qe.logger.Debug("Layer 2: Database disabled, skipping to API fallback",
			zap.String("query_id", queryID),
			zap.String("ip", ip))
	}

	// 第三层：API回源
	if qe.config.APIFallbackEnabled {
		qe.logger.Debug("Layer 3: Attempting API fallback query",
			zap.String("query_id", queryID),
			zap.String("ip", ip))

		result, err := qe.queryAPI(queryCtx, ip, queryID)
		totalDuration := time.Since(startTime)

		if err != nil {
			qe.logger.Error("Query failed at all layers",
				zap.String("query_id", queryID),
				zap.String("ip", ip),
				zap.Duration("total_duration", totalDuration),
				zap.Error(err))
		} else {
			qe.logger.Info("Query completed successfully from API fallback",
				zap.String("query_id", queryID),
				zap.String("ip", ip),
				zap.String("source", "api"),
				zap.Duration("total_duration", totalDuration),
				zap.Duration("api_duration", result.Duration))
		}

		return result, err
	} else {
		qe.logger.Debug("Layer 3: API fallback disabled",
			zap.String("query_id", queryID),
			zap.String("ip", ip))
	}

	// 所有查询方式都失败
	totalDuration := time.Since(startTime)
	qe.incrementStat("errors")

	qe.logger.Error("Query failed: all layers exhausted",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Duration("total_duration", totalDuration),
		zap.Bool("cache_enabled", qe.config.CacheEnabled),
		zap.Bool("database_enabled", qe.config.DatabaseEnabled),
		zap.Bool("api_fallback_enabled", qe.config.APIFallbackEnabled))

	return &QueryResult{
		Source:   "none",
		Duration: totalDuration,
		Error:    fmt.Errorf("no data found for IP: %s", ip),
	}, fmt.Errorf("no data found for IP: %s", ip)
}

// queryCache 查询缓存层
func (qe *QueryEngine) queryCache(ctx context.Context, ip string, queryID string) *QueryResult {
	cacheStartTime := time.Now()

	qe.logger.Debug("Cache query started",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Time("cache_start_time", cacheStartTime))

	ipInfo, err := qe.cache.Get(ctx, ip)
	cacheDuration := time.Since(cacheStartTime)

	// 记录缓存操作监控
	if qe.monitor != nil {
		qe.monitor.RecordCacheOperation(err == nil && ipInfo != nil)
	}

	if err != nil {
		qe.logger.Warn("Cache query failed",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("cache_duration", cacheDuration),
			zap.Error(err))
		return nil
	}

	if ipInfo != nil {
		qe.incrementStat("cache_hits")
		qe.logger.Info("Cache query successful - HIT",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("cache_duration", cacheDuration),
			zap.String("source", ipInfo.Metadata.Source))

		return &QueryResult{
			IPInfo:    ipInfo,
			Source:    "cache",
			Duration:  cacheDuration,
			FromCache: true,
		}
	}

	qe.logger.Debug("Cache query completed - MISS",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Duration("cache_duration", cacheDuration))

	return nil
}

// queryDatabase 查询数据库层
func (qe *QueryEngine) queryDatabase(ctx context.Context, ip string, queryID string) *QueryResult {
	dbStartTime := time.Now()

	qe.logger.Debug("Database query started",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Time("db_start_time", dbStartTime))

	ipInfo, err := qe.db.Query(ctx, ip)
	dbDuration := time.Since(dbStartTime)

	// 记录数据库查询监控
	if qe.monitor != nil {
		qe.monitor.RecordDatabaseQuery(dbDuration, err == nil)
	}

	if err != nil {
		qe.logger.Warn("Database query failed",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("db_duration", dbDuration),
			zap.Error(err))
		return nil
	}

	if ipInfo != nil {
		qe.incrementStat("database_hits")
		qe.logger.Info("Database query successful - HIT",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("db_duration", dbDuration),
			zap.String("source", ipInfo.Metadata.Source),
			zap.String("country", ipInfo.Geolocation.Country.Code))

		return &QueryResult{
			IPInfo:   ipInfo,
			Source:   "database",
			Duration: dbDuration,
		}
	}

	qe.logger.Debug("Database query completed - MISS",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Duration("db_duration", dbDuration))

	return nil
}

// queryAPI API回源查询
func (qe *QueryEngine) queryAPI(ctx context.Context, ip string, queryID string) (*QueryResult, error) {
	apiStartTime := time.Now()
	qe.incrementStat("api_fallbacks")

	qe.logger.Info("API fallback query started",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Time("api_start_time", apiStartTime))

	// 使用APIValidator进行查询
	ipInfo, err := qe.apiValidator.ValidateIPInfo(ctx, ip, []model.IPInfo{})
	apiDuration := time.Since(apiStartTime)

	if err != nil {
		qe.incrementStat("errors")
		qe.logger.Error("API fallback query failed",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("api_duration", apiDuration),
			zap.Error(err))
		return &QueryResult{
			Source:   "api",
			Duration: apiDuration,
			Error:    err,
		}, err
	}

	qe.logger.Info("API fallback query successful",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Duration("api_duration", apiDuration),
		zap.String("api_source", ipInfo.Metadata.Source),
		zap.String("country", ipInfo.Geolocation.Country.Code))

	result := &QueryResult{
		IPInfo:   ipInfo,
		Source:   "api",
		Duration: apiDuration,
	}

	// 异步写入数据库和缓存
	if qe.config.EnableAsyncWrite {
		qe.logger.Debug("Initiating async write operations",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Bool("database_write", true),
			zap.Bool("cache_write", qe.config.CacheEnabled))

		go qe.asyncDatabaseWrite(ip, ipInfo, queryID)
		if qe.config.CacheEnabled {
			go qe.asyncCacheWrite(ip, ipInfo, queryID)
		}
	} else {
		qe.logger.Debug("Async write disabled",
			zap.String("query_id", queryID),
			zap.String("ip", ip))
	}

	return result, nil
}

// asyncCacheWrite 异步写入缓存
func (qe *QueryEngine) asyncCacheWrite(ip string, ipInfo *model.IPInfo, queryID string) {
	writeStartTime := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	qe.logger.Debug("Async cache write started",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Time("write_start_time", writeStartTime))

	err := qe.cache.Set(ctx, ip, ipInfo)
	writeDuration := time.Since(writeStartTime)

	if err != nil {
		qe.logger.Error("Async cache write failed",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("write_duration", writeDuration),
			zap.String("source", ipInfo.Metadata.Source),
			zap.Error(err))

		// 可以考虑添加重试逻辑
		if writeDuration < 3*time.Second {
			qe.logger.Debug("Attempting cache write retry",
				zap.String("query_id", queryID),
				zap.String("ip", ip))

			retryCtx, retryCancel := context.WithTimeout(context.Background(), 2*time.Second)
			defer retryCancel()

			if retryErr := qe.cache.Set(retryCtx, ip, ipInfo); retryErr != nil {
				qe.logger.Error("Async cache write retry failed",
					zap.String("query_id", queryID),
					zap.String("ip", ip),
					zap.Error(retryErr))
			} else {
				qe.logger.Info("Async cache write retry successful",
					zap.String("query_id", queryID),
					zap.String("ip", ip))
			}
		}
	} else {
		qe.logger.Info("Async cache write successful",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("write_duration", writeDuration),
			zap.String("source", ipInfo.Metadata.Source))
	}
}

// asyncDatabaseWrite 异步写入数据库
func (qe *QueryEngine) asyncDatabaseWrite(ip string, ipInfo *model.IPInfo, queryID string) {
	writeStartTime := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	qe.logger.Debug("Async database write started",
		zap.String("query_id", queryID),
		zap.String("ip", ip),
		zap.Time("write_start_time", writeStartTime),
		zap.String("source", ipInfo.Metadata.Source),
		zap.String("cidr", ipInfo.IPRange.CIDR))

	// 验证CIDR字段不为空
	if ipInfo.IPRange.CIDR == "" {
		qe.logger.Error("Async database write failed: empty CIDR field",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.String("source", ipInfo.Metadata.Source),
			zap.String("start_ip", ipInfo.IPRange.StartIP),
			zap.String("end_ip", ipInfo.IPRange.EndIP))

		// 尝试生成CIDR
		if ipInfo.IPRange.StartIP != "" {
			var cidr string
			if strings.Contains(ipInfo.IPRange.StartIP, ":") {
				// IPv6
				cidr = ipInfo.IPRange.StartIP + "/128"
				ipInfo.IPRange.IPVersion = "IPv6"
			} else {
				// IPv4
				cidr = ipInfo.IPRange.StartIP + "/32"
				ipInfo.IPRange.IPVersion = "IPv4"
			}
			ipInfo.IPRange.CIDR = cidr

			qe.logger.Info("Generated CIDR for database write",
				zap.String("query_id", queryID),
				zap.String("ip", ip),
				zap.String("generated_cidr", cidr))
		} else {
			qe.logger.Error("Cannot generate CIDR: StartIP is also empty",
				zap.String("query_id", queryID),
				zap.String("ip", ip))
			return
		}
	}

	err := qe.db.Insert(ctx, *ipInfo)
	writeDuration := time.Since(writeStartTime)

	if err != nil {
		qe.logger.Error("Async database write failed",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("write_duration", writeDuration),
			zap.String("source", ipInfo.Metadata.Source),
			zap.String("country", ipInfo.Geolocation.Country.Code),
			zap.Error(err))

		// 检查是否是重复键错误（这通常是正常的）
		if strings.Contains(err.Error(), "duplicate") || strings.Contains(err.Error(), "unique") {
			qe.logger.Debug("Database write failed due to duplicate entry (normal)",
				zap.String("query_id", queryID),
				zap.String("ip", ip))
		} else {
			// 对于其他错误，可以考虑重试
			if writeDuration < 8*time.Second {
				qe.logger.Debug("Attempting database write retry",
					zap.String("query_id", queryID),
					zap.String("ip", ip))

				retryCtx, retryCancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer retryCancel()

				if retryErr := qe.db.Insert(retryCtx, *ipInfo); retryErr != nil {
					qe.logger.Error("Async database write retry failed",
						zap.String("query_id", queryID),
						zap.String("ip", ip),
						zap.Error(retryErr))
				} else {
					qe.logger.Info("Async database write retry successful",
						zap.String("query_id", queryID),
						zap.String("ip", ip))
				}
			}
		}
	} else {
		qe.logger.Info("Async database write successful",
			zap.String("query_id", queryID),
			zap.String("ip", ip),
			zap.Duration("write_duration", writeDuration),
			zap.String("source", ipInfo.Metadata.Source),
			zap.String("country", ipInfo.Geolocation.Country.Code),
			zap.String("city", ipInfo.Geolocation.City))
	}
}

// BatchQuery 批量查询IP信息
func (qe *QueryEngine) BatchQuery(ctx context.Context, ips []string) (map[string]*QueryResult, error) {
	if len(ips) == 0 {
		return make(map[string]*QueryResult), nil
	}

	results := make(map[string]*QueryResult)
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 限制并发数
	semaphore := make(chan struct{}, qe.config.MaxConcurrentAPIs)

	for _, ip := range ips {
		wg.Add(1)
		go func(ipAddr string) {
			defer wg.Done()

			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result, err := qe.Query(ctx, ipAddr)
			if err != nil {
				result = &QueryResult{
					Source: "error",
					Error:  err,
				}
			}

			mu.Lock()
			results[ipAddr] = result
			mu.Unlock()
		}(ip)
	}

	wg.Wait()

	qe.logger.Info("Batch query completed",
		zap.Int("total", len(ips)),
		zap.Int("results", len(results)))

	return results, nil
}

// GetStats 获取查询统计信息
func (qe *QueryEngine) GetStats() map[string]any {
	qe.stats.RLock()
	defer qe.stats.RUnlock()

	return map[string]any{
		"cache_hits":     qe.stats.CacheHits,
		"database_hits":  qe.stats.DatabaseHits,
		"api_fallbacks":  qe.stats.APIFallbacks,
		"total_queries":  qe.stats.TotalQueries,
		"errors":         qe.stats.Errors,
		"cache_hit_rate": qe.calculateHitRate(qe.stats.CacheHits),
		"db_hit_rate":    qe.calculateHitRate(qe.stats.DatabaseHits),
	}
}

// calculateHitRate 计算命中率
func (qe *QueryEngine) calculateHitRate(hits int64) float64 {
	if qe.stats.TotalQueries == 0 {
		return 0.0
	}
	return float64(hits) / float64(qe.stats.TotalQueries) * 100
}

// incrementStat 增加统计计数
func (qe *QueryEngine) incrementStat(statName string) {
	qe.stats.Lock()
	defer qe.stats.Unlock()

	switch statName {
	case "cache_hits":
		qe.stats.CacheHits++
	case "database_hits":
		qe.stats.DatabaseHits++
	case "api_fallbacks":
		qe.stats.APIFallbacks++
	case "total_queries":
		qe.stats.TotalQueries++
	case "errors":
		qe.stats.Errors++
	}
}

// UpdateConfig 更新查询配置
func (qe *QueryEngine) UpdateConfig(config QueryConfig) {
	qe.config = config
	qe.logger.Info("Query engine configuration updated",
		zap.Bool("cache_enabled", config.CacheEnabled),
		zap.Bool("database_enabled", config.DatabaseEnabled),
		zap.Bool("api_fallback_enabled", config.APIFallbackEnabled))
}
