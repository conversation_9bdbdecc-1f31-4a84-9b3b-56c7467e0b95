package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

type APIValidator struct {
	logger   *zap.Logger
	client   *http.Client
	limiters map[string]*rate.Limiter
	mutex    sync.RWMutex
}

type APIEndpoint struct {
	Name       string
	URL        string
	RateLimit  rate.Limit // 每秒请求数限制
	BurstLimit int        // 突发请求限制
	Timeout    time.Duration
	ParseFunc  func([]byte, string) (*model.IPInfo, error)
}

// 基于ipInfo.py中的免费API列表
var FreeAPIEndpoints = []APIEndpoint{
	{
		Name:       "ip-api",
		URL:        "http://ip-api.com/json/%s",
		RateLimit:  rate.Every(time.Minute / 45), // 45 requests per minute
		BurstLimit: 5,
		Timeout:    10 * time.Second,
		ParseFunc:  parseIPAPIResponse,
	},
	{
		Name:       "ipapi",
		URL:        "https://ipapi.co/%s/json/",
		RateLimit:  rate.Every(time.Minute / 30), // 30 requests per minute
		BurstLimit: 3,
		Timeout:    10 * time.Second,
		ParseFunc:  parseIPAPICoResponse,
	},
	{
		Name:       "freeipapi",
		URL:        "https://freeipapi.com/api/json/%s",
		RateLimit:  rate.Every(time.Minute / 60), // 60 requests per minute
		BurstLimit: 5,
		Timeout:    10 * time.Second,
		ParseFunc:  parseFreeIPAPIResponse,
	},
	{
		Name:       "ipwhois",
		URL:        "http://ipwhois.app/json/%s",
		RateLimit:  rate.Every(time.Minute / 10), // 10 requests per minute
		BurstLimit: 2,
		Timeout:    15 * time.Second,
		ParseFunc:  parseIPWhoisResponse,
	},
	{
		Name:       "ipgeolocation",
		URL:        "https://api.ipgeolocation.io/ipgeo?apiKey=&ip=%s",
		RateLimit:  rate.Every(time.Minute / 30), // 30 requests per minute for free
		BurstLimit: 3,
		Timeout:    10 * time.Second,
		ParseFunc:  parseIPGeolocationResponse,
	},
}

func NewAPIValidator(logger *zap.Logger) *APIValidator {
	validator := &APIValidator{
		logger:   logger,
		client:   &http.Client{Timeout: 15 * time.Second},
		limiters: make(map[string]*rate.Limiter),
	}

	// 初始化速率限制器
	for _, endpoint := range FreeAPIEndpoints {
		validator.limiters[endpoint.Name] = rate.NewLimiter(endpoint.RateLimit, endpoint.BurstLimit)
	}

	return validator
}

// ValidateIPInfo 通过多个第三方API交叉验证IP信息
func (av *APIValidator) ValidateIPInfo(ctx context.Context, ip string, conflictData []model.IPInfo) (*model.IPInfo, error) {
	av.logger.Info("Starting API cross-validation",
		zap.String("ip", ip),
		zap.Int("conflict_sources", len(conflictData)))

	// 并发查询多个API
	resultsChan := make(chan *model.IPInfo, len(FreeAPIEndpoints))
	var wg sync.WaitGroup

	for _, endpoint := range FreeAPIEndpoints {
		wg.Add(1)
		go func(ep APIEndpoint) {
			defer wg.Done()

			// 检查速率限制
			av.mutex.RLock()
			limiter := av.limiters[ep.Name]
			av.mutex.RUnlock()

			if !limiter.Allow() {
				av.logger.Debug("Rate limit exceeded, skipping API", zap.String("api", ep.Name))
				return
			}

			// 查询API
			ipInfo, err := av.queryAPI(ctx, ep, ip)
			if err != nil {
				av.logger.Debug("API query failed",
					zap.String("api", ep.Name),
					zap.Error(err))
				return
			}

			if ipInfo != nil {
				resultsChan <- ipInfo
			}
		}(endpoint)
	}

	// 等待所有查询完成
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// 收集结果
	var apiResults []model.IPInfo
	for result := range resultsChan {
		apiResults = append(apiResults, *result)
	}

	if len(apiResults) == 0 {
		return nil, fmt.Errorf("no API results available for validation")
	}

	// 合并冲突数据和API验证结果
	allData := append(conflictData, apiResults...)

	// 使用投票机制确定最可信的结果
	validatedInfo := av.performVoting(allData)

	av.logger.Info("API cross-validation completed",
		zap.String("ip", ip),
		zap.Int("api_results", len(apiResults)),
		zap.String("final_source", validatedInfo.Metadata.Source))

	return &validatedInfo, nil
}

// queryAPI 查询单个API
func (av *APIValidator) queryAPI(ctx context.Context, endpoint APIEndpoint, ip string) (*model.IPInfo, error) {
	url := fmt.Sprintf(endpoint.URL, ip)

	// 创建带超时的context
	queryCtx, cancel := context.WithTimeout(ctx, endpoint.Timeout)
	defer cancel()

	req, err := http.NewRequestWithContext(queryCtx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "IPInsight/1.0")

	resp, err := av.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析响应
	return endpoint.ParseFunc(body, endpoint.Name)
}

// performVoting 执行投票机制确定最可信的结果
func (av *APIValidator) performVoting(allData []model.IPInfo) model.IPInfo {
	if len(allData) == 0 {
		return model.IPInfo{}
	}

	if len(allData) == 1 {
		return allData[0]
	}

	// 统计各字段的投票结果
	countryVotes := make(map[string]int)
	regionVotes := make(map[string]int)
	cityVotes := make(map[string]int)
	ispVotes := make(map[string]int)

	for _, data := range allData {
		if data.Geolocation.Country.Code != "" {
			countryVotes[data.Geolocation.Country.Code]++
		}
		if data.Geolocation.Region.Code != "" {
			regionVotes[data.Geolocation.Region.Code]++
		}
		if data.Geolocation.City != "" {
			cityVotes[data.Geolocation.City]++
		}
		if data.Network.ISP != "" {
			ispVotes[data.Network.ISP]++
		}
	}

	// 选择得票最多的结果作为基础
	baseData := allData[0]

	// 应用投票结果
	if country := getMostVoted(countryVotes); country != "" {
		baseData.Geolocation.Country.Code = country
	}
	if region := getMostVoted(regionVotes); region != "" {
		baseData.Geolocation.Region.Code = region
	}
	if city := getMostVoted(cityVotes); city != "" {
		baseData.Geolocation.City = city
	}
	if isp := getMostVoted(ispVotes); isp != "" {
		baseData.Network.ISP = isp
	}

	// 更新元数据
	baseData.Metadata.Source = "api_validated"
	baseData.Metadata.LastUpdated = time.Now().Format(time.RFC3339)
	baseData.Metadata.Confidence = PtrInt(85) // 高置信度

	return baseData
}

// getMostVoted 获取得票最多的选项
func getMostVoted(votes map[string]int) string {
	maxVotes := 0
	winner := ""

	for option, count := range votes {
		if count > maxVotes {
			maxVotes = count
			winner = option
		}
	}

	return winner
}

// 以下是各API的响应解析函数

func parseIPAPIResponse(body []byte, source string) (*model.IPInfo, error) {
	var resp struct {
		Status      string  `json:"status"`
		Country     string  `json:"country"`
		CountryCode string  `json:"countryCode"`
		Region      string  `json:"region"`
		RegionName  string  `json:"regionName"`
		City        string  `json:"city"`
		Zip         string  `json:"zip"`
		Lat         float64 `json:"lat"`
		Lon         float64 `json:"lon"`
		Timezone    string  `json:"timezone"`
		ISP         string  `json:"isp"`
		Org         string  `json:"org"`
		AS          string  `json:"as"`
		Query       string  `json:"query"`
	}

	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	if resp.Status != "success" {
		return nil, fmt.Errorf("API returned error status")
	}

	// 为单个IP生成CIDR和确定IP版本
	var cidr, ipVersion string
	if strings.Contains(resp.Query, ":") {
		// IPv6
		cidr = resp.Query + "/128"
		ipVersion = "IPv6"
	} else {
		// IPv4
		cidr = resp.Query + "/32"
		ipVersion = "IPv4"
	}

	return &model.IPInfo{
		IPRange: model.IPRange{
			CIDR:      cidr,
			StartIP:   resp.Query,
			EndIP:     resp.Query,
			IPVersion: ipVersion,
		},
		Geolocation: model.Geolocation{
			Country: model.Country{
				Code: resp.CountryCode,
				Name: resp.Country,
			},
			Region: model.Region{
				Code: resp.Region,
				Name: resp.RegionName,
			},
			City:       resp.City,
			PostalCode: resp.Zip,
			Latitude:   PtrFloat64(resp.Lat),
			Longitude:  PtrFloat64(resp.Lon),
		},
		Network: model.Network{
			ISP:          resp.ISP,
			Organization: resp.Org,
			ASN:          resp.AS,
		},
		Timezone: model.Timezone{
			Name: resp.Timezone,
		},
		Metadata: model.Metadata{
			Source:      source,
			LastUpdated: time.Now().Format(time.RFC3339),
			Confidence:  PtrInt(70),
		},
	}, nil
}

func parseIPAPICoResponse(body []byte, source string) (*model.IPInfo, error) {
	// 实现ipapi.co的响应解析
	// 这里简化处理，实际应该根据API文档实现
	return parseIPAPIResponse(body, source)
}

func parseFreeIPAPIResponse(body []byte, source string) (*model.IPInfo, error) {
	// 实现freeipapi.com的响应解析
	return parseIPAPIResponse(body, source)
}

func parseIPWhoisResponse(body []byte, source string) (*model.IPInfo, error) {
	// 实现ipwhois.app的响应解析
	return parseIPAPIResponse(body, source)
}

func parseIPGeolocationResponse(body []byte, source string) (*model.IPInfo, error) {
	// 实现ipgeolocation.io的响应解析
	return parseIPAPIResponse(body, source)
}

// 辅助函数
func PtrInt(i int) *int {
	return &i
}

func PtrFloat64(f float64) *float64 {
	return &f
}
