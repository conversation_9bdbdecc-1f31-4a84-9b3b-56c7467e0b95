package service

import (
	"context"
	"fmt"
	"time"

	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
)

// UserService 用户服务接口
type UserService interface {
	// 用户管理
	CreateUser(ctx context.Context, req *model.CreateUserRequest, createdBy int64) (*model.User, error)
	GetUser(ctx context.Context, id int64) (*model.User, error)
	GetUserByUsername(ctx context.Context, username string) (*model.User, error)
	UpdateUser(ctx context.Context, id int64, req *model.UpdateUserRequest, updatedBy int64) (*model.User, error)
	DeleteUser(ctx context.Context, id int64) error
	ListUsers(ctx context.Context, page, pageSize int, role, status string) (*model.UserListResponse, error)

	// 认证相关
	AuthenticateUser(ctx context.Context, username, password string) (*model.User, error)
	AuthenticateAPIKey(ctx context.Context, apiKey string) (*model.User, error)

	// 密码管理
	ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error
	RequestPasswordReset(ctx context.Context, email string) (string, error)
	ResetPassword(ctx context.Context, token, newPassword string) error

	// API密钥管理
	GenerateAPIKey(ctx context.Context, userID int64) (string, error)
	RevokeAPIKey(ctx context.Context, userID int64) error

	// 用户状态管理
	LockUser(ctx context.Context, userID int64, duration time.Duration) error
	UnlockUser(ctx context.Context, userID int64) error
	ActivateUser(ctx context.Context, userID int64) error
	DeactivateUser(ctx context.Context, userID int64) error

	// 权限管理
	GrantPermission(ctx context.Context, userID int64, permission, resource string, grantedBy int64, expiresAt *time.Time) error
	RevokePermission(ctx context.Context, userID int64, permission, resource string) error
	HasPermission(ctx context.Context, userID int64, permission, resource string) (bool, error)
	GetUserPermissions(ctx context.Context, userID int64) ([]model.UserPermission, error)

	// 活动日志
	LogUserActivity(ctx context.Context, userID *int64, action, resource, resourceID string, details map[string]interface{}, ipAddress, userAgent string, success bool, errorMessage string) error
	GetUserActivityLogs(ctx context.Context, userID int64, page, pageSize int) ([]model.UserActivityLog, int64, error)

	// 统计信息
	GetUserStats(ctx context.Context) (*UserStats, error)
	GetUserSummaries(ctx context.Context) ([]model.UserSummary, error)
	GetUserActivitySummaries(ctx context.Context) ([]model.UserActivitySummary, error)

	// 维护任务
	CleanupExpiredSessions(ctx context.Context) (int, error)
	MaintenanceCleanup(ctx context.Context) error
}

// UserStats 用户统计信息
type UserStats struct {
	TotalUsers       int64 `json:"total_users"`
	ActiveUsers      int64 `json:"active_users"`
	InactiveUsers    int64 `json:"inactive_users"`
	LockedUsers      int64 `json:"locked_users"`
	AdminUsers       int64 `json:"admin_users"`
	OperatorUsers    int64 `json:"operator_users"`
	RegularUsers     int64 `json:"regular_users"`
	ReadonlyUsers    int64 `json:"readonly_users"`
	UsersWithAPIKeys int64 `json:"users_with_api_keys"`
	RecentLogins24h  int64 `json:"recent_logins_24h"`
}

// UserServiceImpl 用户服务实现
type UserServiceImpl struct {
	userRepo database.UserRepository
	logger   *zap.Logger
}

// NewUserService 创建用户服务
func NewUserService(userRepo database.UserRepository, logger *zap.Logger) UserService {
	return &UserServiceImpl{
		userRepo: userRepo,
		logger:   logger,
	}
}

// CreateUser 创建用户
func (s *UserServiceImpl) CreateUser(ctx context.Context, req *model.CreateUserRequest, createdBy int64) (*model.User, error) {
	// 验证用户名和邮箱是否已存在
	if _, err := s.userRepo.GetUserByUsername(ctx, req.Username); err == nil {
		return nil, fmt.Errorf("username already exists")
	}

	if req.Email != "" {
		if _, err := s.userRepo.GetUserByEmail(ctx, req.Email); err == nil {
			return nil, fmt.Errorf("email already exists")
		}
	}

	// 创建用户
	user, err := s.userRepo.CreateUser(ctx, req, createdBy)
	if err != nil {
		s.logger.Error("Failed to create user", zap.Error(err), zap.String("username", req.Username))
		return nil, err
	}

	s.logger.Info("User created successfully",
		zap.String("username", user.Username),
		zap.Int64("user_id", user.ID),
		zap.Int64("created_by", createdBy))

	return user, nil
}

// AuthenticateUser 用户认证
func (s *UserServiceImpl) AuthenticateUser(ctx context.Context, username, password string) (*model.User, error) {
	user, err := s.userRepo.VerifyPassword(ctx, username, password)
	if err != nil {
		// 记录失败的登录尝试
		if userForLog, _ := s.userRepo.GetUserByUsername(ctx, username); userForLog != nil {
			s.userRepo.LogActivity(ctx, &userForLog.ID, "login_failed", "auth", "login", nil, "", "", false, err.Error())
		}
		return nil, err
	}

	// 更新登录信息
	s.userRepo.UpdateLoginInfo(ctx, user.ID, "", true)

	// 记录成功的登录
	s.userRepo.LogActivity(ctx, &user.ID, "login_success", "auth", "login", nil, "", "", true, "")

	s.logger.Info("User authenticated successfully",
		zap.String("username", user.Username),
		zap.Int64("user_id", user.ID))

	return user, nil
}

// AuthenticateAPIKey API密钥认证
func (s *UserServiceImpl) AuthenticateAPIKey(ctx context.Context, apiKey string) (*model.User, error) {
	user, err := s.userRepo.GetUserByAPIKey(ctx, apiKey)
	if err != nil {
		return nil, err
	}

	// 记录API密钥使用
	s.userRepo.LogActivity(ctx, &user.ID, "api_key_used", "auth", "api_access", nil, "", "", true, "")

	return user, nil
}

// GetUser 获取用户信息
func (s *UserServiceImpl) GetUser(ctx context.Context, id int64) (*model.User, error) {
	return s.userRepo.GetUserByID(ctx, id)
}

// GetUserByUsername 根据用户名获取用户
func (s *UserServiceImpl) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	return s.userRepo.GetUserByUsername(ctx, username)
}

// UpdateUser 更新用户信息
func (s *UserServiceImpl) UpdateUser(ctx context.Context, id int64, req *model.UpdateUserRequest, updatedBy int64) (*model.User, error) {
	// 检查邮箱是否已被其他用户使用
	if req.Email != "" {
		if existingUser, err := s.userRepo.GetUserByEmail(ctx, req.Email); err == nil && existingUser.ID != id {
			return nil, fmt.Errorf("email already exists")
		}
	}

	user, err := s.userRepo.UpdateUser(ctx, id, req, updatedBy)
	if err != nil {
		s.logger.Error("Failed to update user", zap.Error(err), zap.Int64("user_id", id))
		return nil, err
	}

	s.logger.Info("User updated successfully",
		zap.String("username", user.Username),
		zap.Int64("user_id", user.ID),
		zap.Int64("updated_by", updatedBy))

	return user, nil
}

// DeleteUser 删除用户
func (s *UserServiceImpl) DeleteUser(ctx context.Context, id int64) error {
	// 获取用户信息用于日志
	user, err := s.userRepo.GetUserByID(ctx, id)
	if err != nil {
		return err
	}

	// 删除用户的所有会话
	s.userRepo.DeleteUserSessions(ctx, id)

	// 软删除用户
	err = s.userRepo.DeleteUser(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete user", zap.Error(err), zap.Int64("user_id", id))
		return err
	}

	s.logger.Info("User deleted successfully",
		zap.String("username", user.Username),
		zap.Int64("user_id", id))

	return nil
}

// ListUsers 获取用户列表
func (s *UserServiceImpl) ListUsers(ctx context.Context, page, pageSize int, role, status string) (*model.UserListResponse, error) {
	return s.userRepo.ListUsers(ctx, page, pageSize, role, status)
}

// ChangePassword 修改密码
func (s *UserServiceImpl) ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error {
	err := s.userRepo.ChangePassword(ctx, userID, currentPassword, newPassword)
	if err != nil {
		s.logger.Error("Failed to change password", zap.Error(err), zap.Int64("user_id", userID))
		return err
	}

	// 删除用户的所有会话，强制重新登录
	s.userRepo.DeleteUserSessions(ctx, userID)

	s.logger.Info("Password changed successfully", zap.Int64("user_id", userID))
	return nil
}

// RequestPasswordReset 请求密码重置
func (s *UserServiceImpl) RequestPasswordReset(ctx context.Context, email string) (string, error) {
	token, err := s.userRepo.GeneratePasswordResetToken(ctx, email)
	if err != nil {
		s.logger.Error("Failed to generate password reset token", zap.Error(err), zap.String("email", email))
		return "", err
	}

	s.logger.Info("Password reset token generated", zap.String("email", email))
	return token, nil
}

// ResetPassword 重置密码
func (s *UserServiceImpl) ResetPassword(ctx context.Context, token, newPassword string) error {
	err := s.userRepo.ResetPassword(ctx, token, newPassword)
	if err != nil {
		s.logger.Error("Failed to reset password", zap.Error(err))
		return err
	}

	s.logger.Info("Password reset successfully")
	return nil
}

// GenerateAPIKey 生成API密钥
func (s *UserServiceImpl) GenerateAPIKey(ctx context.Context, userID int64) (string, error) {
	apiKey, err := s.userRepo.GenerateAPIKey(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to generate API key", zap.Error(err), zap.Int64("user_id", userID))
		return "", err
	}

	s.logger.Info("API key generated successfully", zap.Int64("user_id", userID))
	return apiKey, nil
}

// RevokeAPIKey 撤销API密钥
func (s *UserServiceImpl) RevokeAPIKey(ctx context.Context, userID int64) error {
	err := s.userRepo.RevokeAPIKey(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to revoke API key", zap.Error(err), zap.Int64("user_id", userID))
		return err
	}

	s.logger.Info("API key revoked successfully", zap.Int64("user_id", userID))
	return nil
}

// LockUser 锁定用户
func (s *UserServiceImpl) LockUser(ctx context.Context, userID int64, duration time.Duration) error {
	err := s.userRepo.LockUser(ctx, userID, duration)
	if err != nil {
		s.logger.Error("Failed to lock user", zap.Error(err), zap.Int64("user_id", userID))
		return err
	}

	// 删除用户的所有会话
	s.userRepo.DeleteUserSessions(ctx, userID)

	s.logger.Info("User locked successfully", zap.Int64("user_id", userID), zap.Duration("duration", duration))
	return nil
}

// UnlockUser 解锁用户
func (s *UserServiceImpl) UnlockUser(ctx context.Context, userID int64) error {
	err := s.userRepo.UnlockUser(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to unlock user", zap.Error(err), zap.Int64("user_id", userID))
		return err
	}

	s.logger.Info("User unlocked successfully", zap.Int64("user_id", userID))
	return nil
}

// HasPermission 检查用户权限
func (s *UserServiceImpl) HasPermission(ctx context.Context, userID int64, permission, resource string) (bool, error) {
	return s.userRepo.HasPermission(ctx, userID, permission, resource)
}

// LogUserActivity 记录用户活动
func (s *UserServiceImpl) LogUserActivity(ctx context.Context, userID *int64, action, resource, resourceID string, details map[string]interface{}, ipAddress, userAgent string, success bool, errorMessage string) error {
	return s.userRepo.LogActivity(ctx, userID, action, resource, resourceID, details, ipAddress, userAgent, success, errorMessage)
}

// GetUserStats 获取用户统计信息
func (s *UserServiceImpl) GetUserStats(ctx context.Context) (*UserStats, error) {
	totalUsers, err := s.userRepo.GetUserCount(ctx)
	if err != nil {
		return nil, err
	}

	activeUsers, err := s.userRepo.GetActiveUserCount(ctx)
	if err != nil {
		return nil, err
	}

	// 这里可以添加更多的统计查询
	stats := &UserStats{
		TotalUsers:  totalUsers,
		ActiveUsers: activeUsers,
		// 其他统计信息可以根据需要添加
	}

	return stats, nil
}

// CleanupExpiredSessions 清理过期会话
func (s *UserServiceImpl) CleanupExpiredSessions(ctx context.Context) (int, error) {
	count, err := s.userRepo.CleanupExpiredSessions(ctx)
	if err != nil {
		s.logger.Error("Failed to cleanup expired sessions", zap.Error(err))
		return 0, err
	}

	if count > 0 {
		s.logger.Info("Cleaned up expired sessions", zap.Int("count", count))
	}

	return count, nil
}

// ActivateUser 激活用户
func (s *UserServiceImpl) ActivateUser(ctx context.Context, userID int64) error {
	req := &model.UpdateUserRequest{Status: model.UserStatusActive}
	_, err := s.userRepo.UpdateUser(ctx, userID, req, 0) // 系统操作
	if err != nil {
		s.logger.Error("Failed to activate user", zap.Error(err), zap.Int64("user_id", userID))
		return err
	}

	s.logger.Info("User activated successfully", zap.Int64("user_id", userID))
	return nil
}

// DeactivateUser 停用用户
func (s *UserServiceImpl) DeactivateUser(ctx context.Context, userID int64) error {
	req := &model.UpdateUserRequest{Status: model.UserStatusInactive}
	_, err := s.userRepo.UpdateUser(ctx, userID, req, 0) // 系统操作
	if err != nil {
		s.logger.Error("Failed to deactivate user", zap.Error(err), zap.Int64("user_id", userID))
		return err
	}

	// 删除用户的所有会话
	s.userRepo.DeleteUserSessions(ctx, userID)

	s.logger.Info("User deactivated successfully", zap.Int64("user_id", userID))
	return nil
}

// GrantPermission 授予权限
func (s *UserServiceImpl) GrantPermission(ctx context.Context, userID int64, permission, resource string, grantedBy int64, expiresAt *time.Time) error {
	err := s.userRepo.GrantPermission(ctx, userID, permission, resource, grantedBy, expiresAt)
	if err != nil {
		s.logger.Error("Failed to grant permission", zap.Error(err), zap.Int64("user_id", userID), zap.String("permission", permission))
		return err
	}

	s.logger.Info("Permission granted successfully",
		zap.Int64("user_id", userID),
		zap.String("permission", permission),
		zap.String("resource", resource),
		zap.Int64("granted_by", grantedBy))
	return nil
}

// RevokePermission 撤销权限
func (s *UserServiceImpl) RevokePermission(ctx context.Context, userID int64, permission, resource string) error {
	err := s.userRepo.RevokePermission(ctx, userID, permission, resource)
	if err != nil {
		s.logger.Error("Failed to revoke permission", zap.Error(err), zap.Int64("user_id", userID), zap.String("permission", permission))
		return err
	}

	s.logger.Info("Permission revoked successfully",
		zap.Int64("user_id", userID),
		zap.String("permission", permission),
		zap.String("resource", resource))
	return nil
}

// GetUserPermissions 获取用户权限
func (s *UserServiceImpl) GetUserPermissions(ctx context.Context, userID int64) ([]model.UserPermission, error) {
	return s.userRepo.GetUserPermissions(ctx, userID)
}

// GetUserActivityLogs 获取用户活动日志
func (s *UserServiceImpl) GetUserActivityLogs(ctx context.Context, userID int64, page, pageSize int) ([]model.UserActivityLog, int64, error) {
	return s.userRepo.GetUserActivityLogs(ctx, userID, page, pageSize)
}

// GetUserSummaries 获取用户摘要
func (s *UserServiceImpl) GetUserSummaries(ctx context.Context) ([]model.UserSummary, error) {
	return s.userRepo.GetUserSummaries(ctx)
}

// GetUserActivitySummaries 获取用户活动摘要
func (s *UserServiceImpl) GetUserActivitySummaries(ctx context.Context) ([]model.UserActivitySummary, error) {
	return s.userRepo.GetUserActivitySummaries(ctx)
}

// MaintenanceCleanup 维护清理
func (s *UserServiceImpl) MaintenanceCleanup(ctx context.Context) error {
	// 清理过期会话
	sessionCount, err := s.CleanupExpiredSessions(ctx)
	if err != nil {
		s.logger.Error("Failed to cleanup expired sessions", zap.Error(err))
		return err
	}

	s.logger.Info("Maintenance cleanup completed", zap.Int("expired_sessions_cleaned", sessionCount))
	return nil
}
