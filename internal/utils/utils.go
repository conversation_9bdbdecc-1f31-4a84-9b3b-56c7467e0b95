package utils

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
)

type Auth struct {
	Username string
	Password string
	Type     *string
}

// DownloadOptions 下载选项配置
type DownloadOptions struct {
	MaxRetries      int           // 最大重试次数
	RetryDelay      time.Duration // 重试延迟
	Timeout         time.Duration // 请求超时
	MinFileSize     int64         // 最小文件大小（字节）
	ValidateFile    bool          // 是否验证文件完整性
	CleanupOnFail   bool          // 失败时是否清理文件
	CheckRemoteSize bool          // 是否检查远程文件大小
	ShowProgress    bool          // 是否显示下载进度
	ResumeDownload  bool          // 是否支持断点续传
	SkipIfExists    bool          // 如果文件存在且大小匹配则跳过
}

// DefaultDownloadOptions 默认下载选项
func DefaultDownloadOptions() *DownloadOptions {
	return &DownloadOptions{
		MaxRetries:      3,
		RetryDelay:      2 * time.Second,
		Timeout:         60 * time.Second,
		MinFileSize:     1024, // 1KB
		ValidateFile:    true,
		CleanupOnFail:   true,
		CheckRemoteSize: true,
		ShowProgress:    true,
		ResumeDownload:  true,
		SkipIfExists:    true,
	}
}

// RemoteFileInfo 远程文件信息
type RemoteFileInfo struct {
	Size         int64
	LastModified time.Time
	ETag         string
	AcceptRanges bool
}

// getRemoteFileInfo 获取远程文件信息
func getRemoteFileInfo(ctx context.Context, downloadUrl string, auth *Auth, timeout time.Duration, logger *zap.Logger) (*RemoteFileInfo, error) {
	client := http.Client{Timeout: timeout}
	req, err := http.NewRequestWithContext(ctx, http.MethodHead, downloadUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("create HEAD request: %w", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "ipInsight/1.0 (Data Fetcher)")

	if auth != nil {
		if auth.Type == nil || *auth.Type == "basic" {
			req.SetBasicAuth(auth.Username, auth.Password)
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HEAD request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HEAD request returned status: %d", resp.StatusCode)
	}

	info := &RemoteFileInfo{
		Size:         resp.ContentLength,
		ETag:         resp.Header.Get("ETag"),
		AcceptRanges: resp.Header.Get("Accept-Ranges") == "bytes",
	}

	// 解析最后修改时间
	if lastModStr := resp.Header.Get("Last-Modified"); lastModStr != "" {
		if lastMod, err := time.Parse(time.RFC1123, lastModStr); err == nil {
			info.LastModified = lastMod
		}
	}

	logger.Debug("Remote file info retrieved",
		zap.String("url", downloadUrl),
		zap.Int64("size", info.Size),
		zap.Time("last_modified", info.LastModified),
		zap.String("etag", info.ETag),
		zap.Bool("accept_ranges", info.AcceptRanges))

	return info, nil
}

// shouldSkipDownload 检查是否应该跳过下载
func shouldSkipDownload(filePath string, remoteInfo *RemoteFileInfo, logger *zap.Logger) bool {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		// 文件不存在，需要下载
		return false
	}

	// 检查文件大小
	if remoteInfo.Size > 0 && fileInfo.Size() != remoteInfo.Size {
		logger.Info("Local file size differs from remote",
			zap.String("file", filePath),
			zap.Int64("local_size", fileInfo.Size()),
			zap.Int64("remote_size", remoteInfo.Size))
		return false
	}

	// 检查最后修改时间
	if !remoteInfo.LastModified.IsZero() && fileInfo.ModTime().Before(remoteInfo.LastModified) {
		logger.Info("Local file is older than remote",
			zap.String("file", filePath),
			zap.Time("local_time", fileInfo.ModTime()),
			zap.Time("remote_time", remoteInfo.LastModified))
		return false
	}

	logger.Info("Skipping download - local file is up to date",
		zap.String("file", filePath),
		zap.Int64("size", fileInfo.Size()))
	return true
}

// DownloadWithRetry 带重试机制的下载函数
func DownloadWithRetry(ctx context.Context, downloadUrl string, dest *os.File, logger *zap.Logger, auth *Auth, options *DownloadOptions) error {
	if options == nil {
		options = DefaultDownloadOptions()
	}

	// 检查远程文件信息
	var remoteInfo *RemoteFileInfo
	if options.CheckRemoteSize || options.SkipIfExists {
		var err error
		remoteInfo, err = getRemoteFileInfo(ctx, downloadUrl, auth, options.Timeout, logger)
		if err != nil {
			logger.Warn("Failed to get remote file info, proceeding with download", zap.Error(err))
		}
	}

	// 检查是否可以跳过下载
	if options.SkipIfExists && remoteInfo != nil {
		if shouldSkipDownload(dest.Name(), remoteInfo, logger) {
			return nil
		}
	}

	var lastErr error
	for attempt := 0; attempt <= options.MaxRetries; attempt++ {
		if attempt > 0 {
			logger.Info("Retrying download",
				zap.String("url", downloadUrl),
				zap.Int("attempt", attempt),
				zap.Int("max_retries", options.MaxRetries))

			// 等待重试延迟
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(options.RetryDelay):
			}

			// 重置文件指针到开始位置
			if _, err := dest.Seek(0, 0); err != nil {
				logger.Error("Failed to reset file pointer", zap.Error(err))
				return fmt.Errorf("reset file pointer: %w", err)
			}

			// 截断文件
			if err := dest.Truncate(0); err != nil {
				logger.Error("Failed to truncate file", zap.Error(err))
				return fmt.Errorf("truncate file: %w", err)
			}
		}

		err := downloadSingle(ctx, downloadUrl, dest, logger, auth, options, remoteInfo)
		if err == nil {
			// 下载成功，验证文件
			if options.ValidateFile {
				if err := validateDownloadedFile(dest, options, logger); err != nil {
					logger.Error("File validation failed", zap.Error(err))
					lastErr = fmt.Errorf("file validation failed: %w", err)
					continue
				}
			}

			logger.Info("Successfully downloaded and validated file",
				zap.String("url", downloadUrl),
				zap.Int("attempts", attempt+1))
			return nil
		}

		lastErr = err
		logger.Warn("Download attempt failed",
			zap.String("url", downloadUrl),
			zap.Int("attempt", attempt+1),
			zap.Error(err))
	}

	// 所有重试都失败了，清理文件
	if options.CleanupOnFail {
		if err := cleanupFailedDownload(dest, logger); err != nil {
			logger.Error("Failed to cleanup failed download", zap.Error(err))
		}
	}

	return fmt.Errorf("download failed after %d attempts: %w", options.MaxRetries+1, lastErr)
}

// ProgressWriter 进度显示写入器
type ProgressWriter struct {
	file         *os.File
	total        int64
	written      int64
	logger       *zap.Logger
	showProgress bool
	lastReport   time.Time
}

func (pw *ProgressWriter) Write(p []byte) (int, error) {
	n, err := pw.file.Write(p)
	if err != nil {
		return n, err
	}

	pw.written += int64(n)

	// 每秒最多报告一次进度
	if pw.showProgress && time.Since(pw.lastReport) > time.Second {
		if pw.total > 0 {
			percentage := float64(pw.written) / float64(pw.total) * 100
			pw.logger.Info("Download progress",
				zap.String("file", pw.file.Name()),
				zap.Int64("written", pw.written),
				zap.Int64("total", pw.total),
				zap.Float64("percentage", percentage))
		} else {
			pw.logger.Info("Download progress",
				zap.String("file", pw.file.Name()),
				zap.Int64("written", pw.written))
		}
		pw.lastReport = time.Now()
	}

	return n, err
}

// downloadSingle 单次下载尝试
func downloadSingle(ctx context.Context, downloadUrl string, dest *os.File, logger *zap.Logger, auth *Auth, options *DownloadOptions, remoteInfo *RemoteFileInfo) error {
	client := http.Client{Timeout: options.Timeout}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, downloadUrl, nil)
	if err != nil {
		return fmt.Errorf("create request: %w", err)
	}

	// 设置User-Agent以避免被某些服务器拒绝
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")

	// 断点续传支持
	var startPos int64 = 0
	if options.ResumeDownload && remoteInfo != nil && remoteInfo.AcceptRanges {
		if fileInfo, err := dest.Stat(); err == nil && fileInfo.Size() > 0 {
			startPos = fileInfo.Size()
			req.Header.Set("Range", fmt.Sprintf("bytes=%d-", startPos))
			logger.Info("Resuming download from position",
				zap.String("url", downloadUrl),
				zap.Int64("start_pos", startPos))
		}
	}

	if auth != nil {
		if auth.Type == nil || *auth.Type == "basic" {
			req.SetBasicAuth(auth.Username, auth.Password)
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("fetch data: %w", err)
	}
	defer resp.Body.Close()

	// 检查状态码（支持断点续传的206和正常的200）
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusPartialContent {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// 如果是断点续传，需要定位到文件末尾
	if startPos > 0 && resp.StatusCode == http.StatusPartialContent {
		if _, err := dest.Seek(0, io.SeekEnd); err != nil {
			return fmt.Errorf("seek to end of file: %w", err)
		}
	}

	// 获取总文件大小
	var totalSize int64 = resp.ContentLength
	if remoteInfo != nil && remoteInfo.Size > 0 {
		totalSize = remoteInfo.Size
	}

	// 检查Content-Length
	if resp.ContentLength > 0 && resp.ContentLength < options.MinFileSize {
		return fmt.Errorf("file too small: %d bytes (minimum: %d)", resp.ContentLength, options.MinFileSize)
	}

	// 创建进度写入器
	progressWriter := &ProgressWriter{
		file:         dest,
		total:        totalSize,
		written:      startPos, // 从已有的位置开始计算
		logger:       logger,
		showProgress: options.ShowProgress,
		lastReport:   time.Now(),
	}

	written, err := io.Copy(progressWriter, resp.Body)
	if err != nil {
		return fmt.Errorf("save data: %w", err)
	}

	totalWritten := startPos + written

	// 检查实际写入的字节数
	if totalWritten < options.MinFileSize {
		return fmt.Errorf("downloaded file too small: %d bytes (minimum: %d)", totalWritten, options.MinFileSize)
	}

	// 最终进度报告
	if options.ShowProgress {
		logger.Info("Download completed",
			zap.String("url", downloadUrl),
			zap.Int64("total_size", totalWritten),
			zap.Int64("downloaded", written),
			zap.Bool("resumed", startPos > 0))
	}

	return nil
}

// validateDownloadedFile 验证下载的文件
func validateDownloadedFile(file *os.File, options *DownloadOptions, logger *zap.Logger) error {
	// 获取文件信息
	info, err := file.Stat()
	if err != nil {
		return fmt.Errorf("get file info: %w", err)
	}

	// 检查文件大小
	if info.Size() < options.MinFileSize {
		return fmt.Errorf("file size too small: %d bytes (minimum: %d)", info.Size(), options.MinFileSize)
	}

	// 检查文件是否为空或只包含空白字符
	if info.Size() == 0 {
		return fmt.Errorf("file is empty")
	}

	// 重置文件指针并读取前几个字节来检查文件格式
	if _, err := file.Seek(0, 0); err != nil {
		return fmt.Errorf("seek file: %w", err)
	}

	buffer := make([]byte, 512) // 读取前512字节用于检测
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return fmt.Errorf("read file for validation: %w", err)
	}

	// 检查是否是HTML错误页面（常见的下载失败情况）
	content := string(buffer[:n])
	if strings.Contains(strings.ToLower(content), "<html") ||
		strings.Contains(strings.ToLower(content), "<!doctype html") {
		return fmt.Errorf("downloaded file appears to be an HTML page, not the expected data file")
	}

	// 重置文件指针到开始位置
	if _, err := file.Seek(0, 0); err != nil {
		return fmt.Errorf("reset file pointer after validation: %w", err)
	}

	logger.Debug("File validation passed",
		zap.String("filename", file.Name()),
		zap.Int64("size", info.Size()))

	return nil
}

// cleanupFailedDownload 清理失败的下载文件
func cleanupFailedDownload(file *os.File, logger *zap.Logger) error {
	filename := file.Name()

	// 关闭文件
	if err := file.Close(); err != nil {
		logger.Error("Failed to close file during cleanup", zap.String("file", filename), zap.Error(err))
	}

	// 删除文件
	if err := os.Remove(filename); err != nil {
		if !os.IsNotExist(err) {
			return fmt.Errorf("remove failed download file: %w", err)
		}
	}

	logger.Info("Cleaned up failed download file", zap.String("file", filename))
	return nil
}

// Download 保持向后兼容的下载函数
func Download(ctx context.Context, downloadUrl string, dest *os.File, logger *zap.Logger, auth *Auth) error {
	return DownloadWithRetry(ctx, downloadUrl, dest, logger, auth, DefaultDownloadOptions())
}

func ExtractTarGz(src, destDir string, logger *zap.Logger) error {
	// Open the tar.gz file
	file, err := os.Open(src)
	if err != nil {
		logger.Error("open file Error")
		return err
	}
	defer file.Close()
	gzr, err := gzip.NewReader(file)
	if err != nil {
		logger.Error("Create a gzip reader Error")
		return err
	}
	defer gzr.Close()
	tr := tar.NewReader(gzr)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return err
	}
	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		target := filepath.Join(destDir, header.Name)
		// 安全检查：确保解压路径在目标目录内
		cleanDestDir := filepath.Clean(destDir) + string(os.PathSeparator)
		cleanTarget := filepath.Clean(target)
		if !strings.HasPrefix(cleanTarget, cleanDestDir) && cleanTarget != filepath.Clean(destDir) {
			return os.ErrInvalid
		}
		switch header.Typeflag {
		case tar.TypeDir:
			if err := os.MkdirAll(target, os.FileMode(header.Mode)); err != nil {
				return err
			}
		case tar.TypeReg:
			f, err := os.OpenFile(target, os.O_CREATE|os.O_RDWR, os.FileMode(header.Mode))
			if err != nil {
				return err
			}
			defer f.Close()
			if _, err := io.Copy(f, tr); err != nil {
				return err
			}
		default:
			continue
		}
	}

	return nil
}

func ExtractZip(src, destDir string, logger *zap.Logger) error {
	reader, err := zip.OpenReader(src)
	if err != nil {
		logger.Error("Failed to open zip file", zap.String("path", src), zap.Error(err))
		return err
	}
	defer reader.Close()

	if err := os.MkdirAll(destDir, 0755); err != nil {
		logger.Error("Failed to create destination directory", zap.String("dir", destDir), zap.Error(err))
		return err
	}

	for _, file := range reader.File {
		filePath := filepath.Join(destDir, file.Name)
		if !strings.HasPrefix(filePath, filepath.Clean(destDir)+string(os.PathSeparator)) {
			logger.Warn("Invalid file path detected", zap.String("path", filePath))
			continue
		}
		if file.FileInfo().IsDir() {
			if err := os.MkdirAll(filePath, file.Mode()); err != nil {
				logger.Error("Failed to create directory", zap.String("path", filePath), zap.Error(err))
				return err
			}
			logger.Debug("Created directory", zap.String("path", filePath))
			continue
		}
		if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
			logger.Error("Failed to create parent directory", zap.String("path", filePath), zap.Error(err))
			return err
		}
		srcFile, err := file.Open()
		if err != nil {
			logger.Error("Failed to open file in zip", zap.String("path", file.Name), zap.Error(err))
			return err
		}
		defer srcFile.Close()
		destFile, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			logger.Error("Failed to create destination file", zap.String("path", filePath), zap.Error(err))
			return err
		}
		defer destFile.Close()
		if _, err := io.Copy(destFile, srcFile); err != nil {
			logger.Error("Failed to extract file", zap.String("path", filePath), zap.Error(err))
			return err
		}
		logger.Debug("Extracted file", zap.String("path", filePath))
	}
	logger.Info("Successfully extracted zip file", zap.String("src", src), zap.String("dest", destDir))
	return nil
}

func CheckFresh(info os.FileInfo, duration time.Duration) bool {
	// Get the last modified time of the file
	modTime := info.ModTime()
	fresh := time.Since(modTime) <= duration

	// 文件必须有内容且在有效期内
	if info.Size() > 0 && fresh {
		return true
	}
	return false
}

// NoNeedDownloadOptions 下载去重检查选项
type NoNeedDownloadOptions struct {
	Duration *time.Duration // 检查时间范围，默认7天
	Force    bool           // 是否强制下载，忽略时间检查
}

// DefaultNoNeedDownloadOptions 默认下载去重选项
func DefaultNoNeedDownloadOptions() *NoNeedDownloadOptions {
	defaultDuration := 7 * 24 * time.Hour
	return &NoNeedDownloadOptions{
		Duration: &defaultDuration,
		Force:    false,
	}
}

// NoNeedDownloadWithOptions 检查是否需要下载文件（支持强制下载选项）
func NoNeedDownloadWithOptions(filePath string, logger *zap.Logger, options *NoNeedDownloadOptions) (bool, error) {
	if options == nil {
		options = DefaultNoNeedDownloadOptions()
	}

	// 如果强制下载，直接返回需要下载
	if options.Force {
		logger.Info("Force download enabled, skipping file check", zap.String("path", filePath))
		return false, nil
	}

	info, err := os.Stat(filePath)
	dur := 7 * 24 * time.Hour
	if options.Duration != nil {
		dur = *options.Duration
	}

	if err == nil {
		fresh := CheckFresh(info, dur)
		if fresh {
			logger.Info("File already exists and is fresh, skipping download",
				zap.String("path", filePath),
				zap.Duration("check_duration", dur),
				zap.Time("file_mod_time", info.ModTime()),
				zap.Duration("file_age", time.Since(info.ModTime())))
			return true, nil
		} else {
			logger.Info("File exists but is stale, will download",
				zap.String("path", filePath),
				zap.Duration("check_duration", dur),
				zap.Time("file_mod_time", info.ModTime()),
				zap.Duration("file_age", time.Since(info.ModTime())))
		}
	} else {
		if !os.IsNotExist(err) {
			logger.Error("Failed to stat file", zap.String("path", filePath), zap.Error(err))
			return false, fmt.Errorf("stat file: %w", err)
		}
		logger.Info("File does not exist, will download", zap.String("path", filePath))
	}
	return false, nil
}

// NoNeedDownload 保持向后兼容的函数
func NoNeedDownload(mmdbPath string, logger *zap.Logger, duration *time.Duration) (bool, error) {
	options := &NoNeedDownloadOptions{
		Duration: duration,
		Force:    false,
	}
	return NoNeedDownloadWithOptions(mmdbPath, logger, options)
}

func GetLogger() (*zap.Logger, error) {

	logMode := os.Getenv("LOG_MODE")
	if strings.TrimSpace(logMode) == "" {
		logMode = "development"
	}

	switch logMode {
	case "production":
		logger, err := zap.NewProduction()
		if err != nil {
			return nil, fmt.Errorf("创建生产模式 Logger 失败: %v", err)
		}
		logger.Info("已初始化生产模式 Logger")
		return logger, nil
	case "development":
		logger, err := zap.NewDevelopment()
		if err != nil {
			return nil, fmt.Errorf("创建开发模式 Logger 失败: %v", err)
		}
		logger.Info("已初始化开发模式 Logger")
		return logger, nil
	default:
		return nil, fmt.Errorf("无效的 LOG_MODE: %s，仅支持 'production' 或 'development'", logMode)
	}
}

func ReadExtFiles(_path string, ext string) ([]string, error) {
	entries, err := os.ReadDir(_path)
	if err != nil {
		return nil, err
	}
	dbList := []string{}
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ext) {
			newPath := filepath.Join(_path, entry.Name())
			dbList = append(dbList, newPath)
		}
	}
	return dbList, nil
}

func ConcurrencyFn[T, R any](process func(t T), con int, tasks []T) {
	jobs := make(chan T, len(tasks))
	var wg sync.WaitGroup
	// add con goroutine workers
	for i := range con {
		wg.Add(1)
		go func(worderId int) {
			defer wg.Done()
			fmt.Println("worderId:", worderId)
			for job := range jobs {
				process(job)
			}
		}(i)
	}
	for _, task := range tasks {
		jobs <- task
	}
	close(jobs)
	wg.Wait()
	fmt.Println("finished all tasks.")
}

func SetDebugLimit() int {
	var debugLimit int
	if num, err := strconv.Atoi(os.Getenv("DEBUG_LIMIT")); err == nil {
		debugLimit = num
	} else {
		// 默认值：-1 表示无限制（生产模式），10表示调试模式
		// 可通过环境变量 DEBUG_LIMIT 设置具体数值
		debugLimit = -1 // 无限制，正常处理所有数据
	}
	return debugLimit
}

// SaveInfoToJSON 将给定的 batch 切片（或任何可 JSON 序列化的数据）保存到源代码文件所在目录的目录名.json 文件中。
// batch 可以是任意支持 json.Marshal 的类型，例如 []string、[]int 或自定义结构体切片。
// 返回错误如果发生任何问题。
func SaveInfoToJSON(batch any, _filename string) error {
	// 获取源代码文件所在目录
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("error getting current directory: %w", err)
	}

	// 构建文件名：_filename + ".json"
	filename := filepath.Join(dir, "examples", _filename+".json") // 确保文件路径在该目录下
	fmt.Println("processing _batch sliceing...")
	var _batch []any
	for i, b := range batch.([]model.IPInfo) {
		if i > 80 && i < 90 {
			_batch = append(_batch, b)
		}
	}
	// 将 batch 编码为 JSON
	data, err := json.Marshal(_batch)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %w", err)
	}

	// 写入文件到源代码目录
	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		return fmt.Errorf("error writing file: %w", err)
	}

	fmt.Printf("Batch saved to %s successfully.\n", filename)
	return nil
}
func ShowProcess(processed int, limit *int) {}
func ShowProcess1(processed int, limit *int) {
	var _limit int
	if limit == nil {
		_limit = 1e3

	} else {
		_limit = *limit
	}
	if processed%_limit == 0 {
		fmt.Printf("processed: %d\n", processed)
	}
}
