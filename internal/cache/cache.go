package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type Cache struct {
	client       *redis.Client
	logger       *zap.Logger
	hotIPManager *HotIPManager
}

func NewCache(config config.CacheConfig, logger *zap.Logger) *Cache {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password: config.Password,
		DB:       0,
	})

	cache := &Cache{
		client: client,
		logger: logger,
	}

	// 初始化热门IP管理器
	cache.hotIPManager = NewHotIPManager(cache, logger)

	// 加载持久化的统计数据
	if err := cache.hotIPManager.LoadStats(); err != nil {
		logger.Warn("Failed to load hot IP stats", zap.Error(err))
	}

	return cache
}

func (c *Cache) Get(ctx context.Context, key string) (*model.IPInfo, error) {
	// 记录IP访问
	c.hotIPManager.RecordAccess(key)

	data, err := c.client.Get(ctx, key).Bytes()
	if err == redis.Nil {
		return nil, nil
	}
	if err != nil {
		c.logger.Warn("Failed to get from cache", zap.Error(err))
		return nil, err
	}

	var ipInfo model.IPInfo
	if err := json.Unmarshal(data, &ipInfo); err != nil {
		return nil, err
	}
	return &ipInfo, nil
}

func (c *Cache) Set(ctx context.Context, key string, ipInfo *model.IPInfo) error {
	data, err := json.Marshal(ipInfo)
	if err != nil {
		return err
	}

	// 为热门IP设置更长的TTL
	ttl := time.Hour
	if c.hotIPManager.IsHotIP(key) {
		ttl = c.hotIPManager.config.CacheTTL
		c.logger.Debug("Using extended TTL for hot IP",
			zap.String("ip", key),
			zap.Duration("ttl", ttl))
	}

	return c.client.Set(ctx, key, data, ttl).Err()
}

// GetHotIPStats 获取热门IP统计信息
func (c *Cache) GetHotIPStats() map[string]interface{} {
	return c.hotIPManager.GetStats()
}

// GetHotIPs 获取热门IP列表
func (c *Cache) GetHotIPs() []HotIPInfo {
	return c.hotIPManager.GetHotIPs()
}

// IsHotIP 检查是否为热门IP
func (c *Cache) IsHotIP(ip string) bool {
	return c.hotIPManager.IsHotIP(ip)
}

// Ping 检查Redis连接
func (c *Cache) Ping(ctx context.Context) error {
	return c.client.Ping(ctx).Err()
}
