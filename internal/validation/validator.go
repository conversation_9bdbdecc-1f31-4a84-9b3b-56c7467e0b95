package validation

import (
	"net"
	"regexp"
	"strings"

	"github.com/cosin2077/ipInsight/internal/errors"
)

// IPValidator IP地址验证器
type IPValidator struct{}

// NewIPValidator 创建IP验证器
func NewIPValidator() *IPValidator {
	return &IPValidator{}
}

// ValidateIP 验证IP地址格式
func (v *IPValidator) ValidateIP(ip string) error {
	if ip == "" {
		return errors.ErrInvalidIP.WithDetails("IP address cannot be empty")
	}

	// 去除空白字符
	ip = strings.TrimSpace(ip)

	// 验证IP地址格式
	if net.ParseIP(ip) == nil {
		return errors.ErrInvalidIP.WithDetails("Invalid IP address format: " + ip)
	}

	return nil
}

// ValidateIPList 验证IP地址列表
func (v *IPValidator) ValidateIPList(ips []string) error {
	if len(ips) == 0 {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"IP list cannot be empty",
			400,
		)
	}

	if len(ips) > 100 { // 限制批量查询数量
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Too many IPs in batch request (max: 100)",
			400,
		)
	}

	for i, ip := range ips {
		if err := v.ValidateIP(ip); err != nil {
			return errors.NewAppError(
				errors.ErrCodeInvalidIP,
				"Invalid IP at index "+string(rune(i))+": "+ip,
				400,
			).WithDetails(err.Error())
		}
	}

	return nil
}

// UsernameValidator 用户名验证器
type UsernameValidator struct{}

// NewUsernameValidator 创建用户名验证器
func NewUsernameValidator() *UsernameValidator {
	return &UsernameValidator{}
}

// ValidateUsername 验证用户名
func (v *UsernameValidator) ValidateUsername(username string) error {
	if username == "" {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Username cannot be empty",
			400,
		)
	}

	if len(username) < 3 || len(username) > 50 {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Username must be between 3 and 50 characters",
			400,
		)
	}

	// 用户名只能包含字母、数字、下划线和连字符
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, username)
	if !matched {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Username can only contain letters, numbers, underscores and hyphens",
			400,
		)
	}

	return nil
}

// EmailValidator 邮箱验证器
type EmailValidator struct{}

// NewEmailValidator 创建邮箱验证器
func NewEmailValidator() *EmailValidator {
	return &EmailValidator{}
}

// ValidateEmail 验证邮箱格式
func (v *EmailValidator) ValidateEmail(email string) error {
	if email == "" {
		return nil // 邮箱可以为空
	}

	// 简单的邮箱格式验证
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	matched, _ := regexp.MatchString(emailRegex, email)
	if !matched {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Invalid email format",
			400,
		)
	}

	return nil
}

// PasswordValidator 密码验证器
type PasswordValidator struct{}

// NewPasswordValidator 创建密码验证器
func NewPasswordValidator() *PasswordValidator {
	return &PasswordValidator{}
}

// ValidatePassword 验证密码强度
func (v *PasswordValidator) ValidatePassword(password string) error {
	if password == "" {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Password cannot be empty",
			400,
		)
	}

	if len(password) < 8 {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Password must be at least 8 characters long",
			400,
		)
	}

	if len(password) > 128 {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Password must be less than 128 characters",
			400,
		)
	}

	// 检查密码复杂度（至少包含字母和数字）
	hasLetter := regexp.MustCompile(`[a-zA-Z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)

	if !hasLetter || !hasNumber {
		return errors.NewAppError(
			errors.ErrCodeInvalidRequest,
			"Password must contain at least one letter and one number",
			400,
		)
	}

	return nil
}
