package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 定义错误码类型
type ErrorCode string

// 预定义错误码
const (
	// 通用错误码
	ErrCodeInternal       ErrorCode = "INTERNAL_ERROR"
	ErrCodeInvalidRequest ErrorCode = "INVALID_REQUEST"
	ErrCodeNotFound       ErrorCode = "NOT_FOUND"
	ErrCodeUnauthorized   ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden      ErrorCode = "FORBIDDEN"
	ErrCodeRateLimit      ErrorCode = "RATE_LIMIT_EXCEEDED"

	// 业务错误码
	ErrCodeInvalidIP       ErrorCode = "INVALID_IP"
	ErrCodeIPNotFound      ErrorCode = "IP_NOT_FOUND"
	ErrCodeDatabaseError   ErrorCode = "DATABASE_ERROR"
	ErrCodeCacheError      ErrorCode = "CACHE_ERROR"
	ErrCodeAPIError        ErrorCode = "API_ERROR"
	ErrCodeDatasourceError ErrorCode = "DATASOURCE_ERROR"
	ErrCodeConfigError     ErrorCode = "CONFIG_ERROR"
	ErrCodeAuthError       ErrorCode = "AUTH_ERROR"
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	HTTPStatus int       `json:"-"`
	Cause      error     `json:"-"`
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 支持错误链
func (e *AppError) Unwrap() error {
	return e.Cause
}

// NewAppError 创建新的应用错误
func NewAppError(code ErrorCode, message string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
	}
}

// NewAppErrorWithCause 创建带原因的应用错误
func NewAppErrorWithCause(code ErrorCode, message string, httpStatus int, cause error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
		Cause:      cause,
	}
}

// WithDetails 添加错误详情
func (e *AppError) WithDetails(details string) *AppError {
	e.Details = details
	return e
}

// 预定义常用错误
var (
	ErrInvalidIP = NewAppError(
		ErrCodeInvalidIP,
		"Invalid IP address format",
		http.StatusBadRequest,
	)

	ErrIPNotFound = NewAppError(
		ErrCodeIPNotFound,
		"IP information not found",
		http.StatusNotFound,
	)

	ErrUnauthorized = NewAppError(
		ErrCodeUnauthorized,
		"Authentication required",
		http.StatusUnauthorized,
	)

	ErrForbidden = NewAppError(
		ErrCodeForbidden,
		"Access forbidden",
		http.StatusForbidden,
	)

	ErrRateLimit = NewAppError(
		ErrCodeRateLimit,
		"Rate limit exceeded",
		http.StatusTooManyRequests,
	)

	ErrInternal = NewAppError(
		ErrCodeInternal,
		"Internal server error",
		http.StatusInternalServerError,
	)

	ErrInvalidRequest = NewAppError(
		ErrCodeInvalidRequest,
		"Invalid request format",
		http.StatusBadRequest,
	)
)

// 数据库相关错误
func NewDatabaseError(message string, cause error) *AppError {
	return NewAppErrorWithCause(
		ErrCodeDatabaseError,
		message,
		http.StatusInternalServerError,
		cause,
	)
}

// 缓存相关错误
func NewCacheError(message string, cause error) *AppError {
	return NewAppErrorWithCause(
		ErrCodeCacheError,
		message,
		http.StatusInternalServerError,
		cause,
	)
}

// API相关错误
func NewAPIError(message string, cause error) *AppError {
	return NewAppErrorWithCause(
		ErrCodeAPIError,
		message,
		http.StatusBadGateway,
		cause,
	)
}

// 数据源相关错误
func NewDatasourceError(message string, cause error) *AppError {
	return NewAppErrorWithCause(
		ErrCodeDatasourceError,
		message,
		http.StatusInternalServerError,
		cause,
	)
}

// 配置相关错误
func NewConfigError(message string, cause error) *AppError {
	return NewAppErrorWithCause(
		ErrCodeConfigError,
		message,
		http.StatusInternalServerError,
		cause,
	)
}

// 认证相关错误
func NewAuthError(message string, cause error) *AppError {
	return NewAppErrorWithCause(
		ErrCodeAuthError,
		message,
		http.StatusUnauthorized,
		cause,
	)
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) (*AppError, bool) {
	if appErr, ok := err.(*AppError); ok {
		return appErr, true
	}
	return nil, false
}

// GetHTTPStatus 获取错误对应的HTTP状态码
func GetHTTPStatus(err error) int {
	if appErr, ok := IsAppError(err); ok {
		return appErr.HTTPStatus
	}
	return http.StatusInternalServerError
}

// ErrorResponse API错误响应结构
type ErrorResponse struct {
	Error   ErrorInfo `json:"error"`
	TraceID string    `json:"trace_id,omitempty"`
}

// ErrorInfo 错误信息结构
type ErrorInfo struct {
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
	Details string    `json:"details,omitempty"`
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(err error, traceID string) *ErrorResponse {
	if appErr, ok := IsAppError(err); ok {
		return &ErrorResponse{
			Error: ErrorInfo{
				Code:    appErr.Code,
				Message: appErr.Message,
				Details: appErr.Details,
			},
			TraceID: traceID,
		}
	}

	// 对于非应用错误，返回通用内部错误
	return &ErrorResponse{
		Error: ErrorInfo{
			Code:    ErrCodeInternal,
			Message: "Internal server error",
			Details: err.Error(),
		},
		TraceID: traceID,
	}
}
