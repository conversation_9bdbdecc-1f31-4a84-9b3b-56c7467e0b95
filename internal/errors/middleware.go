package errors

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TraceIDKey 用于在上下文中存储追踪ID的键
const TraceIDKey = "trace_id"

// ErrorHandlerMiddleware 错误处理中间件
func ErrorHandlerMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成追踪ID
		traceID := generateTraceID()
		c.Set(TraceIDKey, traceID)

		// 将追踪ID添加到上下文
		ctx := context.WithValue(c.Request.Context(), TraceIDKey, traceID)
		c.Request = c.Request.WithContext(ctx)

		// 处理请求
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err

			// 记录错误日志
			logError(logger, err, traceID, c)

			// 如果还没有设置响应，则设置错误响应
			if !c.Writer.Written() {
				errorResponse := NewErrorResponse(err, traceID)
				httpStatus := GetHTTPStatus(err)
				c.JSON(httpStatus, errorResponse)
			}
		}
	}
}

// HandleError 统一错误处理函数
func HandleError(c *gin.Context, err error) {
	c.Error(err)
	c.Abort()
}

// HandleAppError 处理应用错误
func HandleAppError(c *gin.Context, appErr *AppError) {
	c.Error(appErr)
	c.Abort()
}

// generateTraceID 生成追踪ID
func generateTraceID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// GetTraceID 从上下文获取追踪ID
func GetTraceID(ctx context.Context) string {
	if traceID, ok := ctx.Value(TraceIDKey).(string); ok {
		return traceID
	}
	return ""
}

// GetTraceIDFromGin 从Gin上下文获取追踪ID
func GetTraceIDFromGin(c *gin.Context) string {
	if traceID, exists := c.Get(TraceIDKey); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}

// logError 记录错误日志
func logError(logger *zap.Logger, err error, traceID string, c *gin.Context) {
	fields := []zap.Field{
		zap.String("trace_id", traceID),
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
		zap.String("client_ip", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")),
		zap.Error(err),
	}

	// 如果是应用错误，添加更多上下文
	if appErr, ok := IsAppError(err); ok {
		fields = append(fields,
			zap.String("error_code", string(appErr.Code)),
			zap.Int("http_status", appErr.HTTPStatus),
		)

		if appErr.Details != "" {
			fields = append(fields, zap.String("error_details", appErr.Details))
		}

		// 根据错误类型选择日志级别
		switch appErr.HTTPStatus {
		case http.StatusInternalServerError:
			logger.Error("Internal server error", fields...)
		case http.StatusBadRequest, http.StatusUnauthorized, http.StatusForbidden:
			logger.Warn("Client error", fields...)
		default:
			logger.Info("Request error", fields...)
		}
	} else {
		// 未知错误，记录为错误级别
		logger.Error("Unhandled error", fields...)
	}
}
