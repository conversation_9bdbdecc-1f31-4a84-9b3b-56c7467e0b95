{"TEST_URLS": ["http://httpbin.org/ip", "https://api.ipify.org?format=json", "https://api.myip.com", "https://wtfismyip.com/json", "https://api.iplocation.net/?cmd=get-ip", "https://ip-api.com/json", "https://api64.ipify.org?format=json", "https://ipwho.is", "https://get.geojs.io/v1/ip.json", "https://ipapi.co/json", "https://api.bigdatacloud.net/data/client-ip", "https://jsonip.com", "https://freeipapi.com/api/json", "https://api.db-ip.com/v2/free/self", "https://ifconfig.me/ip", "http://icanhazip.com", "https://ipinfo.io/ip", "https://checkip.amazonaws.com", "http://ip4only.me/api/", "https://myip.dnsomatic.com", "https://ipecho.net/plain", "https://ident.me", "https://ip2c.org/self", "https://api.ip.sb/ip", "https://api.ip.sb/geoip", "https://myexternalip.com/raw", "https://ifconfig.co/ip", "https://ip.seeip.org", "https://ipget.net", "https://api.ipify.org", "https://ident.me", "https://l2.io/ip", "https://v6.ident.me", "https://ipecho.net/plain"], "IP_INFO_URLS": ["https://ipinfo.io/{ip}/json", "https://ip-api.com/json/{ip}", "https://api.ipgeolocation.io/ipgeo?apiKey={api_key}&ip={ip}", "https://ipwho.is/{ip}", "https://api.iplocation.net/?ip={ip}", "https://ipapi.co/{ip}/json", "https://get.geojs.io/v1/ip/geo/{ip}.json", "https://api.bigdatacloud.net/data/ip-geolocation?ip={ip}&localityLanguage=en", "https://api.ip.sb/geoip/{ip}", "https://ip2c.org/{ip}", "https://api.db-ip.com/v2/free/{ip}", "https://freegeoip.app/json/{ip}", "https://ipapi.com/ip_api.php?ip={ip}", "https://api.techniknews.net/ipgeo/{ip}", "https://api.seeip.org/geoip/{ip}", "https://ipregistry.co/{ip}", "https://api.ipdata.co/{ip}?api-key={api_key}", "https://ipwhois.app/json/{ip}", "https://api.ip2location.io/?ip={ip}", "https://geolocation-db.com/json/{ip}", "https://api.ipinfodb.com/v3/ip-city/?key={api_key}&ip={ip}&format=json"]}