#!/bin/bash

# ipInsight 改进验证测试脚本
# 测试三层查询架构日志增强、API回源异步写入和路由优化

set -e

echo "=== ipInsight 改进验证测试 ==="
echo "测试时间: $(date)"
echo

# 配置
BASE_URL="http://localhost:8080"
TEST_IP="*******"
INVALID_IP="999.999.999.999"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查 ipInsight 服务状态..."
    if curl -s "${BASE_URL}/health" > /dev/null; then
        log_success "服务正在运行"
        return 0
    else
        log_error "服务未运行，请先启动 ipInsight 服务"
        echo "启动命令: go run cmd/ipInsight/main.go"
        exit 1
    fi
}

# 测试根路径欢迎页面
test_welcome_page() {
    log_info "测试根路径欢迎页面..."
    
    response=$(curl -s "${BASE_URL}/")
    if echo "$response" | grep -q "ipInsight"; then
        log_success "根路径欢迎页面正常"
        echo "响应包含服务信息、健康状态、API端点和使用示例"
    else
        log_error "根路径欢迎页面异常"
        echo "响应: $response"
    fi
    echo
}

# 测试API文档页面
test_docs_page() {
    log_info "测试API文档页面..."
    
    response=$(curl -s "${BASE_URL}/docs")
    if echo "$response" | grep -q "API Documentation"; then
        log_success "API文档页面正常"
        echo "响应包含完整的API文档和使用示例"
    else
        log_error "API文档页面异常"
        echo "响应: $response"
    fi
    echo
}

# 测试404错误处理
test_404_handling() {
    log_info "测试404错误处理..."
    
    # 测试不存在的路径
    response=$(curl -s "${BASE_URL}/nonexistent")
    if echo "$response" | grep -q "Not Found"; then
        log_success "404错误处理正常"
        echo "响应包含友好的错误信息和建议"
    else
        log_error "404错误处理异常"
        echo "响应: $response"
    fi
    
    # 测试API相关的错误路径
    response=$(curl -s "${BASE_URL}/api/wrong")
    if echo "$response" | grep -q "suggestions"; then
        log_success "API路径404处理包含智能建议"
    else
        log_warning "API路径404处理可能缺少建议"
    fi
    echo
}

# 测试三层查询架构和日志
test_query_layers() {
    log_info "测试三层查询架构..."
    
    # 第一次查询 - 应该触发完整的三层查询
    log_info "执行第一次查询 (触发完整三层查询)..."
    response=$(curl -s "${BASE_URL}/api/v1/ip/${TEST_IP}")
    
    if echo "$response" | grep -q "ip"; then
        log_success "IP查询成功"
        echo "查询结果: $(echo "$response" | jq -r '.ip // .query // "N/A"' 2>/dev/null || echo "解析失败")"
        
        # 检查响应中的source字段
        source=$(echo "$response" | jq -r '.source // "unknown"' 2>/dev/null || echo "unknown")
        log_info "数据来源: $source"
        
        case $source in
            "cache")
                log_info "数据来自缓存层 (Layer 1)"
                ;;
            "database")
                log_info "数据来自数据库层 (Layer 2)"
                ;;
            "api")
                log_info "数据来自API回源层 (Layer 3)"
                ;;
            *)
                log_warning "未知数据来源: $source"
                ;;
        esac
    else
        log_error "IP查询失败"
        echo "响应: $response"
    fi
    
    # 第二次查询 - 应该从缓存获取
    log_info "执行第二次查询 (应该从缓存获取)..."
    sleep 1
    response2=$(curl -s "${BASE_URL}/api/v1/ip/${TEST_IP}")
    
    if echo "$response2" | grep -q "ip"; then
        source2=$(echo "$response2" | jq -r '.source // "unknown"' 2>/dev/null || echo "unknown")
        if [ "$source2" = "cache" ]; then
            log_success "缓存层工作正常"
        else
            log_warning "第二次查询未从缓存获取，来源: $source2"
        fi
    fi
    echo
}

# 测试批量查询
test_batch_query() {
    log_info "测试批量查询..."
    
    batch_data='{"ips":["*******","*******","***************"]}'
    response=$(curl -s -X POST "${BASE_URL}/api/v1/ip/batch" \
        -H "Content-Type: application/json" \
        -d "$batch_data")
    
    if echo "$response" | grep -q "results"; then
        log_success "批量查询成功"
        count=$(echo "$response" | jq '.results | length' 2>/dev/null || echo "0")
        log_info "查询结果数量: $count"
    else
        log_error "批量查询失败"
        echo "响应: $response"
    fi
    echo
}

# 测试健康检查
test_health_check() {
    log_info "测试健康检查..."
    
    response=$(curl -s "${BASE_URL}/health")
    if echo "$response" | grep -q "ok"; then
        log_success "健康检查正常"
    else
        log_error "健康检查异常"
        echo "响应: $response"
    fi
    echo
}

# 测试错误处理
test_error_handling() {
    log_info "测试错误处理..."
    
    # 测试无效IP
    response=$(curl -s "${BASE_URL}/api/v1/ip/${INVALID_IP}")
    if echo "$response" | grep -q "error\|invalid"; then
        log_success "无效IP错误处理正常"
    else
        log_warning "无效IP错误处理可能需要改进"
        echo "响应: $response"
    fi
    echo
}

# 主测试流程
main() {
    echo "开始验证 ipInsight 改进..."
    echo
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 命令未找到，JSON解析功能受限"
    fi
    
    # 执行测试
    check_service
    test_welcome_page
    test_docs_page
    test_404_handling
    test_health_check
    test_query_layers
    test_batch_query
    test_error_handling
    
    echo "=== 测试完成 ==="
    log_success "所有测试已执行完成"
    echo
    echo "注意事项："
    echo "1. 查看服务日志以验证详细的三层查询日志记录"
    echo "2. 检查异步写入操作的日志输出"
    echo "3. 验证缓存和数据库的数据一致性"
    echo "4. 监控API回源的重试机制和失败回滚"
    echo
    echo "建议查看日志命令:"
    echo "tail -f /path/to/ipinsight.log | grep -E 'query_id|Layer|async'"
}

# 执行主函数
main "$@"
curl -X POST -d "{}" http://localhost:8081/api/v1/admin/ip-completion/start?api_key=admin-api-key-123456