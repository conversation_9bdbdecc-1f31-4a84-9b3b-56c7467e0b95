#!/bin/bash

# 测试IP Insight API功能的脚本

BASE_URL="http://localhost:8082"

echo "=== IP Insight API 测试 ==="
echo

# 测试健康检查
echo "1. 测试健康检查..."
curl -s "$BASE_URL/health" | jq .
echo
echo

# 测试IP查询 (这会触发回源查询)
echo "2. 测试IP查询 (回源查询)..."
echo "查询 *******..."
curl -s "$BASE_URL/ip/*******" | jq .
echo
echo

# 测试手动触发数据源更新
echo "3. 测试手动触发数据源更新..."
echo "触发 ipapi 数据源更新..."
curl -s -X POST "$BASE_URL/api/v1/admin/fetch" \
  -H "Content-Type: application/json" \
  -d '{"sources": ["ipapi"]}' | jq .
echo
echo

# 测试批量IP查询
echo "4. 测试批量IP查询..."
echo "查询多个IP..."
curl -s -X POST "$BASE_URL/ip/batch" \
  -H "Content-Type: application/json" \
  -d '{"ips": ["*******", "*******", "***************"]}' | jq .
echo
echo

echo "=== 测试完成 ==="
