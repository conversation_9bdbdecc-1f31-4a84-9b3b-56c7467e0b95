import React, { useState, useEffect } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import { useToastIntegration } from '../../hooks/useToastIntegration'
import { useMediaQuery } from '../../hooks/useMediaQuery'
import { cn } from '@/lib/utils'

const drawerWidth = 280
const collapsedDrawerWidth = 72

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const isMobile = useMediaQuery('(max-width: 767px)')
  const [mobileOpen, setMobileOpen] = useState(false)
  const [desktopOpen, setDesktopOpen] = useState(true)
  const [collapsed, setCollapsed] = useState(false)

  // 初始化Toast集成
  useToastIntegration()

  // 从localStorage读取折叠状态
  useEffect(() => {
    const savedCollapsed = localStorage.getItem('sidebar-collapsed')
    if (savedCollapsed !== null) {
      setCollapsed(JSON.parse(savedCollapsed))
    }
  }, [])

  // 保存折叠状态到localStorage
  const handleToggleCollapsed = () => {
    const newCollapsed = !collapsed
    setCollapsed(newCollapsed)
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed))
  }

  const handleDrawerToggle = () => {
    if (isMobile) {
      setMobileOpen(!mobileOpen)
    } else {
      if (desktopOpen) {
        handleToggleCollapsed()
      } else {
        setDesktopOpen(true)
      }
    }
  }

  const handleMobileDrawerClose = () => {
    setMobileOpen(false)
  }

  const drawer = (
    <Sidebar
      onMobileClose={handleMobileDrawerClose}
      isMobile={isMobile}
      collapsed={!isMobile && collapsed}
    />
  )

  // 计算当前drawer宽度
  const currentDrawerWidth = isMobile
    ? 0
    : desktopOpen
      ? collapsed
        ? collapsedDrawerWidth
        : drawerWidth
      : 0

  return (
    <div className="flex min-h-screen bg-background">
      {/* Header */}
      <Header
        drawerWidth={currentDrawerWidth}
        onMenuClick={handleDrawerToggle}
        collapsed={collapsed}
        onToggleCollapse={handleToggleCollapsed}
      />

      {/* Sidebar */}
      <nav
        className={cn(
          "transition-all duration-300 ease-in-out",
          isMobile ? "w-0" : desktopOpen ? (collapsed ? "w-18" : "w-70") : "w-0"
        )}
        style={{
          width: isMobile ? 0 : desktopOpen ? (collapsed ? collapsedDrawerWidth : drawerWidth) : 0
        }}
      >
        {/* Mobile drawer */}
        {isMobile && (
          <div
            className={cn(
              "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm transition-opacity",
              mobileOpen ? "opacity-100" : "opacity-0 pointer-events-none"
            )}
            onClick={handleMobileDrawerClose}
          >
            <div
              className={cn(
                "fixed left-0 top-0 h-full bg-card border-r border-border transition-transform duration-300 ease-in-out",
                mobileOpen ? "translate-x-0" : "-translate-x-full"
              )}
              style={{ width: drawerWidth }}
              onClick={(e) => e.stopPropagation()}
            >
              {drawer}
            </div>
          </div>
        )}

        {/* Desktop drawer */}
        {!isMobile && desktopOpen && (
          <div
            className="fixed left-0 top-0 h-full bg-card border-r border-border transition-all duration-300 ease-in-out overflow-hidden"
            style={{ width: collapsed ? collapsedDrawerWidth : drawerWidth }}
          >
            {drawer}
          </div>
        )}
      </nav>

      {/* Main content */}
      <main
        className={cn(
          "flex-1 p-6 mt-16 bg-background min-h-[calc(100vh-4rem)] transition-all duration-300 ease-in-out",
          !isMobile && desktopOpen ? "ml-0" : collapsed ? `-ml-18` : `-ml-70`
        )}
        style={{
          marginLeft: isMobile
            ? 0
            : desktopOpen
              ? 0
              : `-${collapsed ? collapsedDrawerWidth : drawerWidth}px`,
        }}
      >
        {children}
      </main>
    </div>
  )
}

export default Layout