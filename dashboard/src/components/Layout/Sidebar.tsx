import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip'
import { Dashboard, Computer, Analytics, Storage, Api, Insights } from '@/components/ui/icons'
import { cn } from '@/lib/utils'

interface SidebarProps {
  onMobileClose?: () => void
  isMobile?: boolean
  collapsed?: boolean
}

interface MenuItem {
  text: string
  icon: React.ReactElement
  path: string
  badge?: string
}

const menuItems: MenuItem[] = [
  {
    text: '总览',
    icon: <Dashboard className="h-5 w-5" />,
    path: '/dashboard',
  },
  {
    text: '系统状态',
    icon: <Computer className="h-5 w-5" />,
    path: '/system',
  },
  {
    text: 'API统计',
    icon: <Analytics className="h-5 w-5" />,
    path: '/api-stats',
  },
  {
    text: '数据源管理',
    icon: <Storage className="h-5 w-5" />,
    path: '/data-sources',
  },
  {
    text: 'API测试',
    icon: <Api className="h-5 w-5" />,
    path: '/api-test',
  },
]

const Sidebar: React.FC<SidebarProps> = ({ onMobileClose, isMobile, collapsed = false }) => {
  const location = useLocation()
  const navigate = useNavigate()

  const handleNavigation = (path: string) => {
    navigate(path)
    if (isMobile && onMobileClose) {
      onMobileClose()
    }
  }

  const isActive = (path: string) => {
    return location.pathname === path
  }

  return (
    <TooltipProvider>
      <div className="flex flex-col h-full bg-card border-r border-border shadow-sm">
        {/* Logo区域 */}
        <div
          className={cn(
            'flex items-center gap-3 p-4 border-b border-border',
            collapsed && 'justify-center'
          )}
        >
          <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
            <Insights className="h-5 w-5 text-primary-foreground" />
          </div>
          {!collapsed && (
            <div>
              <h1 className="text-lg font-semibold">ipInsight</h1>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {menuItems.map((item) => {
              const active = isActive(item.path)

              if (collapsed) {
                return (
                  <Tooltip key={item.path}>
                    <TooltipTrigger asChild>
                      <Button
                        variant={active ? 'default' : 'ghost'}
                        size="icon"
                        className={cn(
                          'w-full h-12 transition-all duration-200 hover:scale-105',
                          active && 'bg-primary text-primary-foreground shadow-md'
                        )}
                        onClick={() => handleNavigation(item.path)}
                      >
                        {item.icon}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <div className="flex items-center gap-2">
                        {item.text}
                        {item.badge && (
                          <Badge variant="secondary" className="text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                )
              }

              return (
                <Button
                  key={item.path}
                  variant={active ? 'default' : 'ghost'}
                  className={cn(
                    'w-full justify-start gap-3 h-12 transition-all duration-200 hover:scale-[1.02]',
                    active && 'bg-primary text-primary-foreground shadow-md'
                  )}
                  onClick={() => handleNavigation(item.path)}
                >
                  {item.icon}
                  <span className="flex-1 text-left">{item.text}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </Button>
              )
            })}
          </div>
        </nav>

        {/* 底部信息 */}
        {!collapsed && (
          <div className="p-4 border-t border-border">
            <div className="text-xs text-muted-foreground space-y-1">
              <div>版本: v1.0.0</div>
              <div>© 2024 ipInsight</div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}

export default Sidebar