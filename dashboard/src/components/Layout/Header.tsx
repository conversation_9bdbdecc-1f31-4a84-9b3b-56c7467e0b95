import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Menu as MenuIcon,
  User as AccountCircle,
  Logout,
  RefreshCw as Refresh,
  Bell as Notifications,
  Person,
  Settings,
  ChevronLeft,
  ChevronRight,
} from '@/components/ui/icons'
// import UserProfile from '../Auth/UserProfile'
import { useAuth } from '../../contexts/AuthContext'
import { useMonitoringData } from '../../hooks/useApi'
import { apiService } from '../../services/api'
import { formatRelativeTime } from '../../utils/format'
import { cn } from '@/lib/utils'

interface HeaderProps {
  drawerWidth: number
  onMenuClick: () => void
  collapsed?: boolean
  onToggleCollapse?: () => void
}

const Header: React.FC<HeaderProps> = ({
  drawerWidth,
  onMenuClick,
  collapsed = false,
  onToggleCollapse,
}) => {
  const { logout } = useAuth()
  const [showProfile, setShowProfile] = useState(false)

  // 获取系统状态用于显示健康状态
  const { data: systemStatus, refetch: refetchStatus } = useMonitoringData(
    () => apiService.getSystemStatus(),
    30000 // 30秒刷新一次
  )

  const handleLogout = () => {
    logout()
  }

  const handleRefresh = () => {
    refetchStatus()
  }

  const handleShowProfile = () => {
    setShowProfile(true)
  }

  return (
    <TooltipProvider>
      <header
        className={cn(
          "fixed top-0 z-40 w-full border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60",
          "transition-all duration-300 ease-in-out"
        )}
        style={{
          width: `calc(100% - ${drawerWidth}px)`,
          marginLeft: drawerWidth,
        }}
      >
        <div className="flex h-16 items-center px-4">
          {/* 移动端菜单按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="mr-2 md:hidden"
            onClick={onMenuClick}
            aria-label="打开菜单"
          >
            <MenuIcon className="h-5 w-5" />
          </Button>

          {/* 桌面端折叠按钮 */}
          {drawerWidth > 0 && onToggleCollapse && (
            <Button
              variant="ghost"
              size="icon"
              className="mr-2 hidden md:flex"
              onClick={onToggleCollapse}
              aria-label="切换侧边栏"
            >
              {collapsed ? (
                <ChevronRight className="h-5 w-5" />
              ) : (
                <ChevronLeft className="h-5 w-5" />
              )}
            </Button>
          )}

          <h1 className="flex-1 text-lg font-semibold">
            ipInsight 管理看板
          </h1>

          {/* 系统状态指示器 */}
          {systemStatus && (
            <Badge
              variant={systemStatus.status === 'ok' ? 'success' : 'destructive'}
              className="mr-2"
            >
              {systemStatus.status === 'ok' ? '系统正常' : '系统异常'}
            </Badge>
          )}

          {/* 刷新按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={handleRefresh}>
                <Refresh className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>刷新数据</TooltipContent>
          </Tooltip>

          {/* 通知按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon">
                <Notifications className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>通知</TooltipContent>
          </Tooltip>

          {/* 用户菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="ml-2">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>
                    <AccountCircle className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={handleShowProfile}>
                <Person className="mr-2 h-4 w-4" />
                用户资料
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <Logout className="mr-2 h-4 w-4" />
                退出登录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 用户资料对话框 */}
        <Dialog open={showProfile} onOpenChange={setShowProfile}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>用户资料</DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p>用户资料功能正在迁移中...</p>
            </div>
            <DialogFooter>
              <Button onClick={() => setShowProfile(false)}>关闭</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </header>
    </TooltipProvider>
  )
}

export default Header