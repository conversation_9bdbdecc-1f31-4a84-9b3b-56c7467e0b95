import React from 'react'
import { Moon, Sun } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { useTheme } from '@/contexts/ThemeContext'
import { cn } from '@/lib/utils'

interface ThemeToggleProps {
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'icon'
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  size = 'icon',
  variant = 'ghost',
}) => {
  const { theme, toggleTheme } = useTheme()

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant}
          size={size}
          onClick={toggleTheme}
          className={cn(
            'relative transition-all duration-200 hover:scale-105',
            className
          )}
          aria-label={`切换到${theme === 'light' ? '暗色' : '明亮'}主题`}
        >
          <Sun
            className={cn(
              'h-4 w-4 transition-all duration-300',
              theme === 'light'
                ? 'rotate-0 scale-100 opacity-100'
                : 'rotate-90 scale-0 opacity-0'
            )}
          />
          <Moon
            className={cn(
              'absolute h-4 w-4 transition-all duration-300',
              theme === 'dark'
                ? 'rotate-0 scale-100 opacity-100'
                : '-rotate-90 scale-0 opacity-0'
            )}
          />
          <span className="sr-only">切换主题</span>
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>切换到{theme === 'light' ? '暗色' : '明亮'}主题</p>
      </TooltipContent>
    </Tooltip>
  )
}

export default ThemeToggle