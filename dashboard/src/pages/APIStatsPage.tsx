import React, { useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/spinner'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Activity,
  BarChart3,
  Clock,
  TrendingUp,
  Users,
  Api,
  CheckCircle,
  AlertTriangle,
  Timer,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../contexts/ToastContext'
import { formatNumber, formatResponseTime } from '@/lib/utils'
import { cn } from '@/lib/utils'
import RefreshButton from '@/components/RefreshButton'

const APIStatsPage: React.FC = () => {
  const { showError } = useToast()

  // API调用函数 - 使用真实后端接口
  const getAPIStats = useCallback(() => apiService.getQueryStats(), [])
  const getMetrics = useCallback(() => apiService.getMetrics(), [])

  // 获取API统计数据
  const {
    data: apiStats,
    loading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useMonitoringData(getAPIStats, 30000)
  console.log(`apiStats:`, apiStats)
  // 获取监控指标数据（包含API端点统计）
  const {
    data: metrics,
    loading: metricsLoading,
    error: metricsError,
    refetch: refetchMetrics,
  } = useMonitoringData(getMetrics, 30000)
  console.log(`apiStats:`, apiStats)
  // 处理错误
  React.useEffect(() => {
    if (statsError) showError('获取API统计失败: ' + statsError)
    if (metricsError) showError('获取监控指标失败: ' + metricsError)
  }, [statsError, metricsError, showError])

  // 刷新所有数据
  const handleRefreshAll = () => {
    refetchStats()
    refetchMetrics()
    // showSuccess('数据已刷新')
  }

  // 从后端API获取的真实数据
  const endpointStats = metrics?.api?.endpoint_stats || {}
  const endpoints = Object.values(endpointStats).map((stat: any) => ({
    path: stat.path,
    method: stat.method,
    requests: stat.request_count,
    avg_time: Math.round(stat.avg_duration / 1000000), // 转换纳秒到毫秒
    success_rate:
      stat.request_count > 0
        ? (((stat.request_count - stat.error_count) / stat.request_count) * 100).toFixed(1)
        : 0,
    last_access: stat.last_access,
  }))

  // 统计卡片数据 - 基于真实API数据
  const totalRequests = metrics?.service?.total_requests || 0
  const successRequests = metrics?.service?.success_requests || 0
  const errorRequests = metrics?.service?.error_requests || 0
  const avgResponseTime = metrics?.service?.avg_response_time || 0
  const successRate = totalRequests > 0 ? ((successRequests / totalRequests) * 100).toFixed(1) : 0
  const errorRate = totalRequests > 0 ? ((errorRequests / totalRequests) * 100).toFixed(1) : 0

  const statsCards = [
    {
      title: '总请求数',
      value: totalRequests,
      icon: <Activity className="h-6 w-6" />,
      trend: 'na',
      trendUp: true,
      loading: statsLoading || metricsLoading,
    },
    {
      title: '缓存命中数',
      value: apiStats?.cache_hits || 0,
      icon: <TrendingUp className="h-6 w-6" />,
      trend: 'na',
      trendUp: true,
      loading: statsLoading,
    },
    {
      title: '成功率',
      value: `${successRate}%`,
      icon: <CheckCircle className="h-6 w-6" />,
      trend: 'na',
      trendUp: true,
      loading: statsLoading || metricsLoading,
      valueColor: 'text-green-500',
    },
    {
      title: '平均响应时间',
      value: `${Math.round(avgResponseTime)}ms`,
      icon: <Timer className="h-6 w-6" />,
      trend: 'na',
      trendUp: true,
      loading: statsLoading || metricsLoading,
    },
    {
      title: '错误率',
      value: `${errorRate}%`,
      icon: <AlertTriangle className="h-6 w-6" />,
      trend: 'na',
      trendUp: true,
      loading: statsLoading || metricsLoading,
      valueColor: parseFloat(errorRate.toString()) > 1 ? 'text-red-500' : 'text-green-500',
    },
    {
      title: '数据库查询',
      value: apiStats?.database_hits || 0,
      icon: <Users className="h-6 w-6" />,
      trend: 'na',
      trendUp: true,
      loading: statsLoading,
    },
  ]

  // 获取状态颜色
  const getStatusColor = (rate: number) => {
    if (rate >= 99) return 'success'
    if (rate >= 95) return 'warning'
    return 'destructive'
  }

  // 获取响应时间颜色
  const getResponseTimeColor = (time: number) => {
    if (time <= 100) return 'text-green-500'
    if (time <= 500) return 'text-yellow-500'
    return 'text-red-500'
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">API统计</h1>
          <p className="text-muted-foreground">API接口调用统计和性能监控</p>
        </div>
        <RefreshButton handleRefreshAll={handleRefreshAll} />
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statsCards.map((card, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardContent className="p-6">
              {card.loading ? (
                <div className="flex items-center justify-center h-20">
                  <Spinner size="lg" />
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <div className={cn('text-primary', card.valueColor)}>{card.icon}</div>
                    <Badge variant={card.trendUp ? 'success' : 'destructive'}>{card.trend}</Badge>
                  </div>
                  <div className="mt-4">
                    <div className={cn('text-2xl font-bold', card.valueColor)}>
                      {typeof card.value === 'number' ? formatNumber(card.value) : card.value}
                    </div>
                    <div className="text-sm text-muted-foreground">{card.title}</div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* API端点统计表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Api className="h-5 w-5" />
            API端点统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          {metricsLoading ? (
            <div className="flex items-center justify-center h-64">
              <Spinner size="lg" />
            </div>
          ) : metricsError ? (
            <Alert variant="destructive">
              <AlertDescription>加载端点统计失败: {metricsError}</AlertDescription>
            </Alert>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>端点</TableHead>
                    <TableHead>方法</TableHead>
                    <TableHead className="text-right">请求数</TableHead>
                    <TableHead className="text-right">平均响应时间</TableHead>
                    <TableHead className="text-right">成功率</TableHead>
                    <TableHead className="text-center">状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {endpoints.map((endpoint, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-mono text-sm">{endpoint.path}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-mono">
                          {endpoint.method}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-semibold">
                        {formatNumber(endpoint.requests)}
                      </TableCell>
                      <TableCell
                        className={cn(
                          'text-right font-semibold',
                          getResponseTimeColor(endpoint.avg_time)
                        )}
                      >
                        {formatResponseTime(endpoint.avg_time)}
                      </TableCell>
                      <TableCell className="text-right font-semibold">
                        {typeof endpoint.success_rate === 'number'
                          ? endpoint.success_rate.toFixed(1)
                          : endpoint.success_rate}
                        %
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge
                          variant={getStatusColor(parseFloat(endpoint.success_rate.toString()))}
                        >
                          {parseFloat(endpoint.success_rate.toString()) >= 99
                            ? '优秀'
                            : parseFloat(endpoint.success_rate.toString()) >= 95
                              ? '良好'
                              : '需要关注'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 性能趋势图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              请求量趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-muted/20 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">请求量趋势图表</p>
                <p className="text-xs text-muted-foreground">24小时数据</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              响应时间趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-muted/20 rounded-lg">
              <div className="text-center">
                <Clock className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">响应时间趋势图表</p>
                <p className="text-xs text-muted-foreground">24小时数据</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default APIStatsPage