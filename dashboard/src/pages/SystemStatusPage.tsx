import React, { useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Server,
  Cpu,
  HardDrive as Memory,
  HardDrive,
  Activity,
  Wifi,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Gauge,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../contexts/ToastContext'
import { formatBytes } from '@/lib/utils'
import { cn } from '@/lib/utils'

const SystemStatusPage: React.FC = () => {
  const { showError, showSuccess } = useToast()

  // API调用函数
  const getSystemStatus = useCallback(() => apiService.getSystemStatus(), [])
  const getSystemMetrics = useCallback(() => apiService.getMetrics(), [])

  // 获取系统状态数据
  const {
    data: systemStatus,
    loading: statusLoading,
    error: statusError,
    refetch: refetchStatus,
  } = useMonitoringData(getSystemStatus, 5000)
  console.log(`systemStatus:`, systemStatus)
  const {
    data: systemMetrics,
    loading: metricsLoading,
    error: metricsError,
    refetch: refetchMetrics,
  } = useMonitoringData(getSystemMetrics, 5000)
  console.log(`systemMetrics:`, systemMetrics)
  // 处理错误
  React.useEffect(() => {
    if (statusError) showError('获取系统状态失败: ' + statusError)
    if (metricsError) showError('获取系统指标失败: ' + metricsError)
  }, [statusError, metricsError, showError])

  // 刷新所有数据
  const handleRefreshAll = () => {
    refetchStatus()
    refetchMetrics()
    // showSuccess('数据已刷新')
  }

  // 使用真实的系统指标数据
  const metrics = systemMetrics?.system

  // 获取状态颜色
  const getStatusColor = (value: number, thresholds: { warning: number; danger: number }) => {
    if (value >= thresholds.danger) return 'destructive'
    if (value >= thresholds.warning) return 'warning'
    return 'success'
  }

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 60 * 60))
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
    const minutes = Math.floor((seconds % (60 * 60)) / 60)
    return `${days}天 ${hours}小时 ${minutes}分钟`
  }

  // 系统服务状态
  const services = [
    { name: 'Web服务器', status: 'running', port: 8082 },
    { name: 'PostgreSQL', status: 'running', port: 5432 },
    { name: 'Redis', status: 'running', port: 6379 },
    { name: '数据源更新', status: 'running', port: null },
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">系统状态</h1>
          <p className="text-muted-foreground">实时监控系统运行状态和性能指标</p>
        </div>
        <Button onClick={handleRefreshAll} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* 系统概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* CPU使用率 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Cpu className="h-5 w-5 text-primary" />
                <span className="font-medium">CPU使用率</span>
              </div>
              <Badge variant={getStatusColor(metrics?.cpu_usage || 0, { warning: 70, danger: 90 })}>
                {(metrics?.cpu_usage || 0).toFixed(1)}%
              </Badge>
            </div>
            <Progress value={metrics?.cpu_usage || 0} className="h-2" />
            <div className="mt-2 text-xs text-muted-foreground">
              Goroutines: {metrics?.goroutine_count || 0}
            </div>
          </CardContent>
        </Card>

        {/* 内存使用率 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Memory className="h-5 w-5 text-primary" />
                <span className="font-medium">内存使用率</span>
              </div>
              <Badge
                variant={getStatusColor(metrics?.memory_usage || 0, { warning: 80, danger: 95 })}
              >
                {(metrics?.memory_usage || 0).toFixed(1)}%
              </Badge>
            </div>
            <Progress value={metrics?.memory_usage || 0} className="h-2" />
            <div className="mt-2 text-xs text-muted-foreground">
              堆内存: {formatBytes(metrics?.heap_in_use || 0)} /{' '}
              {formatBytes(metrics?.heap_size || 0)}
            </div>
          </CardContent>
        </Card>

        {/* 磁盘使用率 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <HardDrive className="h-5 w-5 text-primary" />
                <span className="font-medium">磁盘使用率</span>
              </div>
              <Badge
                variant={getStatusColor(metrics?.disk_usage || 0, { warning: 80, danger: 95 })}
              >
                {(metrics?.disk_usage || 0).toFixed(1)}%
              </Badge>
            </div>
            <Progress value={metrics?.disk_usage || 0} className="h-2" />
            <div className="mt-2 text-xs text-muted-foreground">磁盘使用率暂不可用</div>
          </CardContent>
        </Card>

        {/* 系统运行时间 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                <span className="font-medium">运行时间</span>
              </div>
              <Badge variant="success">正常</Badge>
            </div>
            <div className="text-lg font-semibold">{formatUptime(metrics?.uptime / 1e9 || 0)}</div>
            <div className="mt-2 text-xs text-muted-foreground">自上次重启以来</div>
          </CardContent>
        </Card>
      </div>

      {/* 网络和服务状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 服务状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              服务状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {services.map((service, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      'w-2 h-2 rounded-full',
                      service.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                    )}
                  />
                  <span>{service.name}</span>
                  {service.port && (
                    <span className="text-xs text-muted-foreground">:{service.port}</span>
                  )}
                </div>
                <Badge variant={service.status === 'running' ? 'success' : 'destructive'}>
                  {service.status === 'running' ? '运行中' : '已停止'}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>
        {/* 性能指标 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gauge className="h-5 w-5" />
              性能指标
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">API响应时间</span>
              <span className="font-semibold">
                {Math.round(systemMetrics?.service?.avg_response_time || 0)}ms
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">数据库查询时间</span>
              <span className="font-semibold">
                {Math.round(systemMetrics?.database?.avg_query_time || 0)}ms
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">缓存命中率</span>
              <span className="font-semibold">
                {(systemMetrics?.cache?.hit_rate || 0).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">并发连接数</span>
              <span className="font-semibold">
                {systemMetrics?.service?.active_connections || 0}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SystemStatusPage