import React, { useCallback, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/spinner'
import { Progress } from '@/components/ui/progress'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Storage,
  RefreshCw,
  Download,
  CheckCircle,
  AlertTriangle,
  Database,
  Globe,
  Pause,
  Settings,
  Info,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../contexts/ToastContext'
import { formatBytes, formatRelativeTime } from '@/lib/utils'
import RefreshButton from '@/components/RefreshButton'

const DataSourcesPage: React.FC = () => {
  const { showError, showSuccess, showInfo } = useToast()
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false)
  const [selectedSource, setSelectedSource] = useState<string | null>(null)
  const [updating, setUpdating] = useState(false)

  // API调用函数
  const getDataSources = useCallback(() => apiService.getDataSources(), [])

  // 获取数据源状态
  const {
    data: dataSources,
    loading: sourcesLoading,
    error: sourcesError,
    refetch: refetchSources,
  } = useMonitoringData(getDataSources, 30000)

  // 处理错误
  React.useEffect(() => {
    if (sourcesError) showError('获取数据源状态失败: ' + sourcesError)
  }, [sourcesError, showError])

  // 使用真实的数据源数据
  const sources = dataSources || []

  // 获取状态颜色和文本
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'active':
        return { variant: 'success' as const, text: '正常', icon: CheckCircle }
      case 'updating':
        return { variant: 'warning' as const, text: '更新中', icon: RefreshCw }
      case 'error':
        return { variant: 'destructive' as const, text: '错误', icon: AlertTriangle }
      case 'disabled':
        return { variant: 'secondary' as const, text: '已禁用', icon: Pause }
      default:
        return { variant: 'secondary' as const, text: '未知', icon: Info }
    }
  }

  // 手动更新数据源
  const handleUpdateSource = async (sourceId: string) => {
    setUpdating(true)
    try {
      // 调用真实的后端API
      await apiService.fetchDataSource([sourceId])
      showSuccess(`数据源 ${sourceId} 更新成功`)
      refetchSources()
    } catch (error: any) {
      showError(`更新数据源失败: ${error.message || error}`)
    } finally {
      setUpdating(false)
      setUpdateDialogOpen(false)
      setSelectedSource(null)
    }
  }

  // 批量更新所有数据源
  const handleUpdateAll = async () => {
    setUpdating(true)
    try {
      showInfo('开始批量更新所有数据源...', { duration: 3000 })

      // 调用后端API更新所有数据源（不指定特定数据源）
      await apiService.fetchDataSource(dataSources.map(s => s.name))

      showSuccess('所有数据源更新完成', {
        title: '批量更新成功',
        duration: 5000
      })

      // 刷新数据源状态
      refetchSources()
    } catch (error: unknown) {
      const errorMessage = error instanceof Error
        ? error.message
        : (error as { response?: { data?: { message?: string } } })?.response?.data?.message || '未知错误'
      showError(`批量更新失败: ${errorMessage}`, {
        title: '更新失败',
        duration: 8000
      })
    } finally {
      setUpdating(false)
    }
  }

  // 统计信息
  const stats = {
    total: sources.length,
    active: sources.filter((s) => s.status === 'active').length,
    updating: sources.filter((s) => s.status === 'updating').length,
    error: sources.filter((s) => s.status === 'error').length,
    totalSize: sources.reduce((sum, s) => sum + (s.size || 0), 0),
    totalRecords: sources.reduce((sum, s) => sum + (s.records || 0), 0),
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">数据源管理</h1>
          <p className="text-muted-foreground">管理和监控IP地理位置数据源</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton handleRefreshAll={refetchSources} />
          {/* <Button onClick={handleUpdateAll} disabled={updating} size="sm">
            {updating ? (
              <Spinner size="sm" className="mr-2" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            批量更新
          </Button> */}
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">{stats.total}</div>
                <div className="text-sm text-muted-foreground">总数据源</div>
              </div>
              <Storage className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-500">{stats.active}</div>
                <div className="text-sm text-muted-foreground">正常运行</div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">{formatBytes(stats.totalSize)}</div>
                <div className="text-sm text-muted-foreground">总数据大小</div>
              </div>
              <Database className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {(stats.totalRecords / 1000000).toFixed(1)}M
                </div>
                <div className="text-sm text-muted-foreground">总记录数</div>
              </div>
              <Globe className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 数据源列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Storage className="h-5 w-5" />
            数据源状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sourcesLoading ? (
            <div className="flex items-center justify-center h-64">
              <Spinner size="lg" />
            </div>
          ) : sourcesError ? (
            <Alert variant="destructive">
              <AlertDescription>加载数据源失败: {sourcesError}</AlertDescription>
            </Alert>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>数据源</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>大小</TableHead>
                    <TableHead>记录数</TableHead>
                    <TableHead>最后更新</TableHead>
                    <TableHead>下次更新</TableHead>
                    <TableHead className="text-center">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sources.map((source) => {
                    const statusInfo = getStatusInfo(source.status)
                    const StatusIcon = statusInfo.icon

                    return (
                      <TableRow key={source.name}>
                        <TableCell>
                          <div>
                            <div className="font-semibold">{source.name}</div>
                            <div className="text-xs text-muted-foreground">
                              数据源: {source.name}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">数据库</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <StatusIcon className="h-4 w-4" />
                            <Badge variant={statusInfo.variant}>{statusInfo.text}</Badge>
                          </div>
                        </TableCell>
                        <TableCell>{formatBytes((source as { size?: number }).size || 0)}</TableCell>
                        <TableCell>{(source.records_count || 0).toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="text-sm">{formatRelativeTime(new Date(source.last_update * 1000).toISOString())}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatRelativeTime(new Date(source.next_update * 1000).toISOString())}</div>
                        </TableCell>
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedSource(source.name)
                                setUpdateDialogOpen(true)
                              }}
                              disabled={source.status === 'updating'}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Settings className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 更新进度 */}
      {stats.updating > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              更新进度
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sources
                .filter((s) => s.status === 'updating')
                .map((source) => (
                  <div key={source.name} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{source.name}</span>
                      <span>更新中...</span>
                    </div>
                    <Progress value={65} className="h-2" />
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 更新确认对话框 */}
      <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认更新数据源</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>确定要更新数据源 "{selectedSource}" 吗？</p>
            <p className="text-sm text-muted-foreground mt-2">
              更新过程可能需要几分钟时间，期间该数据源将暂时不可用。
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setUpdateDialogOpen(false)}
              disabled={updating}
            >
              取消
            </Button>
            <Button
              onClick={() => selectedSource && handleUpdateSource(selectedSource)}
              disabled={updating}
            >
              {updating ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  更新中...
                </>
              ) : (
                '确认更新'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default DataSourcesPage