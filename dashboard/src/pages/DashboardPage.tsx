import React, { useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/spinner'
import {
  TrendingUp,
  CheckCircle,
  Globe as Public,
  Wifi as NetworkCheck,
  Storage,
  RefreshCw,
  Users,
  Activity,
  Server,
  BarChart3,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../contexts/ToastContext'
import { formatNumber } from '@/lib/utils'
import { cn } from '@/lib/utils'
import { HotIP } from '@/types/api'

// 临时的图表组件
const QueryStatsChart = ({ data }: { data: any }) => (
  <div className="h-64 flex items-center justify-center bg-muted/20 rounded-lg">
    <div className="text-center">
      <BarChart3 className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
      <p className="text-sm text-muted-foreground">查询统计图表</p>
      <p className="text-xs text-muted-foreground">数据点: {data?.length || 0}</p>
    </div>
  </div>
)

// 临时的热门IP表格组件
const HotIPsTable = ({ data }: { data: HotIP[] }) => (
  <div className="space-y-2">
    {data?.slice(0, 5).map((ip, index) => (
      <div key={index} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
        <div className="flex items-center gap-3">
          <Badge variant="outline">{index + 1}</Badge>
          <span className="font-mono">{ip.ip}</span>
        </div>
        <div className="text-right">
          <div className="font-semibold">{ip.query_count}</div>
          <div className="text-xs text-muted-foreground">查询次数</div>
        </div>
      </div>
    )) || (
      <div className="text-center py-8 text-muted-foreground">
        <Users className="h-8 w-8 mx-auto mb-2" />
        <p>暂无热门IP数据</p>
      </div>
    )}
  </div>
)

const DashboardPage: React.FC = () => {
  const { showError } = useToast()

  // 使用 useCallback 来稳定 API 调用函数
  const getQueryStats = useCallback(() => apiService.getQueryStats(), [])
  const getHotIPs = useCallback(() => apiService.getHotIPs(), [])
  const getHotIPStats = useCallback(() => apiService.getHotIPStats(), [])
  const getBatchStats = useCallback(() => apiService.getBatchStats(), [])
  const getSystemStatus = useCallback(() => apiService.getSystemStatus(), [])
  const getMetrics = useCallback(() => apiService.getMetrics(), [])

  // 获取监控数据
  const {
    data: queryStats,
    loading: queryLoading,
    error: queryError,
    refetch: refetchQueryStats,
  } = useMonitoringData(getQueryStats, 30000)
  console.log('queryStats:', queryStats)
  const {
    data: hotIPs,
    loading: hotIPsLoading,
    error: hotIPsError,
    refetch: refetchHotIPs,
  } = useMonitoringData(getHotIPs, 60000)
  console.log('hotIPs:', hotIPs)
  const {
    data: hotIPStats,
    loading: hotIPStatsLoading,
    error: hotIPStatsError,
    refetch: refetchHotIPStats,
  } = useMonitoringData(getHotIPStats, 30000)

  const {
    data: batchStats,
    loading: batchLoading,
    error: batchError,
  } = useMonitoringData(getBatchStats, 30000)
  console.log('batchStats:', batchStats)
  const {
    data: systemStatus,
    loading: systemStatusLoading,
    error: systemStatusError,
  } = useMonitoringData(getSystemStatus, 30000)
  console.log('systemStatus:', systemStatus)
  const {
    data: metrics,
    loading: metricsLoading,
    error: metricsError,
  } = useMonitoringData(getMetrics, 30000)
  console.log('metrics:', metrics)
  // 处理错误
  React.useEffect(() => {
    if (queryError) showError('获取查询统计失败: ' + queryError)
    if (hotIPsError) showError('获取热门IP失败: ' + hotIPsError)
    if (hotIPStatsError) showError('获取IP统计失败: ' + hotIPStatsError)
    if (batchError) showError('获取批量统计失败: ' + batchError)
    if (systemStatusError) showError('获取系统状态失败: ' + systemStatusError)
    if (metricsError) showError('获取监控指标失败: ' + metricsError)
  }, [
    queryError,
    hotIPsError,
    hotIPStatsError,
    batchError,
    systemStatusError,
    metricsError,
    showError,
  ])

  // 刷新所有数据
  const handleRefreshAll = () => {
    refetchQueryStats()
    refetchHotIPs()
    refetchHotIPStats()
    // 注意：useMonitoringData hook可能没有返回refetch函数，这里先注释
    // refetchSystemStatus?.()
    // refetchMetrics?.()
  }

  // 统计卡片数据
  const statsCards = [
    {
      title: '总查询次数',
      value: queryStats?.total_queries || 0,
      icon: <Activity className="h-6 w-6" />,
      trend: 'ok',
      trendUp: true,
      loading: queryLoading,
    },
    {
      title: '缓存命中率',
      value: `${(queryStats?.cache_hit_rate || 0).toFixed(1)}%`,
      icon: <TrendingUp className="h-6 w-6" />,
      trend: 'ok',
      trendUp: true,
      loading: queryLoading,
    },
    {
      title: '活跃IP数',
      value: hotIPStats?.hot_ips_count || hotIPs?.length || 0,
      icon: <Public className="h-6 w-6" />,
      trend: 'ok',
      trendUp: true,
      loading: hotIPStatsLoading,
    },
    {
      title: '系统状态',
      value: systemStatus?.status === 'healthy' ? '正常' : systemStatus?.status || '未知',
      icon: <CheckCircle className="h-6 w-6" />,
      trend: 'ok',
      trendUp: true,
      loading: systemStatusLoading,
      valueColor: systemStatus?.status === 'healthy' ? 'text-green-500' : 'text-yellow-500',
    },
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">总览</h1>
          <p className="text-muted-foreground">系统运行状态和关键指标概览</p>
        </div>
        <Button onClick={handleRefreshAll} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardContent className="p-6">
              {card.loading ? (
                <div className="flex items-center justify-center h-20">
                  <Spinner size="lg" />
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <div className={cn('text-primary', card.valueColor)}>{card.icon}</div>
                    <Badge variant={card.trendUp ? 'success' : 'destructive'}>{card.trend}</Badge>
                  </div>
                  <div className="mt-4">
                    <div className={cn('text-2xl font-bold', card.valueColor)}>
                      {typeof card.value === 'number' ? formatNumber(card.value) : card.value}
                    </div>
                    <div className="text-sm text-muted-foreground">{card.title}</div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 查询统计图表 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              查询统计趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            {queryLoading ? (
              <div className="flex items-center justify-center h-64">
                <Spinner size="lg" />
              </div>
            ) : queryError ? (
              <Alert variant="destructive">
                <AlertDescription>加载查询统计失败: {queryError}</AlertDescription>
              </Alert>
            ) : (
              <QueryStatsChart data={queryStats} />
            )}
          </CardContent>
        </Card>

        {/* 热门IP */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              热门IP地址
            </CardTitle>
          </CardHeader>
          <CardContent>
            {hotIPsLoading ? (
              <div className="flex items-center justify-center h-64">
                <Spinner size="lg" />
              </div>
            ) : hotIPsError ? (
              <Alert variant="destructive">
                <AlertDescription>加载热门IP失败: {hotIPsError}</AlertDescription>
              </Alert>
            ) : (
              <HotIPsTable data={hotIPs} />
            )}
          </CardContent>
        </Card>
      </div>

      {/* 系统信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              系统信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {metricsLoading ? (
              <div className="flex items-center justify-center h-64">
                <Spinner size="lg" />
              </div>
            ) : metricsError ? (
              <Alert variant="destructive">
                <AlertDescription>加载监控指标失败: {metricsError}</AlertDescription>
              </Alert>
            ) : (
              <>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">运行时间</span>
                  <span>{metrics?.system?.uptime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">内存使用</span>
                  <span>{metrics?.system?.memory_usage?.toFixed(2)}M</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">CPU使用率</span>
                  <span>{metrics.system.cpu_usage}%</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <NetworkCheck className="h-5 w-5" />
              网络状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">API响应时间</span>
              <span>45ms</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">数据库连接</span>
              <Badge variant="success">正常</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Redis缓存</span>
              <Badge variant="success">正常</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default DashboardPage