@import "tailwindcss";

:root {
  /* 明亮主题颜色 */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 210 100% 56%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 210 100% 56%;
  --radius: 0.5rem;
}

.dark {
  /* 暗色主题颜色 */
  --background: 210 40% 8%;
  --foreground: 210 40% 98%;
  --card: 210 40% 12%;
  --card-foreground: 210 40% 98%;
  --popover: 210 40% 12%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 100% 56%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 16%;
  --secondary-foreground: 210 40% 98%;
  --muted: 210 40% 16%;
  --muted-foreground: 210 40% 65%;
  --accent: 210 40% 16%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 210 40% 18%;
  --input: 210 40% 18%;
  --ring: 210 100% 56%;
}

* {
  box-sizing: border-box;
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* 图表容器样式 */
.recharts-wrapper {
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
}

/* 响应式表格 */
.responsive-table {
  overflow-x: auto;
}

/* 加载动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading-pulse {
  animation: pulse 2s infinite;
}

/* 实用样式类 */
.glass-effect {
  backdrop-filter: blur(10px);
  background: hsl(var(--background) / 0.8);
}

.gradient-border {
  position: relative;
  background: hsl(var(--card));
  border-radius: 0.5rem;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--accent)));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
}

/* 平滑过渡 */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* 焦点环 */
.focus-ring {
  outline: none;
}

.focus-ring:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}
