@import "tailwindcss";

:root {
  --background: 210 40% 8%;
  --foreground: 210 40% 98%;
  --card: 210 40% 12%;
  --card-foreground: 210 40% 98%;
  --popover: 210 40% 12%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 100% 56%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 16%;
  --secondary-foreground: 210 40% 98%;
  --muted: 210 40% 16%;
  --muted-foreground: 210 40% 65%;
  --accent: 210 40% 16%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 210 40% 18%;
  --input: 210 40% 18%;
  --ring: 210 100% 56%;
  --radius: 0.5rem;
}

* {
  box-sizing: border-box;
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 图表容器样式 */
.recharts-wrapper {
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
}

/* 响应式表格 */
.responsive-table {
  overflow-x: auto;
}

/* 加载动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading-pulse {
  animation: pulse 2s infinite;
}
