import { useEffect } from 'react'
import { useToast } from '../contexts/ToastContext'
import { apiService } from '../services/api'

export const useToastIntegration = () => {
  const { showSuccess, showError, showWarning, showInfo } = useToast()

  useEffect(() => {
    // 设置API服务的Toast回调
    apiService.setToastCallback(
      (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
        switch (type) {
          case 'success':
            showSuccess(message)
            break
          case 'error':
            showError(message)
            break
          case 'warning':
            showWarning(message)
            break
          case 'info':
            showInfo(message)
            break
        }
      }
    )
  }, [showSuccess, showError, showWarning, showInfo])

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
  }
}