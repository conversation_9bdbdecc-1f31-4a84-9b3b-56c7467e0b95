import React, { createContext, useContext, useState, useCallback } from 'react'
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider as ToastProviderPrimitive,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast'


type ToastType = 'default' | 'destructive' | 'success' | 'warning'

interface ToastMessage {
  id: string
  title?: string
  message: string
  type: ToastType
  duration?: number
  closable?: boolean
}

interface ToastContextType {
  showToast: (
    message: string,
    type?: ToastType,
    options?: { title?: string; duration?: number; closable?: boolean }
  ) => void
  showSuccess: (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => void
  showError: (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => void
  showWarning: (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => void
  showInfo: (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => void
  hideToast: (id: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([])

  const generateId = () => Math.random().toString(36).substring(2, 11)

  const showToast = useCallback(
    (
      message: string,
      type: ToastType = 'default',
      options: { title?: string; duration?: number; closable?: boolean } = {}
    ) => {
      const { title, duration = 6000, closable = true } = options
      const id = generateId()

      const newToast: ToastMessage = {
        id,
        title,
        message,
        type,
        duration,
        closable,
      }

      setToasts((prev) => [...prev, newToast])

      // 自动关闭
      if (duration > 0) {
        setTimeout(() => {
          hideToast(id)
        }, duration)
      }
    },
    []
  )

  const hideToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  const showSuccess = useCallback(
    (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => {
      showToast(message, 'success', options)
    },
    [showToast]
  )

  const showError = useCallback(
    (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => {
      showToast(message, 'destructive', { duration: 8000, ...options })
    },
    [showToast]
  )

  const showWarning = useCallback(
    (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => {
      showToast(message, 'warning', options)
    },
    [showToast]
  )

  const showInfo = useCallback(
    (message: string, options?: { title?: string; duration?: number; closable?: boolean }) => {
      showToast(message, 'default', options)
    },
    [showToast]
  )

  const value: ToastContextType = {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideToast,
  }

  return (
    <ToastProviderPrimitive>
      <ToastContext.Provider value={value}>
        {children}
        <div className="fixed top-4 right-4 z-50 flex flex-col gap-3 max-w-sm w-full">
          {toasts.map((toast, index) => (
            <Toast
              key={toast.id}
              variant={toast.type}
              open={true}
              className={`
                transform transition-all duration-300 ease-in-out
                ${index === 0 ? 'animate-in slide-in-from-right-full' : ''}
                shadow-lg border border-border/50 backdrop-blur-sm
                bg-background/95 hover:bg-background/100
                ${toast.type === 'success' ? 'border-green-500/30 bg-green-50/95 dark:bg-green-950/95' : ''}
                ${toast.type === 'destructive' ? 'border-red-500/30 bg-red-50/95 dark:bg-red-950/95' : ''}
                ${toast.type === 'warning' ? 'border-yellow-500/30 bg-yellow-50/95 dark:bg-yellow-950/95' : ''}
              `}
            >
              {toast.title && (
                <ToastTitle className={`
                  font-semibold text-sm
                  ${toast.type === 'success' ? 'text-green-800 dark:text-green-200' : ''}
                  ${toast.type === 'destructive' ? 'text-red-800 dark:text-red-200' : ''}
                  ${toast.type === 'warning' ? 'text-yellow-800 dark:text-yellow-200' : ''}
                `}>
                  {toast.title}
                </ToastTitle>
              )}
              <ToastDescription className={`
                text-sm leading-relaxed
                ${toast.type === 'success' ? 'text-green-700 dark:text-green-300' : ''}
                ${toast.type === 'destructive' ? 'text-red-700 dark:text-red-300' : ''}
                ${toast.type === 'warning' ? 'text-yellow-700 dark:text-yellow-300' : ''}
                ${toast.type === 'default' ? 'text-foreground/90' : ''}
              `}>
                {toast.message}
              </ToastDescription>
              {toast.closable && (
                <ToastClose
                  onClick={() => hideToast(toast.id)}
                  className={`
                    hover:bg-background/80 transition-colors duration-200
                    ${toast.type === 'success' ? 'hover:bg-green-100 dark:hover:bg-green-900' : ''}
                    ${toast.type === 'destructive' ? 'hover:bg-red-100 dark:hover:bg-red-900' : ''}
                    ${toast.type === 'warning' ? 'hover:bg-yellow-100 dark:hover:bg-yellow-900' : ''}
                  `}
                />
              )}
            </Toast>
          ))}
        </div>
        <ToastViewport className="hidden" />
      </ToastContext.Provider>
    </ToastProviderPrimitive>
  )
}

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext)
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}