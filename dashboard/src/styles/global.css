/* 全局样式统一性优化 */

/* 统一的容器样式 */
.page-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
}

/* 统一的卡片样式 */
.unified-card {
  background-color: #1e2139;
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.unified-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 统一的卡片内容间距 */
.unified-card-content {
  padding: 20px !important;
}

.unified-card-content:last-child {
  padding-bottom: 20px !important;
}

/* 统一的标题样式 */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 24px;
  color: #ffffff;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #ffffff;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #ffffff;
}

/* 统一的间距系统 */
.spacing-xs { margin: 4px; }
.spacing-sm { margin: 8px; }
.spacing-md { margin: 16px; }
.spacing-lg { margin: 24px; }
.spacing-xl { margin: 32px; }

.spacing-top-xs { margin-top: 4px; }
.spacing-top-sm { margin-top: 8px; }
.spacing-top-md { margin-top: 16px; }
.spacing-top-lg { margin-top: 24px; }
.spacing-top-xl { margin-top: 32px; }

.spacing-bottom-xs { margin-bottom: 4px; }
.spacing-bottom-sm { margin-bottom: 8px; }
.spacing-bottom-md { margin-bottom: 16px; }
.spacing-bottom-lg { margin-bottom: 24px; }
.spacing-bottom-xl { margin-bottom: 32px; }

.padding-xs { padding: 4px; }
.padding-sm { padding: 8px; }
.padding-md { padding: 16px; }
.padding-lg { padding: 24px; }
.padding-xl { padding: 32px; }

/* 统一的网格布局 */
.grid-container {
  display: grid;
  gap: 24px;
  width: 100%;
}

.grid-2-cols {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3-cols {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4-cols {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式网格 */
@media (max-width: 1200px) {
  .grid-4-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .grid-3-cols,
  .grid-4-cols {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .grid-2-cols,
  .grid-3-cols,
  .grid-4-cols {
    grid-template-columns: 1fr;
  }
  
  .page-container {
    padding: 16px;
  }
}

/* 统一的指标卡片高度 */
.metric-card-small {
  height: 120px;
  min-height: 120px;
}

.metric-card-medium {
  height: 200px;
  min-height: 200px;
}

.metric-card-large {
  height: 400px;
  min-height: 400px;
}

/* 响应式指标卡片高度 */
@media (max-width: 768px) {
  .metric-card-small {
    height: 100px;
    min-height: 100px;
  }
  
  .metric-card-medium {
    height: 180px;
    min-height: 180px;
  }
  
  .metric-card-large {
    height: 300px;
    min-height: 300px;
  }
}

/* 统一的按钮样式 */
.unified-button {
  border-radius: 6px;
  text-transform: none;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.unified-button-small {
  padding: 4px 12px;
  font-size: 0.875rem;
}

.unified-button-large {
  padding: 12px 24px;
  font-size: 1rem;
}

/* 统一的表格样式 */
.unified-table {
  background-color: transparent;
}

.unified-table .MuiTableHead-root {
  background-color: rgba(255, 255, 255, 0.05);
}

.unified-table .MuiTableCell-head {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

.unified-table .MuiTableCell-body {
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.8);
}

.unified-table .MuiTableRow-root:hover {
  background-color: rgba(255, 255, 255, 0.04);
}

/* 统一的状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-success {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.status-warning {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.status-error {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.status-info {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

/* 统一的加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 统一的动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* 统一的滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 统一的焦点样式 */
.unified-focus:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* 统一的禁用状态 */
.unified-disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

/* 统一的文本样式 */
.text-primary {
  color: #ffffff;
}

.text-secondary {
  color: rgba(255, 255, 255, 0.7);
}

.text-muted {
  color: rgba(255, 255, 255, 0.5);
}

.text-success {
  color: #4caf50;
}

.text-warning {
  color: #ff9800;
}

.text-error {
  color: #f44336;
}

.text-info {
  color: #2196f3;
}

/* 统一的背景样式 */
.bg-primary {
  background-color: #2196f3;
}

.bg-secondary {
  background-color: #1e2139;
}

.bg-success {
  background-color: rgba(76, 175, 80, 0.1);
}

.bg-warning {
  background-color: rgba(255, 152, 0, 0.1);
}

.bg-error {
  background-color: rgba(244, 67, 54, 0.1);
}

.bg-info {
  background-color: rgba(33, 150, 243, 0.1);
}
