/* Grid 布局优化 */

/* 主容器间距优化 */
.main-container {
  padding: 16px;
  max-width: 100%;
}

/* 指标卡片网格优化 */
.metrics-grid {
  margin-bottom: 24px;
}

.metrics-grid .MuiGrid-item {
  padding: 8px;
}

/* 内容区域网格优化 */
.content-grid {
  margin-top: 16px;
}

.content-grid .MuiGrid-item {
  padding: 12px;
}

/* 卡片容器优化 */
.card-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-container .MuiCardContent-root {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 图表区域优化 */
.chart-area {
  flex: 1;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

/* 表格区域优化 */
.table-area {
  flex: 1;
  min-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 状态区域优化 */
.status-area {
  flex: 1;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .chart-area,
  .table-area {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .main-container {
    padding: 12px;
  }
  
  .metrics-grid .MuiGrid-item {
    padding: 6px;
  }
  
  .content-grid .MuiGrid-item {
    padding: 8px;
  }
  
  .chart-area,
  .table-area,
  .status-area {
    min-height: 200px;
  }
}

/* 卡片标题区域优化 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 卡片内容区域优化 */
.card-content {
  flex: 1;
  overflow: hidden;
}

/* 滚动区域优化 */
.scroll-area {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

.scroll-area::-webkit-scrollbar {
  width: 6px;
}

.scroll-area::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.scroll-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
