/* 布局优化样式 */

/* 确保卡片高度一致 */
.metric-card {
  height: 100%;
  min-height: 120px;
}

/* 优化指标卡片的内边距 */
.metric-card .MuiCardContent-root {
  padding: 16px !important;
}

/* 图表容器优化 */
.chart-container {
  height: 100%;
  min-height: 350px;
}

/* 表格容器优化 */
.table-container {
  height: 100%;
  overflow: auto;
}

/* 状态指示器优化 */
.status-indicator {
  height: 100%;
}

/* 响应式间距优化 */
@media (max-width: 768px) {
  .metric-card {
    min-height: 100px;
  }
  
  .chart-container {
    min-height: 250px;
  }
}

/* 渐变背景优化 */
.gradient-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  margin-bottom: 24px;
}

/* 卡片阴影优化 */
.enhanced-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.enhanced-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 指标数值样式优化 */
.metric-value {
  font-weight: 600;
  font-size: 1.5rem;
}

/* 状态芯片样式优化 */
.status-chip {
  font-weight: 500;
  border-radius: 16px;
}

/* 图标容器优化 */
.icon-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 数据展示优化 */
.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

/* 分隔线样式 */
.divider-spacing {
  margin: 12px 0;
}
