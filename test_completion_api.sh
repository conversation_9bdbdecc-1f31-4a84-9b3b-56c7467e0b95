#!/bin/bash

# IP补全API测试脚本
BASE_URL="http://localhost:8081"
API_KEY="admin-api-key-123456"

echo "=== IP地理信息补全API测试 ==="
echo

# 1. 测试状态查询
echo "1. 查询补全状态..."
curl -s "${BASE_URL}/api/v1/admin/ip-completion/status?api_key=${API_KEY}"
echo
echo

# 2. 测试数据库统计
echo "2. 查询数据库统计信息..."
curl -s "${BASE_URL}/api/v1/admin/ip-completion/database/stats?api_key=${API_KEY}"
echo
echo

# 3. 测试启动补全（如果当前没有运行）
echo "3. 启动IP补全任务..."
curl -s -X POST -d "{}" "${BASE_URL}/api/v1/admin/ip-completion/start?api_key=${API_KEY}"
echo
echo
# curl -s -X POST -d "{}" "http://localhost:8082/api/v1/admin/ip-completion/start?api_key=admin-api-key-123456"

# 5. 再次查询状态（应该显示运行中）
echo "5. 再次查询补全状态..."
sleep 2
curl -s "${BASE_URL}/api/v1/admin/ip-completion/status?api_key=${API_KEY}" | jq '.'
echo
echo

# 6. 停止补全任务
echo "6. 停止补全任务..."
curl -s -X POST "${BASE_URL}/api/v1/admin/ip-completion/stop?api_key=${API_KEY}" | jq '.'
echo
echo

# 7. 最终状态查询
echo "7. 最终状态查询..."
sleep 1
curl -s "${BASE_URL}/api/v1/admin/ip-completion/status?api_key=${API_KEY}" | jq '.'
echo

echo "=== 测试完成 ==="
