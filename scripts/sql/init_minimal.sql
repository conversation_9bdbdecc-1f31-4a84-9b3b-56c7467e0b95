-- ipInsight 数据库初始化脚本
-- 执行前请确保数据库 ipinsight 已存在

BEGIN;

-- 创建检查函数
CREATE OR REPLACE FUNCTION check_initialization_status()
RETURNS TABLE(table_exists boolean, admin_exists boolean, roles_count bigint) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'users') as table_exists,
        EXISTS(SELECT 1 FROM users WHERE username = 'admin' LIMIT 1) as admin_exists,
        COALESCE((SELECT COUNT(*) FROM roles), 0) as roles_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, false, 0::bigint;
END;
$$ LANGUAGE plpgsql;

-- IP数据主表
CREATE TABLE IF NOT EXISTS ip_ranges_new (
    id BIGSERIAL PRIMARY KEY,
    ip_range CIDR NOT NULL UNIQUE,
    start_ip_int BIGINT NOT NULL,
    end_ip_int BIGINT NOT NULL,
    ip_version SMALLINT NOT NULL CHECK (ip_version IN (4, 6)),
    
    -- 地理位置信息
    country_code VARCHAR(2),
    country_name VARCHAR(100),
    city VARCHAR(100),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    
    -- 网络信息
    isp VARCHAR(200),
    asn VARCHAR(60),
    organization VARCHAR(200),
    
    -- 安全信息
    is_proxy BOOLEAN DEFAULT FALSE,
    is_vpn BOOLEAN DEFAULT FALSE,
    is_tor BOOLEAN DEFAULT FALSE,
    
    -- 元数据
    source VARCHAR(50) NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 扩展信息
    geolocation_extended JSONB,
    network_extended JSONB,
    security_extended JSONB,
    extended_data JSONB,
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户管理表
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'operator', 'user', 'readonly')),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    full_name VARCHAR(100),
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    login_count INTEGER DEFAULT 0,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP WITH TIME ZONE,
    api_key VARCHAR(64) UNIQUE,
    api_key_created_at TIMESTAMP WITH TIME ZONE,
    api_key_last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by BIGINT REFERENCES users(id),
    updated_by BIGINT REFERENCES users(id)
);

-- 其他支持表
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    device_info TEXT,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_permissions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by BIGINT REFERENCES users(id),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, permission, resource)
);

CREATE TABLE IF NOT EXISTS user_activity_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB,
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
DO $$
BEGIN
    -- IP表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_ip_ranges_new_gist') THEN
        CREATE INDEX idx_ip_ranges_new_gist ON ip_ranges_new USING GIST (ip_range inet_ops);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_ip_ranges_new_country') THEN
        CREATE INDEX idx_ip_ranges_new_country ON ip_ranges_new (country_code);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_ip_ranges_new_source') THEN
        CREATE INDEX idx_ip_ranges_new_source ON ip_ranges_new (source);
    END IF;
    
    -- 用户表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_username') THEN
        CREATE INDEX idx_users_username ON users(username);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_email') THEN
        CREATE INDEX idx_users_email ON users(email);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_api_key') THEN
        CREATE INDEX idx_users_api_key ON users(api_key);
    END IF;
    
    -- 会话表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_sessions_user_id') THEN
        CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_sessions_token_hash') THEN
        CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_sessions_expires_at') THEN
        CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
    END IF;
END $$;

-- 创建触发器和函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_ip_ranges_new_updated_at ON ip_ranges_new;
CREATE TRIGGER update_ip_ranges_new_updated_at 
    BEFORE UPDATE ON ip_ranges_new 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入基础数据
INSERT INTO roles (name, display_name, description, permissions, is_system_role) VALUES
('admin', 'System Administrator', 'Full system access', '["*"]'::jsonb, true),
('operator', 'System Operator', 'System monitoring and datasource management', '["system:read", "datasource:*", "api:read"]'::jsonb, true),
('user', 'Regular User', 'Basic query permissions', '["api:read", "ip:query"]'::jsonb, true),
('readonly', 'Read Only User', 'View-only access', '["system:read"]'::jsonb, true)
ON CONFLICT (name) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    permissions = EXCLUDED.permissions,
    updated_at = NOW();

-- 创建默认用户
DO $$
DECLARE
    admin_user_id BIGINT;
    operator_user_id BIGINT;
    password_hash TEXT := '$2b$12$NaYHMA.6Sc1K23a2BklSVOmh4MmW/zwuaBaexiBVmOHKGzKVOdqau';
BEGIN
    -- 创建admin用户
    INSERT INTO users (username, email, password_hash, role, status, full_name, email_verified, password_changed_at) 
    VALUES ('admin', '<EMAIL>', password_hash, 'admin', 'active', 'System Administrator', true, NOW())
    ON CONFLICT (username) DO UPDATE SET
        role = 'admin',
        status = 'active',
        email_verified = true,
        updated_at = NOW()
    RETURNING id INTO admin_user_id;
    
    -- 创建operator用户
    INSERT INTO users (username, email, password_hash, role, status, full_name, email_verified, password_changed_at, created_by) 
    VALUES ('operator', '<EMAIL>', password_hash, 'operator', 'active', 'System Operator', true, NOW(), admin_user_id)
    ON CONFLICT (username) DO UPDATE SET
        role = 'operator',
        status = 'active',
        email_verified = true,
        updated_at = NOW();
    
    -- 添加管理员权限
    INSERT INTO user_permissions (user_id, permission, granted_by, granted_at)
    VALUES (admin_user_id, 'system:admin', admin_user_id, NOW())
    ON CONFLICT (user_id, permission, resource) DO NOTHING;
END $$;

-- 创建维护函数
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS integer AS $$
DECLARE
    deleted_count integer;
BEGIN
    DELETE FROM user_sessions WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION generate_api_key()
RETURNS VARCHAR(64) AS $$
DECLARE
    api_key VARCHAR(64);
BEGIN
    SELECT 'ipik_' || encode(gen_random_bytes(24), 'base64') INTO api_key;
    api_key := replace(replace(replace(api_key, '+', ''), '/', ''), '=', '');
    RETURN substring(api_key, 1, 64);
END;
$$ LANGUAGE plpgsql;

-- 提交事务
COMMIT;

-- 清理临时函数
DROP FUNCTION IF EXISTS check_initialization_status();