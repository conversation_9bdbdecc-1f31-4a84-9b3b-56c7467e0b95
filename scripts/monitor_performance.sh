#!/bin/bash

# ipInsight 数据库性能监控脚本
# 用于监控PostgreSQL和Redis的性能指标

set -e

# 配置参数
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ipinsight}"
DB_USER="${DB_USER:-postgres}"

REDIS_HOST="${REDIS_HOST:-localhost}"
REDIS_PORT="${REDIS_PORT:-6379}"

LOG_FILE="/var/log/ipinsight/performance_monitor.log"
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=85
ALERT_THRESHOLD_CONNECTIONS=150

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error() {
    echo -e "${RED}[ERROR] $1${NC}" | tee -a "$LOG_FILE"
}

# 警告函数
warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}" | tee -a "$LOG_FILE"
}

# 信息函数
info() {
    echo -e "${GREEN}[INFO] $1${NC}" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    local deps=("psql" "redis-cli" "jq")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            error "Required dependency '$dep' is not installed"
            exit 1
        fi
    done
}

# PostgreSQL性能监控
monitor_postgresql() {
    info "=== PostgreSQL Performance Monitoring ==="
    
    # 检查数据库连接
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        error "Cannot connect to PostgreSQL database"
        return 1
    fi
    
    # 获取数据库统计信息
    local stats=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT json_build_object(
            'total_connections', (SELECT count(*) FROM pg_stat_activity),
            'active_connections', (SELECT count(*) FROM pg_stat_activity WHERE state = 'active'),
            'idle_connections', (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle'),
            'database_size', pg_size_pretty(pg_database_size('$DB_NAME')),
            'cache_hit_ratio', round((sum(blks_hit) * 100.0 / sum(blks_hit + blks_read))::numeric, 2)
                FROM pg_stat_database WHERE datname = '$DB_NAME'
        );
    ")
    
    echo "$stats" | jq -r '
        "Total Connections: " + (.total_connections | tostring) + "\n" +
        "Active Connections: " + (.active_connections | tostring) + "\n" +
        "Idle Connections: " + (.idle_connections | tostring) + "\n" +
        "Database Size: " + .database_size + "\n" +
        "Cache Hit Ratio: " + (.cache_hit_ratio | tostring) + "%"
    '
    
    # 检查连接数警告
    local total_conn=$(echo "$stats" | jq -r '.total_connections')
    if [ "$total_conn" -gt "$ALERT_THRESHOLD_CONNECTIONS" ]; then
        warning "High connection count: $total_conn (threshold: $ALERT_THRESHOLD_CONNECTIONS)"
    fi
    
    # 获取慢查询信息
    info "--- Slow Queries (>1s) ---"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            query,
            calls,
            total_time,
            mean_time,
            rows
        FROM pg_stat_statements 
        WHERE mean_time > 1000 
        ORDER BY total_time DESC 
        LIMIT 5;
    " 2>/dev/null || echo "pg_stat_statements extension not available"
    
    # 获取表大小信息
    info "--- Top 5 Largest Tables ---"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
        LIMIT 5;
    "
    
    # 获取索引使用情况
    info "--- Index Usage Statistics ---"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            indexname,
            idx_scan,
            CASE 
                WHEN idx_scan = 0 THEN 'UNUSED'
                WHEN idx_scan < 100 THEN 'LOW'
                WHEN idx_scan < 1000 THEN 'MEDIUM'
                ELSE 'HIGH'
            END as usage_level
        FROM pg_stat_user_indexes 
        WHERE tablename LIKE 'ip_%'
        ORDER BY idx_scan DESC 
        LIMIT 10;
    "
}

# Redis性能监控
monitor_redis() {
    info "=== Redis Performance Monitoring ==="
    
    # 检查Redis连接
    if ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping &> /dev/null; then
        error "Cannot connect to Redis server"
        return 1
    fi
    
    # 获取Redis信息
    local redis_info=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" info)
    
    # 解析内存信息
    local used_memory=$(echo "$redis_info" | grep "^used_memory:" | cut -d: -f2 | tr -d '\r')
    local used_memory_human=$(echo "$redis_info" | grep "^used_memory_human:" | cut -d: -f2 | tr -d '\r')
    local used_memory_peak_human=$(echo "$redis_info" | grep "^used_memory_peak_human:" | cut -d: -f2 | tr -d '\r')
    local maxmemory=$(echo "$redis_info" | grep "^maxmemory:" | cut -d: -f2 | tr -d '\r')
    
    # 解析连接信息
    local connected_clients=$(echo "$redis_info" | grep "^connected_clients:" | cut -d: -f2 | tr -d '\r')
    local total_connections_received=$(echo "$redis_info" | grep "^total_connections_received:" | cut -d: -f2 | tr -d '\r')
    
    # 解析统计信息
    local keyspace_hits=$(echo "$redis_info" | grep "^keyspace_hits:" | cut -d: -f2 | tr -d '\r')
    local keyspace_misses=$(echo "$redis_info" | grep "^keyspace_misses:" | cut -d: -f2 | tr -d '\r')
    
    # 计算命中率
    local total_requests=$((keyspace_hits + keyspace_misses))
    local hit_rate=0
    if [ "$total_requests" -gt 0 ]; then
        hit_rate=$(echo "scale=2; $keyspace_hits * 100 / $total_requests" | bc)
    fi
    
    echo "Memory Usage: $used_memory_human"
    echo "Peak Memory: $used_memory_peak_human"
    echo "Connected Clients: $connected_clients"
    echo "Total Connections: $total_connections_received"
    echo "Cache Hit Rate: ${hit_rate}%"
    echo "Keyspace Hits: $keyspace_hits"
    echo "Keyspace Misses: $keyspace_misses"
    
    # 内存使用率检查
    if [ "$maxmemory" -gt 0 ]; then
        local memory_usage_percent=$(echo "scale=2; $used_memory * 100 / $maxmemory" | bc)
        echo "Memory Usage Percent: ${memory_usage_percent}%"
        
        if (( $(echo "$memory_usage_percent > $ALERT_THRESHOLD_MEMORY" | bc -l) )); then
            warning "High memory usage: ${memory_usage_percent}% (threshold: ${ALERT_THRESHOLD_MEMORY}%)"
        fi
    fi
    
    # 获取慢查询日志
    info "--- Redis Slow Queries ---"
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" slowlog get 5
    
    # 获取键空间信息
    info "--- Redis Keyspace Info ---"
    echo "$redis_info" | grep "^db[0-9]"
}

# 系统资源监控
monitor_system() {
    info "=== System Resource Monitoring ==="
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo "CPU Usage: ${cpu_usage}%"
    
    if (( $(echo "$cpu_usage > $ALERT_THRESHOLD_CPU" | bc -l) )); then
        warning "High CPU usage: ${cpu_usage}% (threshold: ${ALERT_THRESHOLD_CPU}%)"
    fi
    
    # 内存使用率
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage_percent=$(echo "scale=2; $used_mem * 100 / $total_mem" | bc)
    
    echo "Memory Usage: ${memory_usage_percent}%"
    
    if (( $(echo "$memory_usage_percent > $ALERT_THRESHOLD_MEMORY" | bc -l) )); then
        warning "High memory usage: ${memory_usage_percent}% (threshold: ${ALERT_THRESHOLD_MEMORY}%)"
    fi
    
    # 磁盘使用率
    echo "Disk Usage:"
    df -h | grep -E "^/dev/"
    
    # 网络连接统计
    echo "Network Connections:"
    netstat -an | awk '/^tcp/ {print $6}' | sort | uniq -c | sort -nr
}

# 生成性能报告
generate_report() {
    local report_file="/tmp/ipinsight_performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "ipInsight Performance Report"
        echo "Generated at: $(date)"
        echo "=================================="
        echo
        
        monitor_postgresql
        echo
        monitor_redis
        echo
        monitor_system
        
    } > "$report_file"
    
    info "Performance report generated: $report_file"
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log "Starting performance monitoring..."
    
    # 检查依赖
    check_dependencies
    
    # 根据参数执行不同的监控
    case "${1:-all}" in
        "postgresql"|"pg")
            monitor_postgresql
            ;;
        "redis")
            monitor_redis
            ;;
        "system")
            monitor_system
            ;;
        "report")
            generate_report
            ;;
        "all"|*)
            monitor_postgresql
            echo
            monitor_redis
            echo
            monitor_system
            ;;
    esac
    
    log "Performance monitoring completed."
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
