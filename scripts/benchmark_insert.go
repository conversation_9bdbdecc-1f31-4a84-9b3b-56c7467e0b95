package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/database"
	"github.com/cosin2077/ipInsight/internal/model"
	"go.uber.org/zap"
)

func main() {
	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 数据库配置
	dbConfig := config.DatabaseConfig{
		Host:     "localhost",
		Port:     5432,
		User:     "postgres",
		Password: "postgres",
		DBName:   "ipinsight",
	}

	// 创建数据库适配器
	adapter, err := database.NewOptimizedDatabaseAdapter(dbConfig, logger)
	if err != nil {
		log.Fatal("Failed to create database adapter:", err)
	}
	defer adapter.Close()

	ctx := context.Background()

	// 测试数据准备
	testSizes := []int{100, 500, 1000, 5000}
	
	for _, size := range testSizes {
		fmt.Printf("\n=== 测试批量插入 %d 条记录 ===\n", size)
		
		// 生成测试数据
		testData := generateTestData(size)
		
		// 测试旧方法（逐条插入）
		fmt.Printf("1. 逐条插入测试...\n")
		start := time.Now()
		for _, ipInfo := range testData {
			adapter.Insert(ctx, ipInfo)
		}
		singleDuration := time.Since(start)
		fmt.Printf("   逐条插入耗时: %v (%.2f records/sec)\n", 
			singleDuration, float64(size)/singleDuration.Seconds())
		
		// 清理数据
		cleanupTestData(ctx, adapter)
		
		// 测试新方法（批量插入）
		fmt.Printf("2. 批量插入测试...\n")
		start = time.Now()
		result, err := adapter.BatchUpsertIPs(ctx, testData)
		batchDuration := time.Since(start)
		
		if err != nil {
			fmt.Printf("   批量插入失败: %v\n", err)
		} else {
			fmt.Printf("   批量插入耗时: %v (%.2f records/sec)\n", 
				batchDuration, float64(size)/batchDuration.Seconds())
			fmt.Printf("   成功记录: %d/%d\n", result.SuccessRecords, result.TotalRecords)
		}
		
		// 性能提升计算
		if batchDuration > 0 && singleDuration > 0 {
			improvement := float64(singleDuration) / float64(batchDuration)
			fmt.Printf("3. 性能提升: %.2fx 倍\n", improvement)
		}
		
		// 清理数据
		cleanupTestData(ctx, adapter)
	}
}

func generateTestData(count int) []model.IPInfo {
	testData := make([]model.IPInfo, count)
	
	for i := 0; i < count; i++ {
		// 生成测试IP段
		baseIP := 100 + i/255
		subIP := i % 255
		
		testData[i] = model.IPInfo{
			IPRange: model.IPRange{
				CIDR:      fmt.Sprintf("%d.%d.%d.0/24", baseIP, subIP, i%255),
				StartIP:   fmt.Sprintf("%d.%d.%d.0", baseIP, subIP, i%255),
				EndIP:     fmt.Sprintf("%d.%d.%d.255", baseIP, subIP, i%255),
				IPVersion: "IPv4",
			},
			Geolocation: model.Geolocation{
				Country: model.Country{
					Code: fmt.Sprintf("T%d", i%10),
					Name: fmt.Sprintf("TestCountry%d", i%10),
				},
				City: fmt.Sprintf("TestCity%d", i%100),
			},
			Network: model.Network{
				ISP: fmt.Sprintf("TestISP%d", i%50),
				ASN: fmt.Sprintf("AS%d", 1000+i%1000),
			},
			Metadata: model.Metadata{
				Source:      "benchmark_test",
				LastUpdated: time.Now().Format(time.RFC3339),
			},
		}
	}
	
	return testData
}

func cleanupTestData(ctx context.Context, adapter *database.OptimizedDatabaseAdapter) {
	// 清理测试数据
	pool := adapter.GetPool()
	pool.Exec(ctx, "DELETE FROM ip_ranges_new WHERE source = 'benchmark_test'")
}