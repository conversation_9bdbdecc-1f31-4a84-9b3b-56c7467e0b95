# ipInsight - Global Insights, Master Location

![Status: In Development](https://img.shields.io/badge/status-in%20development-yellow)

**ipInsight** is a high-performance, enterprise-grade IP geolocation service built in Go. It aggregates and processes IP address data from multiple free databases and APIs, providing accurate and comprehensive geolocation information. The project is designed to be modular, extensible, and easy to deploy, making it ideal for applications requiring IP-based analytics, security, or content personalization.

## Features

- **Multi-Source Data Aggregation**: Integrates data from multiple free IP databases and APIs, including:
  - MaxMind GeoLite2
  - IP2Location LITE
  - DB-IP Lite
  - IPInfoDB
  - ipapi.is
  - IPLocate.io
  - APIs: ipinfo.io, IPGeolocation.io, ipstack
- **Comprehensive IP Information**: Provides detailed geolocation data, including:
  - Country, region, city
  - Latitude, longitude, postal code
  - ISP, ASN, organization
  - Timezone, proxy status, and more
- **High Performance**:
  - RESTful API with single (`/ip/:ip`) and batch (`/ip/batch`) query endpoints
  - Redis caching for fast responses
  - PostgreSQL for efficient IP range storage and querying
- **Automated Updates**: Scheduled data source updates using a cron-based scheduler
- **Data Fusion**: Merges multi-source data with conflict resolution for accuracy
- **Containerized Deployment**: Docker and docker-compose support for easy setup
- **Extensibility**: Modular architecture for adding new data sources or APIs
- **Monitoring Ready**: Prepared for Prometheus and Grafana integration (upcoming)

## Architecture

ipInsight follows a modular, enterprise-grade architecture:

[User Requests] -> [Gin API Gateway] -> [Service Layer] -> [PostgreSQL Database]
                                             -> [Redis Cache]
                                             -> [Data Sources]
[Scheduler] -> [Data Source Manager] -> [Download & Parse] -> [Database]

- **API Gateway**: Built with Gin, supports high-concurrency queries with rate limiting.
- **Service Layer**: Handles business logic, data fusion, and caching.
- **Database**: PostgreSQL stores IP data with CIDR range indexing.
- **Cache**: Redis accelerates queries for frequently accessed IPs.
- **Data Sources**: Modular interfaces for parsing CSV, MMDB, and API data.
- **Scheduler**: Uses gocron for periodic data updates.

## Prerequisites

- **Go**: 1.21 or later
- **Docker**: 20.10 or later
- **Docker Compose**: 2.0 or later
- **PostgreSQL**: 15 or later (optional for non-Docker deployment)
- **Redis**: 7 or later (optional for non-Docker deployment)

## Installation

### Option 1: Docker (Recommended)

1. Clone the repository:

```bash
git clone https://github.com/cosin2077/ipInsight.git
cd ipInsight
```

Update `config/config.yaml` with valid data source URLs and API keys (if applicable).

Start services with docker-compose:
bash

`docker-compose up -d`

Verify the service is running:

```bash
curl <http://localhost:8080/health>

# Should return: {"status": "ok"}
```

### Option 2: Local Development

Clone the repository:

```bash

git clone https://github.com/cosin2077/ipInsight.git
cd ipInsight
```

Install dependencies:

```bash

go mod download

````

Set up PostgreSQL and Redis:
Install PostgreSQL and create a database named ipinsight.

Install Redis and ensure it's running.

Update `config/config.yaml` with your database and Redis connection details.

Run the application:

```bash

go run cmd/ipInsight/main.go
```

Verify the service:

```bash

curl <http://localhost:8080/health>
```

debug with vscode:
`.vscode/launch.json`

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "go debug",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${file}",
      "cwd": "${cwd}",
      "args": ["--force"]
    },
  ]
}
```

### Usage

Query a Single IP

```bash
curl <http://localhost:8080/ip/*******>
```

Example Response:

```json
{
  "ip_range": {
    "cidr": "*******/24",
    "ip_version": "IPv4"
  },
  "geolocation": {
    "country": {
      "code": "AU",
      "name": "Australia"
    },
    "region": {
      "name": "Queensland"
    },
    "city": "Brisbane",
    "latitude": -27.4705,
    "longitude": 153.0260
  },
  "network": {
    "isp": "Cloudflare",
    "organization": "Cloudflare, Inc."
  },
  "timezone": {
    "name": "Australia/Brisbane"
  },
  "metadata": {
    "source": "maxmind",
    "last_updated": "2025-04-21T00:00:00Z",
    "confidence": 90
  }
}
```

Query Multiple IPs (Batch)

```bash
curl -X POST <http://localhost:8080/ip/batch> -H "Content-Type: application/json" -d '{"ips": ["*******", "*******"]}'
```

Example Response:

```json

{
  "*******": { /*IPInfo structure */ },
  "*******": { /* IPInfo structure*/ }
}
```

### Configuration

Edit `config/config.yaml` to customize settings:
Server: Port for the API (default: 8080).

Database: PostgreSQL connection details.

Cache: Redis connection details.

Data Sources: URLs, schedules, and API keys for each data source.

Example:

```yaml

server:
  port: 8080
database:
  host: postgres
  port: 5432
  user: postgres
  password: postgres
  dbname: ipinsight
cache:
  host: redis
  port: 6379
  password: ""
datasources:
  maxmind:
    url: xxxx
    schedule: "0 0 ** 4"
  # Other data sources
```

### Data Sources

ipInsight integrates the following free data sources:

| Data Source         | Type        | Fields Provided                      | Update Frequency |
|---------------------|-------------|--------------------------------------|------------------|
| MaxMind GeoLite2    | CSV, MMDB   | Country, region, city, lat/lon, ISP, ASN | Weekly           |
| IP2Location LITE    | CSV         | Country, region, city, lat/lon, timezone | Monthly          |
| DB-IP Lite          | MMDB        | Country, city, lat/lon, ISP          | Monthly          |
| IPInfoDB            | CSV         | Country, region, city, lat/lon, ISP  | Monthly          |
| IPLocate.io         | CSV         | Country                              | Daily            |
| IPGeolocation.io    | API         | Country, region, city, lat/lon, timezone | Real-time    |

>Note: Some data sources require registration or API keys. Update `config/config.yaml` accordingly.

### TODO
- IP CIDR cross comparison
- IP data fusion
- IP health information
- IP proxy information, unlocking information, anonymous information
- Missing IP source tracking
- IP information completion and storage