server:
  port: 8081
database:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  dbname: ipinsight
cache:
  host: localhost
  port: 6379
  password: ""
auth:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
  api_keys:
    - "admin-api-key-123456"
    - "backup-api-key-789012"
  token_ttl: 24  # JWT token TTL in hours
  admin_users:
    - "admin"
    - "operator"
  # 默认管理员用户配置 - 系统启动时自动创建
  default_admin:
    enabled: true  # 是否启用默认管理员用户创建
    username: "admin"
    password: "admin123"  # 请在生产环境中修改此密码
    email: "<EMAIL>"
    full_name: "系统管理员"

# 启动配置
startup:
  # 是否在系统启动时自动更新数据源
  # 设置为 false 可以显著提升启动速度，减少资源占用
  # 用户可以通过 /api/v1/admin/fetch API 手动触发数据源更新
  auto_update_datasources: false
# IP地理信息补全配置
ip_completion:
  enabled: false  # 是否启用IP补全功能
  api_key: ""     # ipapi.co API密钥，留空使用免费版本
  batch_size: 50  # 批处理大小
  concurrent_limit: 5  # 并发限制
  enable_proxy: true  # 是否启用代理
  enable_cidr_optimization: true  # 是否启用CIDR优化
  proxy_config_path: "proxy/proxy.txt"  # 代理配置文件路径

  # 重试配置
  max_retries: 3      # 最大重试次数
  initial_delay: 1000 # 初始延迟(毫秒)
  max_delay: 30000    # 最大延迟(毫秒)
  backoff_factor: 2   # 退避因子

  # 速率限制
  requests_per_second: 2  # 每秒请求数限制(免费版本限制)
datasources:
  # MaxMind 数据源 - 需要环境变量 maxmind_id 和 maxmind_license_key
  maxmind:
    url:
      - https://download.maxmind.com/geoip/databases/GeoLite2-ASN/download?suffix=tar.gz
      - https://download.maxmind.com/geoip/databases/GeoLite2-City/download?suffix=tar.gz
      - https://download.maxmind.com/geoip/databases/GeoLite2-Country/download?suffix=tar.gz
    schedule: "0 2 * * 0" # 每周日 02:00 更新
    enabled: true

  # IP2Location 数据源 - 需要环境变量 ip2location_key
  ip2location:
    url:
      - "https://www.ip2location.com/download/?token={ip2location_key}&file=DB11LITECSV"
      - "https://www.ip2location.com/download/?token={ip2location_key}&file=PX12LITECSV"
      - "https://www.ip2location.com/download/?token={ip2location_key}&file=DBASNLITE"
    schedule: "0 3 * * 0" # 每周日 03:00 更新
    enabled: true

  # DB-IP 数据源 - 免费数据，自动日期检测
  dbip:
    url:
      - "https://download.db-ip.com/free/dbip-country-lite-{YYYY-MM}.mmdb.gz"
      - "https://download.db-ip.com/free/dbip-city-lite-{YYYY-MM}.mmdb.gz"
    schedule: "0 4 * * 0" # 每周日 04:00 更新
    enabled: true

  # IPAPI 数据源 - 免费数据
  ipapi:
    url:
      - "https://ipapi.is/data/geolocationDatabaseIPv4.csv.zip"
      - "https://ipapi.is/data/geolocationDatabaseIPv6.csv.zip"
    schedule: "0 5 * * 0" # 每周日 05:00 更新
    enabled: true

  # IPLocate 数据源 - GitHub 免费数据
  iplocate:
    url:
      - "https://github.com/iplocate/ip-address-databases/raw/refs/heads/main/ip-to-asn/ip-to-asn.mmdb?download="
      - "https://github.com/iplocate/ip-address-databases/raw/refs/heads/main/ip-to-country/ip-to-country.mmdb?download="
    schedule: "0 6 * * 0" # 每周日 06:00 更新
    enabled: true

  # QQWry 纯真IP数据库 - 中文地理位置数据
  qqwry:
    url:
      - "https://aite.xyz/share-file/qqwry/qqwry.dat"
      - "https://aite.xyz/share-file/qqwry/qqwry.ipdb"
    schedule: "0 7 * * 0" # 每周日 07:00 更新
    enabled: true
