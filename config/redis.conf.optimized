# Redis 优化配置文件
# 适用于 ipInsight 项目的缓存性能优化
# 请根据实际硬件配置和业务需求调整参数

# =============================================================================
# 网络配置
# =============================================================================

# 绑定地址
bind 127.0.0.1

# 端口
port 6379

# TCP监听队列长度
tcp-backlog 511

# 客户端超时时间（0表示禁用）
timeout 0

# TCP keepalive
tcp-keepalive 300

# =============================================================================
# 内存配置
# =============================================================================

# 最大内存限制 - 建议设置为系统内存的50-70%
# 对于8GB内存的系统，设置为4GB
maxmemory 4gb

# 内存淘汰策略
# allkeys-lru: 在所有键中使用LRU算法删除键
# volatile-lru: 在设置了过期时间的键中使用LRU算法删除键
# allkeys-lfu: 在所有键中使用LFU算法删除键（Redis 4.0+）
maxmemory-policy allkeys-lru

# 内存采样数量（用于LRU/LFU算法）
maxmemory-samples 5

# =============================================================================
# 持久化配置
# =============================================================================

# RDB持久化配置
# 格式：save <seconds> <changes>
save 900 1      # 900秒内至少1个key变化时保存
save 300 10     # 300秒内至少10个key变化时保存
save 60 10000   # 60秒内至少10000个key变化时保存

# RDB文件名
dbfilename dump.rdb

# RDB文件目录
dir /var/lib/redis

# RDB文件压缩
rdbcompression yes

# RDB文件校验
rdbchecksum yes

# AOF持久化配置（可选，根据需求启用）
appendonly no
appendfilename "appendonly.aof"
appendfsync everysec

# =============================================================================
# 客户端配置
# =============================================================================

# 最大客户端连接数
maxclients 10000

# 客户端输出缓冲区限制
# 格式：client-output-buffer-limit <class> <hard limit> <soft limit> <soft seconds>
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# =============================================================================
# 性能优化配置
# =============================================================================

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置（Redis 5.0+）
stream-node-max-bytes 4096
stream-node-max-entries 100

# =============================================================================
# 慢查询日志配置
# =============================================================================

# 慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 慢查询日志最大长度
slowlog-max-len 128

# =============================================================================
# 延迟监控配置
# =============================================================================

# 延迟监控阈值（毫秒）
latency-monitor-threshold 100

# =============================================================================
# 安全配置
# =============================================================================

# 密码认证（生产环境建议设置）
# requirepass your_redis_password

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_9a8b7c6d5e4f"

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别：debug, verbose, notice, warning
loglevel notice

# 日志文件
logfile /var/log/redis/redis-server.log

# 系统日志
syslog-enabled no

# =============================================================================
# 数据库配置
# =============================================================================

# 数据库数量
databases 16

# =============================================================================
# 复制配置（主从复制）
# =============================================================================

# 从服务器只读
replica-read-only yes

# 复制超时时间
repl-timeout 60

# 复制积压缓冲区大小
repl-backlog-size 1mb

# 复制积压缓冲区TTL
repl-backlog-ttl 3600

# =============================================================================
# 集群配置（如果使用Redis Cluster）
# =============================================================================

# 启用集群模式
# cluster-enabled yes

# 集群配置文件
# cluster-config-file nodes-6379.conf

# 集群节点超时时间
# cluster-node-timeout 15000

# =============================================================================
# 模块配置
# =============================================================================

# 加载模块（如果需要）
# loadmodule /path/to/module.so

# =============================================================================
# 特定于ipInsight的优化配置
# =============================================================================

# 针对IP地址缓存的优化
# 由于IP地址通常是字符串，优化字符串存储
hash-max-ziplist-entries 1024
hash-max-ziplist-value 128

# 针对热门IP管理的优化
# 增加有序集合的ziplist阈值
zset-max-ziplist-entries 256
zset-max-ziplist-value 128

# =============================================================================
# 监控和统计配置
# =============================================================================

# 启用统计信息
# 可以通过INFO命令查看详细统计信息

# 内存使用情况监控
# 可以通过MEMORY USAGE命令查看键的内存使用

# =============================================================================
# 高级配置
# =============================================================================

# 后台保存出错时停止写入
stop-writes-on-bgsave-error yes

# 主从复制时从服务器是否响应客户端请求
replica-serve-stale-data yes

# 从服务器优先级（用于Sentinel）
replica-priority 100

# 最小从服务器数量（用于主服务器写入控制）
min-replicas-to-write 0
min-replicas-max-lag 10

# Lua脚本超时时间（毫秒）
lua-time-limit 5000

# 通知配置
notify-keyspace-events ""

# =============================================================================
# 性能调优建议
# =============================================================================

# 1. 内存优化：
#    - 使用合适的数据结构
#    - 设置合理的过期时间
#    - 定期清理无用数据

# 2. 网络优化：
#    - 使用Pipeline减少网络往返
#    - 批量操作减少命令数量
#    - 使用连接池

# 3. 持久化优化：
#    - 根据业务需求选择RDB或AOF
#    - 在从服务器上进行持久化操作
#    - 合理设置保存频率

# 4. 监控指标：
#    - 内存使用率
#    - 命中率
#    - 连接数
#    - 慢查询
#    - 延迟

# =============================================================================
# 常用监控命令
# =============================================================================

# 查看内存使用情况：
# INFO memory

# 查看客户端连接：
# CLIENT LIST

# 查看慢查询日志：
# SLOWLOG GET 10

# 查看键空间统计：
# INFO keyspace

# 查看复制状态：
# INFO replication

# 查看延迟统计：
# LATENCY LATEST

# =============================================================================
# 备注
# =============================================================================

# 配置文件修改后需要重启Redis服务：
# sudo systemctl restart redis
#
# 部分参数可以通过CONFIG SET命令动态修改：
# CONFIG SET maxmemory 2gb
#
# 查看当前配置：
# CONFIG GET maxmemory
#
# 保存当前配置到文件：
# CONFIG REWRITE
