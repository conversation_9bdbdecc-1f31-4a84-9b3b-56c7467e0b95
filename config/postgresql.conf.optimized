# PostgreSQL 优化配置文件
# 适用于 ipInsight 项目的数据库性能优化
# 请根据实际硬件配置调整参数

# =============================================================================
# 内存配置
# =============================================================================

# 共享缓冲区 - 建议设置为系统内存的25%
# 对于8GB内存的系统，设置为2GB
shared_buffers = 2GB

# 有效缓存大小 - 建议设置为系统内存的75%
# 对于8GB内存的系统，设置为6GB
effective_cache_size = 6GB

# 工作内存 - 用于排序和哈希操作
# 计算公式：(总内存 - shared_buffers) / max_connections / 2
work_mem = 32MB

# 维护工作内存 - 用于VACUUM、CREATE INDEX等操作
maintenance_work_mem = 512MB

# 自动VACUUM工作内存
autovacuum_work_mem = 512MB

# =============================================================================
# 连接配置
# =============================================================================

# 最大连接数
max_connections = 200

# 超级用户保留连接数
superuser_reserved_connections = 3

# 连接超时时间
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3

# =============================================================================
# WAL (Write-Ahead Logging) 配置
# =============================================================================

# WAL缓冲区
wal_buffers = 64MB

# WAL段大小
wal_segment_size = 1GB

# 检查点配置
checkpoint_timeout = 15min
checkpoint_completion_target = 0.9
checkpoint_warning = 30s

# WAL写入配置
wal_writer_delay = 200ms
wal_writer_flush_after = 1MB

# =============================================================================
# 查询优化配置
# =============================================================================

# 随机页面成本 - SSD优化
random_page_cost = 1.1

# 顺序页面成本
seq_page_cost = 1.0

# 有效IO并发 - SSD优化
effective_io_concurrency = 200

# 最大并行工作进程
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# 统计信息配置
default_statistics_target = 100
constraint_exclusion = partition

# =============================================================================
# 自动VACUUM配置
# =============================================================================

# 启用自动VACUUM
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min

# VACUUM阈值配置
autovacuum_vacuum_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_threshold = 50
autovacuum_analyze_scale_factor = 0.05

# VACUUM成本配置
autovacuum_vacuum_cost_delay = 10ms
autovacuum_vacuum_cost_limit = 1000

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别
log_min_messages = warning
log_min_error_statement = error

# 慢查询日志
log_min_duration_statement = 1000  # 记录执行时间超过1秒的查询
log_statement = 'none'
log_duration = off

# 连接日志
log_connections = on
log_disconnections = on

# 锁等待日志
log_lock_waits = on
deadlock_timeout = 1s

# 检查点日志
log_checkpoints = on

# 自动VACUUM日志
log_autovacuum_min_duration = 0

# 日志格式
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_timezone = 'UTC'

# =============================================================================
# 其他性能配置
# =============================================================================

# 启用JIT编译（PostgreSQL 11+）
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000

# 并行查询配置
force_parallel_mode = off
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0

# 哈希表配置
hash_mem_multiplier = 1.0

# 临时文件配置
temp_file_limit = 10GB

# =============================================================================
# 扩展配置
# =============================================================================

# 预加载共享库
shared_preload_libraries = 'pg_stat_statements'

# pg_stat_statements配置
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = off
pg_stat_statements.save = on

# =============================================================================
# 特定于ipInsight的优化配置
# =============================================================================

# 针对CIDR查询优化
enable_gist = on
enable_hashjoin = on
enable_mergejoin = on
enable_nestloop = on

# JSONB查询优化
enable_hashagg = on
enable_sort = on

# 分区表优化
enable_partition_pruning = on
enable_partitionwise_join = on
enable_partitionwise_aggregate = on

# =============================================================================
# 监控配置
# =============================================================================

# 统计信息收集
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# 统计信息视图配置
stats_temp_directory = 'pg_stat_tmp'

# =============================================================================
# 安全配置
# =============================================================================

# SSL配置（生产环境建议启用）
ssl = off  # 开发环境可以关闭，生产环境建议开启
ssl_prefer_server_ciphers = on

# 认证配置
password_encryption = scram-sha-256

# =============================================================================
# 区域设置
# =============================================================================

# 字符集和排序规则
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'

# 默认文本搜索配置
default_text_search_config = 'pg_catalog.english'

# =============================================================================
# 备注
# =============================================================================

# 配置文件修改后需要重启PostgreSQL服务：
# sudo systemctl restart postgresql
#
# 部分参数可以通过RELOAD生效：
# sudo systemctl reload postgresql
#
# 查看当前配置：
# SELECT name, setting, unit, context FROM pg_settings WHERE name = 'shared_buffers';
#
# 监控查询性能：
# SELECT query, calls, total_time, mean_time FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;
