# 阶段 1：构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 go.mod 和 go.sum，下载依赖
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用，启用 CGO，生成静态二进制
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ipInsight ./cmd/ipInsight

# 阶段 2：运行阶段
FROM alpine:3.18

# 安装运行时依赖（例如 ca-certificates 用于 HTTPS）
RUN apk add --no-cache ca-certificates

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/ipInsight .

# 复制配置文件
COPY config/config.yaml ./config/

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./ipInsight"]