{"version": "0.2.0", "configurations": [{"name": "Launch ipInsight", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/cmd/ipInsight/main.go", "cwd": "${workspaceFolder}", "env": {}, "args": [], "showLog": true}, {"name": "Launch ipInsight with force flag", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/cmd/ipInsight/main.go", "cwd": "${workspaceFolder}", "env": {}, "args": ["--force"], "showLog": true}]}