version: '3.8'

services:
  ipinsight:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    environment:
      - CONFIG_PATH=/app/config/config.yaml
    volumes:
      - ./data:/app/data # 持久化数据目录
    networks:
      - ipinsight-network

  postgres:
    image: postgres:15-alpine
    container_name: ip-insight-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ipinsight
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - ipinsight-network

  redis:
    image: redis:7-alpine
    container_name: ip-insight-redis
    volumes:
      - redis-data:/data
    networks:
      - ipinsight-network

volumes:
  postgres-data:
  redis-data:


networks:
  ipinsight-network:
    driver: bridge
