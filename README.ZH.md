# ipInsight - 高性能IP数据库

![开发状态：进行中](https://img.shields.io/badge/status-in%20development-yellow)

**ipInsight** 是一个使用 Go 语言构建的高性能企业级 IP 地理位置服务。它聚合并处理来自多个数据库，提供准确和全面的地理位置信息。项目采用模块化设计，易于扩展和部署，非常适合需要基于 IP 的分析、安全或内容个性化的应用。

## 特性

- **多源数据聚合**：整合多个 IP 数据库和 API 的数据，包括：
  - MaxMind GeoLite2
  - IP2Location LITE
  - DB-IP Lite
  - IPInfoDB
  - ipapi.is
  - IPLocate.io
  - API接口：ipinfo.io, IPGeolocation.io, ipstack
- **全面的 IP 信息**：提供详细的地理位置数据，包括：
  - 国家、地区、城市
  - 纬度、经度、邮政编码
  - ISP、ASN、组织机构
  - 时区、代理状态等更多信息
- **高性能**：
  - RESTful API 支持单个（`/ip/:ip`）和批量（`/ip/batch`）查询接口
  - Redis 缓存实现快速响应
  - PostgreSQL 实现高效的 IP 范围存储和查询
- **自动更新**：使用基于 cron 的调度器进行数据源更新
- **数据融合**：通过冲突解决机制合并多源数据以提高准确性
- **容器化部署**：支持 Docker 和 docker-compose 实现简易部署
- **可扩展性**：模块化架构便于添加新的数据源或 API
- **监控就绪**：已准备好集成 Prometheus 和 Grafana（即将推出）

## 架构

ipInsight 采用模块化的企业级架构：

[用户请求] -> [Gin API 网关] -> [服务层] -> [PostgreSQL 数据库]
                                      -> [Redis 缓存]
                                      -> [数据源]
[调度器] -> [数据源管理器] -> [下载 & 解析 & 数据清洗 & 数据融合] -> [数据库]

- **API 网关**：基于 Gin 构建，支持高并发查询和限流。
- **服务层**：处理业务逻辑、数据融合和缓存。
- **数据库**：PostgreSQL 使用 CIDR 范围索引存储 IP 数据。
- **缓存**：Redis 加速频繁访问的 IP 查询。
- **数据源**：模块化接口用于解析 CSV、MMDB 和 API 数据。
- **调度器**：使用 gocron 进行定期数据更新。

## 环境要求

- **Go**：1.21 或更高版本
- **Docker**：20.10 或更高版本
- **Docker Compose**：2.0 或更高版本
- **PostgreSQL**：15 或更高版本（非 Docker 部署可选）
- **Redis**：7 或更高版本（非 Docker 部署可选）

## 安装

### 方式一：Docker（推荐）

1. 克隆仓库：

```bash
git clone https://github.com/cosin2077/ipInsight.git
cd ipInsight
```

更新 `config/config.yaml` 配置文件，添加有效的数据源 URL 和 API 密钥（如适用）。

使用 docker-compose 启动服务：

```bash
docker-compose up -d
```

验证服务是否运行：

```bash
curl http://localhost:8080/health

# 应返回：{"status": "ok"}
```

### 方式二：本地开发

克隆仓库：

```bash
git clone https://github.com/cosin2077/ipInsight.git
cd ipInsight
```

数据库初始化：
```
docker exec -i ip-insight-postgres psql -U postgres -d ipinsight < scripts/sql/init_minimal.sql

# 1. 检查表结构
docker exec -i ip-insight-postgres psql -U postgres -d ipinsight -c "SELECT table_name FROM 
information_schema.tables WHERE table_schema = 'public';"

# 2. 检查默认用户
docker exec -i ip-insight-postgres psql -U postgres -d ipinsight -c "SELECT username, role, 
status FROM users;"
```

安装依赖：

```bash
go mod download
```

设置 PostgreSQL 和 Redis：
安装 PostgreSQL 并创建名为 ipinsight 的数据库。

安装并确保 Redis 正在运行。

更新 `config/config.yaml` 中的数据库和 Redis 连接详情。

运行应用：

```bash
go run cmd/ipInsight/main.go
```

验证服务：

```bash
curl http://localhost:8080/health
```

使用 VSCode 调试：
`.vscode/launch.json`

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "go debug",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${file}",
      "cwd": "${cwd}",
      "args": ["--force"]
    }
  ]
}
```

### 使用方法

查询单个 IP

```bash
curl http://localhost:8080/ip/*******
```

示例响应：

```json
{
  "ip_range": {
    "cidr": "*******/24",
    "ip_version": "IPv4"
  },
  "geolocation": {
    "country": {
      "code": "AU",
      "name": "Australia"
    },
    "region": {
      "name": "Queensland"
    },
    "city": "Brisbane",
    "latitude": -27.4705,
    "longitude": 153.0260
  },
  "network": {
    "isp": "Cloudflare",
    "organization": "Cloudflare, Inc."
  },
  "timezone": {
    "name": "Australia/Brisbane"
  },
  "metadata": {
    "source": "maxmind",
    "last_updated": "2025-04-21T00:00:00Z",
    "confidence": 90
  }
}
```

查询多个 IP（批量）

```bash
curl -X POST http://localhost:8080/ip/batch -H "Content-Type: application/json" -d '{"ips": ["*******", "*******"]}'
```

示例响应：

```json
{
  "*******": { /*IP信息结构*/ },
  "*******": { /*IP信息结构*/ }
}
```

### 配置

编辑 `config/config.yaml` 自定义设置：
服务器：API 端口（默认：8080）。

数据库：PostgreSQL 连接详情。

缓存：Redis 连接详情。

数据源：每个数据源的 URL、更新计划和 API 密钥。

示例：

```yaml
server:
  port: 8080
database:
  host: postgres
  port: 5432
  user: postgres
  password: postgres
  dbname: ipinsight
cache:
  host: redis
  port: 6379
  password: ""
datasources:
  maxmind:
    url: xxxx
    schedule: "0 0 ** 4"
  # 其他数据源
```

### 数据源

ipInsight 集成了以下免费数据源：

| 数据源             | 类型         | 提供的字段                           | 更新频率        |
|-------------------|--------------|-------------------------------------|----------------|
| MaxMind GeoLite2  | CSV, MMDB    | 国家、地区、城市、经纬度、ISP、ASN      | 每周           |
| IP2Location LITE  | CSV          | 国家、地区、城市、经纬度、时区          | 每月           |
| DB-IP Lite        | MMDB         | 国家、城市、经纬度、ISP               | 每月           |
| IPInfoDB          | CSV          | 国家、地区、城市、经纬度、ISP          | 每月           |
| IPLocate.io       | CSV          | 国家                                | 每日           |
| IPGeolocation.io  | API          | 国家、地区、城市、经纬度、时区         | 实时           |

>注意：某些数据源需要注册或 API 密钥。请相应更新 `config/config.yaml`。


### TODO
- IP CIDR 交叉对比
- IP 数据融合
- IP 健康度信息
- IP 代理信息、解锁信息、匿名信息
- 缺失 IP 回源
- IP 信息补全入库

nodemon --exec go run cmd/ipInsight/main.go --signal SIGTERM
