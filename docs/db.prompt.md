prompt1
# ipInsight 数据源解析

按照我的计划，在 config.yaml 配置了数据源信息的话（之后可能还会增加），都支持相应的逻辑：下载，解析，交叉验证，插入数据库。

请帮我检查目前配置的几个数据源是否能正常工作。（好像很多个源都不能正常工作）
如果不能，请帮我做出对应的修复。
注意：有些数据源可能不可用，请打印提示我更新相应数据源。但如果本地有相应文件，也可继续流转上述逻辑。

prompt2
请对 ipInsight 项目的数据源配置和下载机制进行以下优化：

## 1. 数据源配置优化
- **移除未配置的数据源**：从 config.yaml 中删除没有有效 URL 或 API 密钥的数据源配置，避免无效的下载尝试
- **更新调度策略**：将所有数据源的 schedule 配置统一改为每周运行一次，但为每个数据源设置不同的小时偏移（相差1小时），便于调试时查看日志和避免资源竞争

## 2. 下载优化机制
- **文件大小检查**：在下载前通过 HTTP HEAD 请求获取远程文件大小，与本地文件大小对比，如果一致则跳过下载
- **文件完整性验证**：结合文件大小和最后修改时间进行更精确的重复下载检测
- **下载进度优化**：为大文件下载添加进度显示和断点续传支持

## 3. 数据源配置更新

### MaxMind 数据源
- 确认环境变量 `maxmind_id` 和 `maxmind_license_key` 已正确配置
- 验证 MaxMind 数据源的下载和解析功能

### IP2Location 数据源  
- 使用环境变量 `ip2location_key` 配置认证
- 实现以下数据库代码的下载逻辑：
  - `DB11LITECSV` → `DB11.LITE` (IP-COUNTRY-REGION-CITY-LATITUDE-LONGITUDE-ZIPCODE-TIMEZONE)
  - `PX12LITECSV` → `PX12.LITE` (IP-PROXYTYPE-COUNTRY-REGION-CITY-ISP-DOMAIN-USAGETYPE-ASN-LASTSEEN-THREAT-RESIDENTIAL-PROVIDER-FRAUDSCORE)  
  - `DBASNLITE` → `DB-ASN.LITE` (IP-ASN)
- 下载 URL 格式：`https://www.ip2location.com/download/?token={ip2location_key}&file={DATABASE_CODE}`

### DB-IP 数据源
- 更新为新的有效 URL（无需 API 密钥）
- **注意**：文件名中的日期部分（如 2025-07）需要根据实际发布时间动态调整，建议实现自动日期检测机制

### IPAPI 数据源
- 确认当前 URL 配置正确
- 修复之前发现的 CSV 解析问题（bare quote 错误）

## 4. 新增纯真 IP 数据库（QQWry）
- **新增数据源**：在 `internal/datasource/qqwry/` 目录下实现 QQWry 数据源
- **解析逻辑**：参考已提供的 `qqwry.go` 文件中的解析代码，实现纯真 IP 数据库的读取和解析
- **数据结构映射**：将 QQWry 数据格式转换为标准的 `model.IPInfo` 结构
- **配置集成**：在 config.yaml 中添加 QQWry 数据源配置
- **缓存优化**：利用现有的 `locationCache = &sync.Map{}` 缓存机制提高查询性能

## 5. 实施优先级
1. 首先实现文件大小检查的下载优化
2. 更新调度配置和移除无效数据源
3. 配置和测试 MaxMind、IP2Location 数据源
4. 更新 DB-IP URL 并实现日期自动检测
5. 最后实现 QQWry 数据源集成

请确保所有修改都经过测试，并保持与现有三层查询架构和数据融合算法的兼容性。
prompt3
请进行以下优化和修正：

1. **服务依赖问题修正**：
   - 不要使用brew或docker重新启动PostgreSQL和Redis服务
   - 这些服务在主程序启动时已经启动，应该直接使用现有的服务连接
   - 检查数据库连接配置是否正确，确保能连接到本地运行的PostgreSQL实例

2. **数据源验证方式优化**：
   - 改用API方式验证数据源，而不是直接运行测试脚本
   - 通过 `/api/v1/admin/fetch` 接口分别指定单个数据源进行测试
   - 不用强制重新下载数据

3. **IP2Proxy CSV解析逻辑修正**：
   - 当前ParseIP2ProxyCSV函数的字段映射与实际数据不匹配
   - 实际CSV数据格式为16列，示例数据行：
     ```
     "16812039","16812039","PUB","TH","Thailand","Ratchaburi","Suan Phueng","TOT Public Company Limited","tot.co.th","ISP/MOB","23969","TOT Public Company Limited","1","-","-","36"
     ```
   - 需要将这16列正确映射到以下字段：
     - IP起始范围 - IP结束范围 - PROXYTYPE - COUNTRY - REGION - CITY - ISP - DOMAIN - USAGETYPE - ASN - 第11列(ISP名称) - LASTSEEN - THREAT - RESIDENTIAL - PROVIDER - FRAUDSCORE
   - 修正解析函数以正确处理这种数据格式

4. **ipapi**
  - 下载格式如下：https://ipapi.is/data/geolocationDatabaseIPv4.csv.zip，应该是 zip，需要有相应的解压缩逻辑
  - 下载格式如下：https://ipapi.is/data/geolocationDatabaseIPv6.csv.zip，应该是 zip，需要有相应的解压缩逻辑
  - 数据类似如下：
  geolocationDatabaseIPv4:
    ip_version,start_ip,end_ip,continent,country_code,country,state,city,zip,timezone,latitude,longitude,accuracy,source
    4,*******,*********,OC,AU,Australia,Victoria,Research,3760,Australia/Melbourne,-37.7,145.18333,3,whoisDescrAttr
  geolocationDatabaseIPv6:
    ip_version,start_ip,end_ip,continent,country_code,country,state,city,zip,timezone,latitude,longitude,accuracy,source
    6,0202:0970:0000:0000:0000:0000:0000:0000,0202:0970:ffff:ffff:ffff:ffff:ffff:ffff,EU,CZ,Czechia,Hlavní město Praha,Prague,110 00,Europe/Prague,50.08804,14.42076,1,whoisGeofeedUrls

请优先修正数据库连接问题，然后依次修改问题。

prompt4
帮我查看目前的数据库架构，是否有需要优化的地方
