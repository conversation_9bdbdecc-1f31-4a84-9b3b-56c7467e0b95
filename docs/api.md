# ipInsight API 接口文档

## 📋 API 概览

ipInsight 是一个高性能的 IP 地理位置查询服务，提供多数据源聚合、智能缓存和实时查询功能。

### 基础信息

- **服务名称**: ipInsight
- **版本**: v1.0.0
- **基础URL**: `http://localhost:8080`
- **API版本**: v1
- **协议**: HTTP/HTTPS
- **数据格式**: JSON

### 认证方式

ipInsight 支持多种认证方式：

1. **JWT Token** (推荐用于Web界面)
   ```
   Authorization: Bearer <jwt_token>
   ```

2. **API Key** (推荐用于API访问)
   ```
   X-API-Key: <api_key>
   ```
   或作为查询参数：
   ```
   ?api_key=<api_key>
   ```

3. **配置文件认证** (向后兼容)

### 限流策略

- **频率限制**: 10 requests/second
- **突发限制**: 20 requests
- **算法**: Token Bucket (令牌桶)

### 错误响应格式

所有错误响应都遵循统一格式：

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details (optional)"
  },
  "trace_id": "unique-trace-id"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_IP` | 400 | IP地址格式无效 |
| `IP_NOT_FOUND` | 404 | IP信息未找到 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `UNAUTHORIZED` | 401 | 认证失败 |
| `FORBIDDEN` | 403 | 权限不足 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `DATABASE_ERROR` | 500 | 数据库操作失败 |
| `API_ERROR` | 502 | 第三方API调用失败 |

---

## 🔍 IP 查询接口

### 1. 单个IP查询

查询单个IP地址的地理位置信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/ip/{ip}`
- **认证**: 无需认证
- **限流**: 适用

**路径参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `ip` | string | 是 | 要查询的IP地址 (支持IPv4和IPv6) |

**请求示例**

```bash
# IPv4 查询
curl "http://localhost:8080/api/v1/ip/*******"

# IPv6 查询
curl "http://localhost:8080/api/v1/ip/2001:4860:4860::8888"
```

**响应示例**

```json
{
  "ip_range": {
    "cidr": "*******/32",
    "start_ip": "*******",
    "end_ip": "*******",
    "ip_version": "IPv4"
  },
  "geolocation": {
    "continent": {
      "code": "NA",
      "name": "North America"
    },
    "country": {
      "code": "US",
      "name": "United States",
      "is_in_european_union": false,
      "geoname_id": 6252001
    },
    "region": {
      "code": "CA",
      "name": "California",
      "geoname_id": 5332921
    },
    "city": "Mountain View",
    "postal_code": "94043",
    "latitude": 37.4056,
    "longitude": -122.0775,
    "accuracy_radius": 1000,
    "geoname_id": 5375480
  },
  "network": {
    "isp": "Google LLC",
    "asn": "AS15169",
    "organization": "Google LLC",
    "domain": "google.com",
    "autonomous_system_id": 15169,
    "connection_type": "Corporate",
    "usage_type": "Commercial",
    "hosting_provider": "Google Cloud",
    "cloud_provider": "Google Cloud Platform",
    "is_business_ip": true,
    "is_residential_ip": false,
    "network_speed": "Broadband"
  },
  "timezone": {
    "name": "America/Los_Angeles",
    "offset": "-08:00",
    "dst_offset": "-07:00",
    "is_dst": false
  },
  "security": {
    "is_proxy": false,
    "is_vpn": false,
    "is_tor": false,
    "is_anonymous": false,
    "proxy_type": "",
    "threat_level": "Low",
    "threat_score": 0,
    "threat_types": [],
    "is_malicious": false,
    "is_bot": false,
    "reputation_score": 95,
    "risk_level": "Low",
    "is_blacklisted": false
  },
  "metadata": {
    "source": "maxmind",
    "last_updated": "2024-01-15T10:30:00Z",
    "confidence": 95,
    "data_sources": ["maxmind", "ipinfo"],
    "fusion_method": "priority_merge",
    "query_time": "2024-01-15T15:45:30Z"
  },
  "extended": {
    "currency": {
      "code": "USD",
      "name": "US Dollar"
    },
    "languages": ["en"],
    "calling_code": "+1",
    "flag": "🇺🇸",
    "population": 74066,
    "custom_fields": {}
  }
}
```

**错误响应示例**

```json
{
  "error": {
    "code": "INVALID_IP",
    "message": "Invalid IP address format",
    "details": "Invalid IP address format: invalid-ip"
  },
  "trace_id": "abc123-def456-789"
}
```

### 2. 批量IP查询

批量查询多个IP地址的地理位置信息。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/ip/batch`
- **认证**: 无需认证
- **限流**: 适用

**请求体**

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `ips` | array[string] | 是 | IP地址列表 (最多100个) |

**请求示例**

```bash
curl -X POST "http://localhost:8080/api/v1/ip/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "ips": ["*******", "*******", "***************"]
  }'
```

**响应示例**

```json
{
  "*******": {
    "ip_range": {
      "cidr": "*******/32",
      "start_ip": "*******",
      "end_ip": "*******",
      "ip_version": "IPv4"
    },
    "geolocation": {
      "country": {
        "code": "US",
        "name": "United States"
      },
      "city": "Mountain View"
    }
  },
  "*******": {
    "ip_range": {
      "cidr": "*******/32",
      "start_ip": "*******",
      "end_ip": "*******",
      "ip_version": "IPv4"
    },
    "geolocation": {
      "country": {
        "code": "US",
        "name": "United States"
      },
      "city": "San Francisco"
    }
  },
  "***************": {
    "error": "No data found"
  }
}
```

**验证规则**

- IP列表不能为空
- 最多支持100个IP地址
- 每个IP地址必须是有效的IPv4或IPv6格式
- 无效的IP会在响应中返回错误信息

---

## 🔐 认证接口

### 1. 用户登录

获取JWT访问令牌。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/auth/login`
- **认证**: 无需认证
- **限流**: 适用

**请求体**

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `username` | string | 是 | 用户名 |
| `password` | string | 是 | 密码 |

**请求示例**

```bash
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

**响应示例**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_at": **********
}
```

**错误响应示例**

```json
{
  "error": {
    "code": "AUTH_ERROR",
    "message": "Invalid username or password"
  },
  "trace_id": "abc123-def456-789"
}
```

---

## 🛠 管理接口

> **注意**: 以下所有管理接口都需要认证。请在请求头中包含有效的JWT Token或API Key。

### 1. 系统状态

获取系统运行状态信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/status`
- **认证**: 必需
- **权限**: 管理员

**请求示例**

```bash
curl "http://localhost:8080/api/v1/admin/status" \
  -H "Authorization: Bearer <jwt_token>"
```

**响应示例**

```json
{
  "timestamp": **********,
  "status": {
    "status": "ok",
    "timestamp": **********,
    "user": "admin",
    "auth_method": "jwt",
    "version": "1.0.0"
  }
}
```

### 2. 健康检查

获取系统健康状态。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/health`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "health": {
    "database_interface": {
      "service": "database_interface",
      "status": "healthy",
      "message": "Database connection is healthy",
      "timestamp": "2024-01-15T15:45:30Z",
      "duration": 5
    },
    "cache": {
      "service": "cache",
      "status": "healthy",
      "message": "Cache is operational",
      "timestamp": "2024-01-15T15:45:30Z",
      "duration": 2
    }
  }
}
```

### 3. 查询统计

获取IP查询统计信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/stats`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "stats": {
    "cache_hits": 1250,
    "database_hits": 380,
    "api_fallbacks": 45,
    "total_queries": 1675,
    "errors": 12,
    "cache_hit_rate": 74.6,
    "db_hit_rate": 22.7
  }
}
```

### 4. 热门IP列表

获取访问频率最高的IP地址列表。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/hot-ips`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "hot_ips": [
    {
      "ip": "*******",
      "count": 156,
      "last_query": **********,
      "country": "United States",
      "city": "Mountain View"
    },
    {
      "ip": "*******",
      "count": 89,
      "last_query": **********,
      "country": "United States",
      "city": "San Francisco"
    }
  ]
}
```

### 5. 热门IP统计

获取热门IP的统计分析信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/hot-ip-stats`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "stats": {
    "total_unique_ips": 2847,
    "top_countries": [
      {
        "country": "United States",
        "count": 1245,
        "percentage": 43.7
      },
      {
        "country": "China",
        "count": 567,
        "percentage": 19.9
      }
    ],
    "top_cities": [
      {
        "city": "Mountain View",
        "country": "United States",
        "count": 234,
        "percentage": 8.2
      }
    ],
    "query_distribution": [
      {
        "hour": 14,
        "count": 145
      },
      {
        "hour": 15,
        "count": 189
      }
    ]
  }
}
```

### 6. 批量操作统计

获取批量查询操作的统计信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/batch-stats`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "stats": {
    "total_batches": 45,
    "total_ips_processed": 1234,
    "avg_batch_size": 27.4,
    "avg_processing_time": 156.7,
    "success_rate": 98.5
  }
}
```

### 7. 监控指标

获取系统监控指标。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/metrics`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "metrics": {
    "system": {
      "cpu_usage": 45.2,
      "memory_usage": 67.8,
      "disk_usage": 23.4,
      "load_average": [1.2, 1.5, 1.8],
      "uptime": 86400,
      "timestamp": "2024-01-15T15:45:30Z"
    },
    "service": {
      "total_requests": 15678,
      "requests_per_second": 12.5,
      "avg_response_time": 145.6,
      "error_rate": 0.8,
      "active_connections": 45,
      "timestamp": "2024-01-15T15:45:30Z"
    },
    "database": {
      "total_connections": 10,
      "active_connections": 3,
      "query_count": 8945,
      "avg_query_time": 23.4,
      "slow_queries": 12,
      "timestamp": "2024-01-15T15:45:30Z"
    },
    "cache": {
      "total_requests": 12456,
      "cache_hits": 9234,
      "cache_misses": 3222,
      "hit_rate": 74.1,
      "memory_usage": 256.7,
      "timestamp": "2024-01-15T15:45:30Z"
    },
    "api": {
      "endpoint_stats": {
        "/api/v1/ip": {
          "total_requests": 8945,
          "avg_response_time": 123.4,
          "error_count": 45,
          "last_request": "2024-01-15T15:45:30Z"
        }
      },
      "timestamp": "2024-01-15T15:45:30Z"
    },
    "last_update": **********
  }
}
```

### 8. 监控摘要

获取系统监控摘要信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/summary`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "summary": {
    "service_health": "healthy",
    "total_requests_24h": 45678,
    "error_rate_24h": 0.8,
    "avg_response_time_24h": 145.6,
    "cache_hit_rate_24h": 74.1,
    "active_data_sources": 8,
    "last_data_update": **********,
    "system_load": {
      "cpu": 45.2,
      "memory": 67.8,
      "disk": 23.4
    },
    "alerts": [
      {
        "id": "alert-001",
        "type": "warning",
        "message": "High memory usage detected",
        "timestamp": **********,
        "resolved": false
      }
    ]
  }
}
```

---

## 📊 数据源管理接口

### 1. 获取数据源状态

获取所有数据源的状态信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/datasources`
- **认证**: 必需

**响应示例**

```json
{
  "timestamp": **********,
  "datasources": [
    {
      "name": "maxmind",
      "enabled": true,
      "last_update": **********,
      "next_update": **********,
      "status": "active",
      "records_count": 4567890
    },
    {
      "name": "ip2location",
      "enabled": true,
      "last_update": **********,
      "next_update": **********,
      "status": "active",
      "records_count": 3456789
    },
    {
      "name": "dbip",
      "enabled": false,
      "last_update": 0,
      "next_update": 0,
      "status": "inactive",
      "error": "Configuration missing"
    }
  ]
}
```

### 2. 手动更新数据源

手动触发数据源更新。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/admin/fetch`
- **认证**: 必需

**请求体**

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `sources` | array[string] | 否 | 要更新的数据源列表，为空则更新所有 |
| `force` | boolean | 否 | 是否强制下载，默认false |

**请求示例**

```bash
# 更新所有数据源
curl -X POST "http://localhost:8080/api/v1/admin/fetch" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "sources": [],
    "force": false
  }'

# 更新指定数据源
curl -X POST "http://localhost:8080/api/v1/admin/fetch" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "sources": ["maxmind", "ip2location"],
    "force": true
  }'
```

**响应示例**

```json
{
  "status": "success",
  "updated_sources": [
    {
      "source": "maxmind",
      "status": "success",
      "records_processed": 4567890,
      "processing_time": 45.6,
      "message": "Update completed successfully"
    },
    {
      "source": "ip2location",
      "status": "success",
      "records_processed": 3456789,
      "processing_time": 38.2,
      "message": "Update completed successfully"
    }
  ],
  "force": true
}
```

### 3. 验证数据源配置

验证数据源配置的有效性。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/admin/validate`
- **认证**: 必需

**请求体**

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `sources` | array[string] | 否 | 要验证的数据源列表，为空则验证所有 |

**请求示例**

```bash
curl -X POST "http://localhost:8080/api/v1/admin/validate" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "sources": ["maxmind", "ip2location"]
  }'
```

**响应示例**

```json
{
  "status": "success",
  "total": 2,
  "valid": 1,
  "invalid": 1,
  "results": [
    {
      "source": "maxmind",
      "valid": true,
      "message": "Configuration is valid"
    },
    {
      "source": "ip2location",
      "valid": false,
      "message": "API key is missing or invalid",
      "details": "Please check your IP2Location API key configuration"
    }
  ],
  "validated_at": "2024-01-15T15:45:30Z"
}
```

---

## 🔧 IP补全服务接口

> **注意**: IP补全服务是可选功能，需要在配置中启用。

### 1. 启动IP补全

启动IP地理信息补全服务。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/admin/ip-completion/start`
- **认证**: 必需

**响应示例**

```json
{
  "status": "success",
  "message": "IP completion service started",
  "started_at": "2024-01-15T15:45:30Z"
}
```

### 2. 停止IP补全

停止IP地理信息补全服务。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/admin/ip-completion/stop`
- **认证**: 必需

**响应示例**

```json
{
  "status": "success",
  "message": "IP completion service stopped",
  "stopped_at": "2024-01-15T15:45:30Z"
}
```

### 3. 获取补全状态

获取IP补全服务的运行状态。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/ip-completion/status`
- **认证**: 必需

**响应示例**

```json
{
  "status": "running",
  "started_at": "2024-01-15T15:45:30Z",
  "processed_count": 12456,
  "success_count": 11890,
  "error_count": 566,
  "current_batch": 125,
  "total_batches": 500,
  "progress": 25.0,
  "estimated_completion": "2024-01-15T18:30:00Z"
}
```

### 4. 获取代理列表

获取IP补全服务使用的代理列表。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/ip-completion/proxies`
- **认证**: 必需

**响应示例**

```json
{
  "proxies": [
    {
      "url": "http://proxy1.example.com:8080",
      "status": "active",
      "success_rate": 95.2,
      "last_used": "2024-01-15T15:45:30Z"
    },
    {
      "url": "http://proxy2.example.com:8080",
      "status": "inactive",
      "success_rate": 0,
      "last_used": null,
      "error": "Connection timeout"
    }
  ],
  "total": 2,
  "active": 1
}
```

### 5. 重新加载代理

重新加载代理配置。

**接口信息**
- **方法**: `POST`
- **路径**: `/api/v1/admin/ip-completion/proxies/reload`
- **认证**: 必需

**响应示例**

```json
{
  "status": "success",
  "message": "Proxy configuration reloaded",
  "loaded_proxies": 5,
  "reloaded_at": "2024-01-15T15:45:30Z"
}
```

### 6. 获取数据库统计

获取IP补全相关的数据库统计信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/admin/ip-completion/database/stats`
- **认证**: 必需

**响应示例**

```json
{
  "total_ips": 4567890,
  "completed_ips": 3456789,
  "pending_ips": 1111101,
  "completion_rate": 75.7,
  "last_completion": "2024-01-15T15:45:30Z",
  "avg_completion_time": 0.25
}
```

---

## 🌐 公共接口

### 1. Prometheus指标

获取Prometheus格式的监控指标。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/v1/metrics`
- **认证**: 无需认证
- **响应格式**: Prometheus文本格式

**请求示例**

```bash
curl "http://localhost:8080/api/v1/metrics"
```

**响应示例**

```
# HELP ipinsight_api_requests_total Total number of API requests
# TYPE ipinsight_api_requests_total counter
ipinsight_api_requests_total{endpoint="/api/v1/ip",status="200"} 1234
ipinsight_api_requests_total{endpoint="/api/v1/ip",status="404"} 56

# HELP ipinsight_api_latency_seconds API request latency in seconds
# TYPE ipinsight_api_latency_seconds histogram
ipinsight_api_latency_seconds_bucket{endpoint="/api/v1/ip",le="0.1"} 800
ipinsight_api_latency_seconds_bucket{endpoint="/api/v1/ip",le="0.5"} 1200
ipinsight_api_latency_seconds_bucket{endpoint="/api/v1/ip",le="+Inf"} 1234
```

### 2. 服务根路径

获取服务基本信息和快速导航。

**接口信息**
- **方法**: `GET`
- **路径**: `/`
- **认证**: 无需认证

**响应示例**

```json
{
  "service": "ipInsight",
  "version": "1.0.0",
  "description": "High-performance IP geolocation service with multi-source data aggregation",
  "timestamp": **********,
  "health": {
    "database_interface": {
      "service": "database_interface",
      "status": "healthy"
    }
  },
  "stats": {
    "total_queries": 1675,
    "cache_hit_rate": 74.6
  },
  "endpoints": {
    "dashboard": "/dashboard",
    "health": "/health",
    "api_docs": "/docs",
    "query_single": "/api/v1/ip/{ip}",
    "query_batch": "/api/v1/ip/batch",
    "admin_login": "/api/v1/auth/login",
    "admin_status": "/api/v1/admin/status",
    "admin_fetch": "/api/v1/admin/fetch",
    "metrics": "/api/v1/metrics"
  },
  "examples": {
    "single_query": "curl http://localhost:8080/api/v1/ip/*******",
    "batch_query": "curl -X POST http://localhost:8080/api/v1/ip/batch -H \"Content-Type: application/json\" -d '{\"ips\":[\"*******\",\"*******\"]}'",
    "health_check": "curl http://localhost:8080/health"
  },
  "documentation": {
    "github": "https://github.com/cosin2077/ipInsight",
    "api_docs": "http://localhost:8080/docs",
    "dashboard": "http://localhost:8080/dashboard"
  }
}
```

### 3. API文档页面

获取API文档信息（JSON格式）。

**接口信息**
- **方法**: `GET`
- **路径**: `/docs`
- **认证**: 无需认证

**响应示例**

```json
{
  "title": "ipInsight API Documentation",
  "version": "1.0.0",
  "base_url": "http://localhost:8080",
  "endpoints": [
    {
      "method": "GET",
      "path": "/api/v1/ip/{ip}",
      "description": "Query IP information for a single IP address",
      "parameters": {
        "ip": "IP address to query (IPv4 or IPv6)"
      },
      "example": "curl http://localhost:8080/api/v1/ip/*******"
    }
  ]
}
```

---

## 🎨 前端静态文件服务

### 1. 管理看板

访问Web管理界面。

**接口信息**
- **方法**: `GET`
- **路径**: `/dashboard`
- **认证**: 无需认证（但看板内功能需要登录）

**功能特性**
- IP查询测试工具
- 系统状态监控
- 数据源管理
- 用户认证界面
- 实时统计图表

### 2. 静态资源

**CSS/JS资源**
- **路径**: `/assets/*`
- **内容**: 前端应用的CSS、JavaScript文件

**图标文件**
- **路径**: `/favicon.ico`
- **内容**: 网站图标

---

## 📋 数据模型说明

### IPInfo 完整数据结构

```json
{
  "ip_range": {
    "cidr": "string",           // CIDR表示法
    "start_ip": "string",       // 起始IP
    "end_ip": "string",         // 结束IP
    "ip_version": "string",     // IP版本 (IPv4/IPv6)
    "total_ips": "number"       // IP总数
  },
  "geolocation": {
    "continent": {
      "code": "string",         // 大陆代码
      "name": "string"          // 大陆名称
    },
    "country": {
      "code": "string",         // 国家代码 (ISO 3166-1)
      "name": "string",         // 国家名称
      "is_in_european_union": "boolean",
      "geoname_id": "number"    // GeoNames ID
    },
    "region": {
      "code": "string",         // 地区代码
      "name": "string",         // 地区名称
      "geoname_id": "number"
    },
    "city": "string",           // 城市名称
    "postal_code": "string",    // 邮政编码
    "latitude": "number",       // 纬度
    "longitude": "number",      // 经度
    "accuracy_radius": "number", // 精度半径(km)
    "metro_code": "string",     // 都市区代码
    "geoname_id": "number"      // 城市GeoNames ID
  },
  "network": {
    "isp": "string",            // 互联网服务提供商
    "asn": "string",            // 自治系统编号
    "organization": "string",   // 组织名称
    "domain": "string",         // 域名
    "autonomous_system_id": "number",
    "connection_type": "string", // 连接类型
    "usage_type": "string",     // 使用类型
    "mcc": "string",            // 移动国家代码
    "mnc": "string",            // 移动网络代码
    "carrier": "string",        // 运营商
    "hosting_provider": "string", // 托管提供商
    "cloud_provider": "string", // 云服务商
    "is_business_ip": "boolean",
    "is_residential_ip": "boolean",
    "is_education_ip": "boolean",
    "is_government_ip": "boolean",
    "is_mobile_ip": "boolean",
    "network_speed": "string"   // 网络速度等级
  },
  "timezone": {
    "name": "string",           // 时区名称
    "offset": "string",         // UTC偏移
    "dst_offset": "string",     // 夏令时偏移
    "is_dst": "boolean"         // 是否夏令时
  },
  "security": {
    "is_proxy": "boolean",      // 是否代理
    "is_vpn": "boolean",        // 是否VPN
    "is_tor": "boolean",        // 是否Tor
    "is_anonymous": "boolean",  // 是否匿名
    "proxy_type": "string",     // 代理类型
    "threat_level": "string",   // 威胁等级
    "threat_score": "number",   // 威胁分数 (0-100)
    "threat_types": ["string"], // 威胁类型列表
    "is_malicious": "boolean",  // 是否恶意
    "is_bot": "boolean",        // 是否机器人
    "is_spammer": "boolean",    // 是否垃圾邮件发送者
    "is_attacker": "boolean",   // 是否攻击者
    "is_scanner": "boolean",    // 是否扫描器
    "last_seen": "string",      // 最后发现时间
    "is_residential_proxy": "boolean",
    "is_mobile_proxy": "boolean",
    "reputation_score": "number", // 信誉分数 (0-100)
    "risk_level": "string",     // 风险等级
    "is_blacklisted": "boolean",
    "blacklist_sources": ["string"]
  },
  "metadata": {
    "source": "string",         // 主要数据源
    "last_updated": "string",   // 最后更新时间 (RFC3339)
    "confidence": "number",     // 置信度 (0-100)
    "data_sources": ["string"], // 所有数据源
    "fusion_method": "string",  // 融合方法
    "query_time": "string",     // 查询时间
    "cache_hit": "boolean",     // 是否缓存命中
    "processing_time": "number" // 处理时间(ms)
  },
  "extended": {
    "currency": {
      "code": "string",         // 货币代码
      "name": "string"          // 货币名称
    },
    "languages": ["string"],    // 语言列表
    "calling_code": "string",   // 国际电话代码
    "flag": "string",           // 国旗emoji
    "population": "number",     // 人口数量
    "weather_code": "string",   // 天气代码
    "market_segment": "string", // 市场细分
    "custom_fields": {}         // 自定义字段
  }
}
```

---

## 🚀 使用示例

### JavaScript/Node.js

```javascript
// 单个IP查询
async function queryIP(ip) {
  const response = await fetch(`http://localhost:8080/api/v1/ip/${ip}`);
  const data = await response.json();
  return data;
}

// 批量IP查询
async function batchQueryIPs(ips) {
  const response = await fetch('http://localhost:8080/api/v1/ip/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ ips })
  });
  const data = await response.json();
  return data;
}

// 管理接口调用（需要认证）
async function getSystemStatus(token) {
  const response = await fetch('http://localhost:8080/api/v1/admin/status', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  const data = await response.json();
  return data;
}
```

### Python

```python
import requests

# 单个IP查询
def query_ip(ip):
    response = requests.get(f'http://localhost:8080/api/v1/ip/{ip}')
    return response.json()

# 批量IP查询
def batch_query_ips(ips):
    response = requests.post(
        'http://localhost:8080/api/v1/ip/batch',
        json={'ips': ips}
    )
    return response.json()

# 用户登录
def login(username, password):
    response = requests.post(
        'http://localhost:8080/api/v1/auth/login',
        json={'username': username, 'password': password}
    )
    return response.json()

# 管理接口调用
def get_system_status(token):
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(
        'http://localhost:8080/api/v1/admin/status',
        headers=headers
    )
    return response.json()
```

### cURL

```bash
# 单个IP查询
curl "http://localhost:8080/api/v1/ip/*******"

# 批量IP查询
curl -X POST "http://localhost:8080/api/v1/ip/batch" \
  -H "Content-Type: application/json" \
  -d '{"ips":["*******","*******"]}'

# 用户登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 获取系统状态（需要token）
curl "http://localhost:8080/api/v1/admin/status" \
  -H "Authorization: Bearer <your_jwt_token>"

# 手动更新数据源
curl -X POST "http://localhost:8080/api/v1/admin/fetch" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"sources":["maxmind"],"force":true}'
```

---

## 📞 技术支持

- **项目地址**: https://github.com/cosin2077/ipInsight
- **问题反馈**: GitHub Issues
- **文档更新**: 2024-01-15
- **API版本**: v1.0.0

---

*本文档基于 ipInsight v1.0.0 生成，如有疑问请参考源码或提交Issue。*
```
```
```
```
```
```