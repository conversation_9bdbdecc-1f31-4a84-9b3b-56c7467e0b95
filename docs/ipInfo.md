## 现在有个API能获取某个IP或者CIDR的信息，详细示例如下：
请求API：
<api>
https://ipapi.co/{ip}/json
</api>
得到返回结果：
<response>
{
    "ip": "*************",
    "network": "***********/24",
    "version": "IPv4",
    "city": "Serpong",
    "region": "West Java",
    "region_code": "JB",
    "country": "ID",
    "country_name": "Indonesia",
    "country_code": "ID",
    "country_code_iso3": "IDN",
    "country_capital": "Jakarta",
    "country_tld": ".id",
    "continent_code": "AS",
    "in_eu": false,
    "postal": null,
    "latitude": -6.31694,
    "longitude": 106.66417,
    "timezone": "Asia/Jakarta",
    "utc_offset": "+0700",
    "country_calling_code": "+62",
    "currency": "IDR",
    "currency_name": "Rupiah",
    "languages": "id,en,nl,jv",
    "country_area": 1919440.0,
    "country_population": 267663435,
    "asn": "AS137342",
    "org": "DISKOMINFO TANGERANG SELATAN"
}
</response>
返回结果如果包含network CIDR范围，那么这个范围的IP应该都可以通用这个response信息。
response中字段可能和本项目定义的model不一致，请仔细检查对应字段。
当前项目数据库中，应该有很多IP信息缺失的行，请帮我实现功能：
1. 通过调用API，补全这些IP或者CIDR的各种信息。
2. 提供代理功能，假设代理信息在 /proxy/proxy.txt 内容例子如下：
```
http://***********:59771
https://*************:80
socks4://*************:8008
socks5://*************:3128
```
3. API调用可能会有类似429之类的报错，如果报错了，那么请轮换代理，直到所有代理都不可用则报错退出程序。
4. 注意合理处理CIDR，IP有交叉的情况。
5. 用你认为合理，最佳的方式实现，可以是通过xxxx 命令，或者是通过API端口触发。
6. 你实现的逻辑应该尽可能独立。
7. 不应该影响现有代码逻辑。
8. 查看当前数据库表结构、字段、分析出最佳策略。