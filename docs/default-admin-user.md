# 默认管理员用户配置

ipInsight 支持在系统启动时自动创建默认管理员用户，这对于初始部署和自动化部署场景非常有用。

## 配置说明

在 `config/config.yaml` 文件中的 `auth` 部分添加 `default_admin` 配置：

```yaml
auth:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
  api_keys:
    - "admin-api-key-123456"
    - "backup-api-key-789012"
  token_ttl: 24  # JWT token TTL in hours
  admin_users:
    - "admin"
    - "operator"
  # 默认管理员用户配置 - 系统启动时自动创建
  default_admin:
    enabled: true  # 是否启用默认管理员用户创建
    username: "admin"
    password: "admin123"  # 请在生产环境中修改此密码
    email: "<EMAIL>"
    full_name: "系统管理员"
```

## 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `enabled` | boolean | 是 | 是否启用默认管理员用户创建功能 |
| `username` | string | 是 | 默认管理员用户名 |
| `password` | string | 是 | 默认管理员密码 |
| `email` | string | 否 | 默认管理员邮箱，如果不设置将使用 `{username}@ipinsight.local` |
| `full_name` | string | 否 | 默认管理员全名，如果不设置将使用 "默认管理员" |

## 工作原理

1. **系统启动检查**：每次系统启动时，如果 `default_admin.enabled` 为 `true`，系统会检查配置的用户名是否已存在
2. **用户创建**：如果用户不存在，系统会自动创建一个具有管理员权限的用户
3. **跳过创建**：如果用户已存在，系统会跳过创建过程并记录日志
4. **安全提醒**：如果使用默认密码 "admin123"，系统会输出安全警告

## 安全建议

⚠️ **重要安全提醒**：

1. **修改默认密码**：请务必在生产环境中修改默认密码
2. **禁用功能**：在生产环境中，建议在首次创建管理员用户后将 `enabled` 设置为 `false`
3. **强密码策略**：使用包含大小写字母、数字和特殊字符的强密码
4. **定期更换**：定期更换管理员密码

## 使用场景

### 1. 初始部署
```yaml
default_admin:
  enabled: true
  username: "admin"
  password: "your-secure-password-here"
  email: "<EMAIL>"
  full_name: "系统管理员"
```

### 2. Docker 容器部署
```yaml
default_admin:
  enabled: true
  username: "${ADMIN_USERNAME:-admin}"
  password: "${ADMIN_PASSWORD:-admin123}"
  email: "${ADMIN_EMAIL:-<EMAIL>}"
  full_name: "${ADMIN_FULLNAME:-系统管理员}"
```

### 3. 生产环境（推荐）
```yaml
default_admin:
  enabled: false  # 生产环境中禁用自动创建
```

## 日志输出

系统会记录以下相关日志：

- **功能禁用**：`Default admin user creation is disabled`
- **配置不完整**：`Default admin user configuration is incomplete, skipping creation`
- **用户已存在**：`Default admin user already exists, skipping creation`
- **创建成功**：`Default admin user created successfully`
- **安全警告**：`SECURITY WARNING: Default admin user is using the default password`

## 故障排除

### 问题：用户创建失败
**可能原因**：
- 数据库连接问题
- 用户名或邮箱格式不正确
- 数据库权限不足

**解决方案**：
1. 检查数据库连接状态
2. 验证配置文件格式
3. 查看系统日志获取详细错误信息

### 问题：用户已存在但无法登录
**可能原因**：
- 用户状态为非活跃状态
- 密码不匹配

**解决方案**：
1. 检查数据库中用户状态
2. 通过数据库直接重置密码
3. 使用其他管理员账户重置该用户

## 相关命令

```bash
# 查看系统启动日志
tail -f logs/ipinsight.log | grep "default admin"

# 检查用户是否创建成功
psql -d ipinsight -c "SELECT username, email, role, status FROM users WHERE username='admin';"
```
