# IP数据库架构设计

## 概述

本文档定义了ipInsight项目的数据库架构设计，将当前的单JSONB字段存储优化为规范化的表结构，以提升查询性能和数据管理效率。

## 设计原则

1. **混合存储策略**：高频查询字段规范化存储，低频字段JSON存储
2. **查询性能优化**：为常用查询字段建立索引
3. **数据完整性**：保持与现有Go模型的兼容性
4. **扩展性**：支持未来新增字段和数据源

## 核心表结构

### 1. 主表：ip_ranges

```sql
CREATE TABLE ip_ranges (
    -- 主键和基础信息
    id BIGSERIAL PRIMARY KEY,
    ip_range CIDR NOT NULL UNIQUE,
    start_ip_int BIGINT NOT NULL,
    end_ip_int BIGINT NOT NULL,
    ip_version SMALLINT NOT NULL CHECK (ip_version IN (4, 6)),
    prefix_len SMALLINT,
    
    -- 网络特征
    is_anycast BOOLEAN DEFAULT FALSE,
    is_multicast BOOLEAN DEFAULT FALSE,
    
    -- 地理位置信息（高频查询字段）
    continent_code VARCHAR(2),
    continent_name VARCHAR(50),
    country_code VARCHAR(2),
    country_name VARCHAR(100),
    region_code VARCHAR(10),
    region_name VARCHAR(100),
    city VARCHAR(100),
    postal_code VARCHAR(20),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    accuracy_radius INTEGER,
    geoname_id INTEGER,
    is_in_european_union BOOLEAN DEFAULT FALSE,
    
    -- 网络信息（高频查询字段）
    isp VARCHAR(200),
    asn VARCHAR(20),
    organization VARCHAR(200),
    domain VARCHAR(100),
    connection_type VARCHAR(50),
    usage_type VARCHAR(50),
    carrier VARCHAR(100),
    hosting_provider VARCHAR(200),
    cloud_provider VARCHAR(100),
    
    -- 网络特征标识
    is_business_ip BOOLEAN DEFAULT FALSE,
    is_residential_ip BOOLEAN DEFAULT FALSE,
    is_education_ip BOOLEAN DEFAULT FALSE,
    is_government_ip BOOLEAN DEFAULT FALSE,
    is_mobile_ip BOOLEAN DEFAULT FALSE,
    network_speed VARCHAR(50),
    
    -- 安全信息（高频查询字段）
    is_proxy BOOLEAN DEFAULT FALSE,
    proxy_type VARCHAR(50),
    proxy_level VARCHAR(20),
    is_vpn BOOLEAN DEFAULT FALSE,
    vpn_provider VARCHAR(100),
    is_tor BOOLEAN DEFAULT FALSE,
    is_anonymizer BOOLEAN DEFAULT FALSE,
    is_data_center BOOLEAN DEFAULT FALSE,
    is_hosting BOOLEAN DEFAULT FALSE,
    is_cloud_ip BOOLEAN DEFAULT FALSE,
    
    -- 威胁情报
    threat_level VARCHAR(20),
    threat_score SMALLINT CHECK (threat_score >= 0 AND threat_score <= 100),
    is_malicious BOOLEAN DEFAULT FALSE,
    is_bot BOOLEAN DEFAULT FALSE,
    is_spammer BOOLEAN DEFAULT FALSE,
    is_attacker BOOLEAN DEFAULT FALSE,
    is_scanner BOOLEAN DEFAULT FALSE,
    is_residential_proxy BOOLEAN DEFAULT FALSE,
    is_mobile_proxy BOOLEAN DEFAULT FALSE,
    reputation_score SMALLINT CHECK (reputation_score >= 0 AND reputation_score <= 100),
    risk_level VARCHAR(20),
    is_blacklisted BOOLEAN DEFAULT FALSE,
    last_seen TIMESTAMP WITH TIME ZONE,
    
    -- 时区信息
    timezone_name VARCHAR(100),
    timezone_offset VARCHAR(10),
    
    -- 元数据
    source VARCHAR(50) NOT NULL,
    source_version VARCHAR(50),
    confidence SMALLINT CHECK (confidence >= 0 AND confidence <= 100),
    accuracy VARCHAR(20),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 扩展信息（低频字段JSON存储）
    geolocation_extended JSONB,
    network_extended JSONB,
    security_extended JSONB,
    extended_data JSONB,
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. 关联表：ip_subdivisions（多级行政区划）

```sql
CREATE TABLE ip_subdivisions (
    id BIGSERIAL PRIMARY KEY,
    ip_range_id BIGINT REFERENCES ip_ranges(id) ON DELETE CASCADE,
    level SMALLINT NOT NULL CHECK (level >= 1 AND level <= 5),
    code VARCHAR(10),
    name VARCHAR(100),
    geoname_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. 关联表：ip_threat_types（威胁类型）

```sql
CREATE TABLE ip_threat_types (
    id BIGSERIAL PRIMARY KEY,
    ip_range_id BIGINT REFERENCES ip_ranges(id) ON DELETE CASCADE,
    threat_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_range_id, threat_type)
);
```

### 4. 关联表：ip_blacklist_sources（黑名单来源）

```sql
CREATE TABLE ip_blacklist_sources (
    id BIGSERIAL PRIMARY KEY,
    ip_range_id BIGINT REFERENCES ip_ranges(id) ON DELETE CASCADE,
    source_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_range_id, source_name)
);
```

### 5. 关联表：ip_languages（语言列表）

```sql
CREATE TABLE ip_languages (
    id BIGSERIAL PRIMARY KEY,
    ip_range_id BIGINT REFERENCES ip_ranges(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(ip_range_id, language_code)
);
```

## 索引设计

### 主要查询索引

```sql
-- IP范围查询（最重要）
CREATE INDEX ip_ranges_range_idx ON ip_ranges USING GIST (ip_range);
CREATE INDEX ip_ranges_int_range_idx ON ip_ranges (start_ip_int, end_ip_int);

-- 地理位置查询
CREATE INDEX ip_ranges_country_idx ON ip_ranges (country_code);
CREATE INDEX ip_ranges_city_idx ON ip_ranges (city);
CREATE INDEX ip_ranges_location_idx ON ip_ranges (latitude, longitude);

-- 网络信息查询
CREATE INDEX ip_ranges_isp_idx ON ip_ranges (isp);
CREATE INDEX ip_ranges_asn_idx ON ip_ranges (asn);
CREATE INDEX ip_ranges_connection_type_idx ON ip_ranges (connection_type);
CREATE INDEX ip_ranges_usage_type_idx ON ip_ranges (usage_type);

-- 安全信息查询
CREATE INDEX ip_ranges_security_idx ON ip_ranges (is_proxy, is_vpn, is_tor);
CREATE INDEX ip_ranges_threat_idx ON ip_ranges (threat_level, threat_score);
CREATE INDEX ip_ranges_reputation_idx ON ip_ranges (reputation_score);

-- 数据源和时间查询
CREATE INDEX ip_ranges_source_idx ON ip_ranges (source);
CREATE INDEX ip_ranges_updated_idx ON ip_ranges (last_updated);

-- 复合索引（常用组合查询）
CREATE INDEX ip_ranges_country_city_idx ON ip_ranges (country_code, city);
CREATE INDEX ip_ranges_security_combo_idx ON ip_ranges (is_proxy, is_vpn, threat_level);
```

### JSONB字段索引

```sql
-- 为JSONB字段创建GIN索引
CREATE INDEX ip_ranges_geo_extended_gin_idx ON ip_ranges USING GIN (geolocation_extended);
CREATE INDEX ip_ranges_network_extended_gin_idx ON ip_ranges USING GIN (network_extended);
CREATE INDEX ip_ranges_security_extended_gin_idx ON ip_ranges USING GIN (security_extended);
CREATE INDEX ip_ranges_extended_data_gin_idx ON ip_ranges USING GIN (extended_data);
```

## 分区策略（可选）

对于大规模数据，可以考虑按时间分区：

```sql
-- 按月分区
CREATE TABLE ip_ranges_partitioned (
    LIKE ip_ranges INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 创建分区表
CREATE TABLE ip_ranges_2025_01 PARTITION OF ip_ranges_partitioned
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

## 性能优化建议

1. **连接池配置**：设置合适的连接池参数
2. **批量操作**：使用COPY FROM进行批量插入
3. **定期维护**：定期VACUUM和ANALYZE
4. **监控查询**：监控慢查询并优化

## 与Go模型的映射关系

主表字段直接映射到Go结构体的对应字段，JSONB字段存储嵌套结构：

- `geolocation_extended`: 存储Subdivisions等复杂地理信息
- `network_extended`: 存储MCC、MNC等移动网络详细信息
- `security_extended`: 存储ThreatTypes、BlacklistSources等数组字段
- `extended_data`: 存储Currency、Languages、CustomFields等扩展信息

这种设计既保证了查询性能，又保持了数据的完整性和扩展性。
