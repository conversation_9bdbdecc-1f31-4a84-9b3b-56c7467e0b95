# ipInsight IP 数据库数据源完整更新流程文档

## 📋 概述

本文档详细描述了 ipInsight 项目中所有 IP 数据库数据源的完整更新流程，包括数据获取、解析、融合、入库等各个环节的具体实现。

## 🗂️ 数据源列表

ipInsight 项目目前支持以下 6 种数据源：

| 数据源 | 类型 | 数据格式 | 更新频率 | 认证要求 |
|--------|------|----------|----------|----------|
| **MaxMind** | 商业/免费 | MMDB | 每周日 02:00 | 需要账号和许可证密钥 |
| **IP2Location** | 商业/免费 | CSV/ZIP | 每周日 03:00 | 需要 API Token |
| **DB-IP** | 免费 | MMDB.GZ | 每周日 04:00 | 无需认证 |
| **IPAPI** | 免费 | CSV/ZIP | 每周日 05:00 | 无需认证 |
| **IPLocate** | 免费 | CSV/MMDB | 每周日 06:00 | 无需认证 |
| **QQWry** | 免费 | DAT | 每周日 07:00 | 无需认证 |

## 🔧 数据源注册与初始化

### 代码位置
- **文件**: `internal/app/config.go`
- **函数**: `initializeDatasources()` (第78-92行)

### 初始化代码
```go
func (app *App) initializeDatasources() error {
    app.logger.Info("Initializing data sources...")
    
    app.datasources = []datasource.Datasource{
        maxmind.NewMaxmind(app.config.Datasources.Maxmind, app.logger, app.db),
        ip2location.NewIP2Location(app.config.Datasources.IP2Location, app.logger, app.db),
        dbip.NewDBIP(app.config.Datasources.DBIP, app.logger, app.db),
        ipapi.NewIPAPI(app.config.Datasources.IPAPI, app.logger, app.db),
        iplocate.NewIPLocate(app.config.Datasources.IPLocate, app.logger, app.db),
        qqwry.NewQQWryDatasource(&app.config.Datasources.QQWry, filepath.Join("data", "qqwry"), app.logger, app.db),
    }
    
    return nil
}
```

## 📊 更新流程架构

### 核心组件
1. **UpdateManager** - 更新管理器 (`internal/datasource/update_manager.go`)
2. **Scheduler** - 调度器 (`internal/scheduler/scheduler.go`)
3. **API Handler** - API处理器 (`internal/api/handlers.go`)

### 更新流程步骤
每个数据源的更新都遵循以下5个核心步骤：

1. **数据获取 (Fetch)** - 下载原始数据文件
2. **数据解析 (Parse)** - 解析数据文件为结构化数据
3. **数据融合 (Fusion)** - 多源数据融合和去重
4. **数据入库 (Update)** - 批量写入数据库
5. **状态更新** - 更新数据源状态和统计信息

## 🎯 手动触发更新 API

### API 端点
- **方法**: `POST`
- **路径**: `/api/v1/admin/fetch`
- **认证**: 必需 (JWT Token 或 API Key)

### 代码位置
- **文件**: `internal/api/handlers.go`
- **函数**: `ManualFetchData()` (第237-252行)
- **路由**: `internal/api/routes.go` (第74行)

### 请求格式
```json
{
  "sources": ["maxmind", "ip2location", "dbip"],  // 可选，指定数据源
  "force": true                                   // 可选，强制下载
}
```

### 使用示例
```bash
# 更新所有数据源
curl -X POST "http://localhost:8082/api/v1/admin/fetch" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"sources": [], "force": false}'

# 更新指定数据源（强制下载）
curl -X POST "http://localhost:8082/api/v1/admin/fetch" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"sources": ["maxmind", "dbip"], "force": true}'
```

### 响应格式
```json
{
  "status": "success",
  "updated_sources": {
    "maxmind": 1234567,
    "dbip": 987654
  },
  "force": true
}
```

## 🔄 自动调度机制

### 调度器配置
- **文件**: `internal/scheduler/scheduler.go`
- **函数**: `RegisterDatasources()` (第69-124行)

### 调度时间配置
```yaml
# config/config.yaml
datasources:
  maxmind:
    schedule: "0 2 * * 0"  # 每周日 02:00
  ip2location:
    schedule: "0 3 * * 0"  # 每周日 03:00
  dbip:
    schedule: "0 4 * * 0"  # 每周日 04:00
  ipapi:
    schedule: "0 5 * * 0"  # 每周日 05:00
  iplocate:
    schedule: "0 6 * * 0"  # 每周日 06:00
  qqwry:
    schedule: "0 7 * * 0"  # 每周日 07:00
```

### 启动时自动更新
- **配置**: `config.yaml` 中的 `startup.auto_update_datasources`
- **默认**: `false` (禁用以提升启动速度)
- **代码位置**: `internal/app/app.go` (第163-193行)

## 📁 数据源详细实现

### 1. MaxMind 数据源

#### 基本信息
- **文件**: `internal/datasource/maxmind/maxmind.go`
- **数据格式**: MMDB (MaxMind Database)
- **支持类型**: Country, City, ASN

#### 核心方法
- **Fetch()** (第172-202行): 并发下载多个MMDB文件
- **ParseAndUpdate()** (第210-278行): 流式解析避免OOM
- **Update()** (第285-297行): 批量入库

#### 下载流程
```go
func (m *Maxmind) FetchSingle(ctx context.Context, url string, dbName string) error {
    // 1. 检查下载去重（支持强制下载）
    checkOptions := utils.DefaultNoNeedDownloadOptions()
    checkOptions.Force = m.force
    
    // 2. 下载tar.gz文件
    auth := utils.Auth{
        Username: os.Getenv("maxmind_id"),
        Password: os.Getenv("maxmind_license_key"),
        Type:     &typ,
    }
    
    // 3. 解压并移动MMDB文件
    utils.ExtractTarGz(tempPath, m.dataDir, m.logger)
    m.MoveMMDBFiles(".mmdb")
}
```

#### 解析流程
- **Country MMDB**: 流式解析国家信息
- **City MMDB**: 流式解析城市信息  
- **ASN MMDB**: 传统解析ASN信息

### 2. IP2Location 数据源

#### 基本信息
- **文件**: `internal/datasource/ip2location/ip2location.go`
- **数据格式**: CSV (ZIP压缩)
- **支持类型**: DB11LITE, PX12LITE, DBASNLITE

#### 核心方法
- **Fetch()** (第59-124行): 下载ZIP文件并解压
- **ParseAndUpdate()** (第191-247行): 流式解析CSV文件
- **Update()** (第249-261行): 批量入库

#### 下载流程
```go
func (i *IP2Location) FetchSingle(ctx context.Context, url string, dbName string) error {
    // 1. 检查文件是否需要更新（6天缓存）
    t := 60 * 6 * 24 * time.Hour
    ok, err := utils.NoNeedDownload(dbPath, m.logger, &t)
    
    // 2. 下载ZIP文件
    utils.Download(ctx, url, tempFile, m.logger, nil)
    
    // 3. 解压ZIP文件
    utils.ExtractZip(tempPath, m.dataDir, m.logger)
}
```

#### CSV解析
- **DB11LITE**: IP-COUNTRY-REGION-CITY-LATITUDE-LONGITUDE-ZIPCODE-TIMEZONE
- **PX12LITE**: IP-PROXYTYPE-COUNTRY-REGION-CITY-ISP-DOMAIN-USAGETYPE-ASN-LASTSEEN-THREAT-RESIDENTIAL-PROVIDER-FRAUDSCORE (16列)
- **DBASNLITE**: IP-ASN

### 3. DB-IP 数据源

#### 基本信息
- **文件**: `internal/datasource/dbip/dbip.go`
- **数据格式**: MMDB.GZ
- **支持类型**: Country-Lite, City-Lite

#### 核心方法
- **Fetch()** (第54-113行): 智能日期检测下载
- **ParseAndUpdate()** (第225-278行): 流式解析MMDB
- **Update()** (第280-292行): 批量入库

#### 智能下载
```go
// 尝试最近3个月的数据
for monthOffset := 0; monthOffset < 3; monthOffset++ {
    targetDate := time.Now().AddDate(0, -monthOffset, 0).Format("2006-01")
    downloadURL := strings.Replace(database.urlTemplate, "{YYYY-MM}", targetDate, 1)
    
    err := d.fetchSingle(ctx, downloadURL, database.filename)
    if err == nil {
        break // 下载成功，退出循环
    }
}
```

### 4. IPAPI 数据源

#### 基本信息
- **文件**: `internal/datasource/ipapi/ipapi.go`
- **数据格式**: CSV (ZIP压缩)
- **支持类型**: IPv4, IPv6

#### 核心方法
- **Fetch()** (第54-121行): 按URL basename下载
- **ParseAndUpdate()** (第129-160行): 流式解析CSV
- **Update()** (第162-174行): 批量入库

#### 文件命名策略
```go
// 根据URL提取文件名
fileName := filepath.Base(url)
if fileName == "" || fileName == "." || fileName == "/" {
    fileName = fmt.Sprintf("ipapi_%d.zip", idx)
}
```

### 5. IPLocate 数据源

#### 基本信息
- **文件**: `internal/datasource/iplocate/iplocate.go`
- **数据格式**: CSV + MMDB
- **支持类型**: CSV, ASN MMDB, Country MMDB

#### 核心方法
- **Fetch()** (第55-134行): 智能文件类型检测
- **ParseAndUpdate()** (第142-202行): 多格式解析
- **Update()** (第281-308行): CIDR转换后入库

#### 多格式处理
```go
func (i *IPLocate) ParseAndUpdate(ctx context.Context) error {
    // 1. 解析CSV文件
    csvFile := filepath.Join(i.dataDir, "iplocate.csv")
    
    // 2. 解析ASN MMDB文件
    asnFile := filepath.Join(i.dataDir, "ip-to-asn.mmdb")
    
    // 3. 解析Country MMDB文件
    countryFile := filepath.Join(i.dataDir, "ip-to-country.mmdb")
}
```

### 6. QQWry 数据源

#### 基本信息
- **文件**: `internal/datasource/qqwry/qqwry_datasource.go`
- **数据格式**: DAT (纯真IP数据库)
- **支持类型**: IPv4 (中国地区详细)

#### 核心方法
- **Fetch()** (第77-111行): 下载DAT文件
- **ParseAndUpdate()** (第165-185行): 流式解析DAT
- **parseAllRecordsStream()** (第247-394行): 二进制解析

#### 二进制解析
```go
func (q *QQWryDatasource) parseAllRecordsStream(ctx context.Context, dbAdapter database.DatabaseInterface) error {
    // 读取索引开始和结束位置
    indexStart := binary.LittleEndian.Uint32(data[:4])
    indexEnd := binary.LittleEndian.Uint32(data[4:8])
    
    // 遍历所有索引记录
    for offset := indexStart; offset <= indexEnd; offset += indexLen {
        // 读取IP范围和位置信息
        startIP := binary.LittleEndian.Uint32(data[offset : offset+4])
        recordOffset := byte3ToUInt32(data[offset+4 : offset+7])
        
        // 批量写入数据库
        if len(batch) >= batchSize {
            dbAdapter.BatchUpsertIPs(ctx, batch)
        }
    }
}
```

## 🔄 数据融合机制

### 融合引擎
- **文件**: `internal/datasource/update_manager.go`
- **函数**: `performDataFusion()` (第186行)

### 融合策略
1. **优先级排序**: MaxMind > IP2Location > DB-IP > IPAPI > IPLocate > QQWry
2. **字段合并**: 高优先级数据源的字段覆盖低优先级
3. **置信度计算**: 基于数据源可靠性和数据完整性

## 🗄️ 数据库入库

### 批量入库接口
- **接口**: `database.DatabaseInterface.BatchUpsertIPs()`
- **策略**: UPSERT (存在则更新，不存在则插入)
- **批次大小**: 1000条记录/批次

### 流式处理
为避免大数据集导致的内存溢出(OOM)，所有数据源都支持流式处理：

```go
// 检查是否支持流式处理
if streamProcessor, ok := ds.(interface{ ParseAndUpdate(ctx context.Context) error }); ok {
    err := streamProcessor.ParseAndUpdate(ctx)
    // 流式处理已经直接写入数据库，跳过后续步骤
}
```

## 📈 监控和状态

### 更新状态跟踪
- **文件**: `internal/datasource/update_manager.go`
- **结构**: `UpdateStatus` 和 `UpdateResult`

### 状态字段
```go
type UpdateStatus struct {
    Source      string    `json:"source"`
    Status      string    `json:"status"`        // running, completed, failed
    StartTime   time.Time `json:"start_time"`
    EndTime     time.Time `json:"end_time"`
    Progress    int       `json:"progress"`      // 0-100
    Records     int64     `json:"records"`
    Error       string    `json:"error,omitempty"`
    LastUpdated time.Time `json:"last_updated"`
}
```

### 查看数据源状态 API
```bash
curl -H "Authorization: Bearer <token>" \
  http://localhost:8082/api/v1/admin/datasources
```

## 🚨 错误处理和重试

### 重试机制
- **下载重试**: `utils.DownloadWithRetry()`
- **数据库重试**: 内置在 `BatchUpsertIPs()` 中
- **解析错误**: 记录日志并跳过错误记录

### 错误类型
1. **网络错误**: 下载失败、超时
2. **解析错误**: 文件格式错误、数据损坏
3. **数据库错误**: 连接失败、约束冲突
4. **认证错误**: API密钥无效、权限不足

## 🔧 配置和环境变量

### 必需环境变量
```bash
# MaxMind
export maxmind_id="your_maxmind_id"
export maxmind_license_key="your_license_key"

# IP2Location  
export ip2location_key="your_api_token"
```

### 配置文件
- **主配置**: `config/config.yaml`
- **示例配置**: `config/config.example.yaml`

## 📝 最佳实践

### 1. 生产环境建议
- 启用 `startup.auto_update_datasources: false` 以提升启动速度
- 使用 API 手动触发更新以便监控和控制
- 设置合理的调度时间避免资源竞争

### 2. 监控建议
- 定期检查数据源状态 API
- 监控更新日志和错误信息
- 设置数据源更新失败告警

### 3. 性能优化
- 使用流式处理避免内存溢出
- 启用下载去重减少重复下载
- 合理设置批次大小平衡性能和内存使用

## 🎯 数据源优先级和融合策略

### 优先级配置
- **文件**: `internal/datasource/data_fusion.go`
- **配置**: `DefaultSourcePriorities` (第29-40行)

```go
var DefaultSourcePriorities = []SourcePriority{
    {"maxmind", 90},       // MaxMind 数据质量最高
    {"ip2location", 85},   // IP2Location 商业数据源
    {"iplocate", 80},      // IPLocate MMDB数据
    {"qqwry", 78},         // QQWry 纯真IP数据库（中文地理位置优势）
    {"dbip", 75},          // DB-IP 免费数据
    {"ipapi", 70},         // IP-API 免费API
    {"ipinfodb", 60},      // IPInfoDB 数据
    {"ipinfo", 55},        // IPInfo API（免费版限制较多）
    {"ipgeolocation", 50}, // IPGeolocation API
    {"ipstack", 45},       // IPStack API
}
```

### 融合算法
1. **按优先级排序**: 高优先级数据源优先
2. **字段级融合**: 逐字段合并，高优先级覆盖低优先级
3. **置信度计算**: 基于数据源可靠性和完整性
4. **元数据更新**: 记录数据来源和融合时间

## 📊 数据源文件结构

### 数据目录结构
```
data/
├── maxmind/
│   ├── GeoLite2-Country.mmdb
│   ├── GeoLite2-City.mmdb
│   └── GeoLite2-ASN.mmdb
├── ip2location/
│   ├── IP2LOCATION-LITE-DB1.CSV
│   ├── IP2LOCATION-LITE-DB11.CSV
│   └── IP2PROXY-LITE-PX12.CSV
├── dbip/
│   ├── dbip-country-lite.mmdb
│   └── dbip-city-lite.mmdb
├── ipapi/
│   ├── geolocationDatabaseIPv4.csv
│   └── geolocationDatabaseIPv6.csv
├── iplocate/
│   ├── iplocate.csv
│   ├── ip-to-asn.mmdb
│   └── ip-to-country.mmdb
└── qqwry/
    └── qqwry.dat
```

### 文件格式说明

#### MaxMind MMDB 格式
- **Country**: 国家级地理位置信息
- **City**: 城市级地理位置信息（包含经纬度）
- **ASN**: 自治系统号码和组织信息

#### IP2Location CSV 格式
- **DB11LITE**: 8列 - IP范围、国家、地区、城市、经纬度、邮编、时区
- **PX12LITE**: 16列 - IP范围、代理类型、国家、地区、城市、ISP、域名、用途类型、ASN、最后发现、威胁、住宅、提供商、欺诈分数

#### DB-IP MMDB 格式
- **Country-Lite**: 国家级信息
- **City-Lite**: 城市级信息

#### IPAPI CSV 格式
- **IPv4/IPv6**: IP范围、国家、地区、城市、经纬度、时区等

#### IPLocate 混合格式
- **CSV**: 基础地理位置信息
- **ASN MMDB**: ASN和组织信息
- **Country MMDB**: 国家信息

#### QQWry DAT 格式
- **二进制格式**: 纯真IP数据库，中文地理位置信息

## 🔄 完整更新流程示例

### 1. MaxMind 更新流程
```bash
# 1. 手动触发更新
curl -X POST "http://localhost:8082/api/v1/admin/fetch" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"sources": ["maxmind"], "force": true}'

# 2. 流程步骤
# Step 1: 下载 (10% 进度)
#   - 并发下载 Country、City、ASN 三个 tar.gz 文件
#   - 使用 MaxMind 认证 (maxmind_id + maxmind_license_key)
#   - 检查下载去重 (7天内跳过，force=true 强制下载)

# Step 2: 解压 (20% 进度)
#   - 解压 tar.gz 文件到临时目录
#   - 移动 .mmdb 文件到 data/maxmind/ 目录

# Step 3: 流式解析 (30-80% 进度)
#   - Country MMDB: 流式解析国家信息
#   - City MMDB: 流式解析城市信息
#   - ASN MMDB: 传统解析ASN信息

# Step 4: 数据融合 (80-90% 进度)
#   - 按 MaxMind 优先级(90)进行数据融合
#   - 与现有数据库数据合并

# Step 5: 批量入库 (90-100% 进度)
#   - 使用 BatchUpsertIPs 批量写入
#   - 1000条记录/批次
#   - UPSERT 策略 (存在则更新)
```

### 2. 多数据源并发更新
```bash
# 并发更新多个数据源
curl -X POST "http://localhost:8082/api/v1/admin/fetch" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"sources": ["maxmind", "dbip", "iplocate"], "force": false}'

# 并发处理流程:
# 1. 为每个数据源创建独立的 goroutine
# 2. 并行执行 Fetch -> Parse -> Fusion -> Update
# 3. 收集所有结果并返回统计信息
# 4. 失败的数据源不影响其他数据源的处理
```

## 🛠️ 调试和故障排除

### 调试模式
在代码中设置调试限制，避免处理过多数据：
```go
// internal/utils/utils.go
func SetDebugLimit() int {
    if os.Getenv("DEBUG_MODE") == "true" {
        return 10 // 调试模式只处理10条记录
    }
    return -1 // 生产模式无限制
}
```

### 常见问题

#### 1. MaxMind 认证失败
```bash
# 错误信息
ERROR: maxmind_id and maxmind_license_key not valid

# 解决方案
export maxmind_id="your_account_id"
export maxmind_license_key="your_license_key"
```

#### 2. IP2Location 下载失败
```bash
# 错误信息
ERROR: Failed to download IP2Location file

# 解决方案
export ip2location_key="your_api_token"
# 检查 API 配额和权限
```

#### 3. 数据库连接失败
```bash
# 错误信息
ERROR: failed to batch upsert IP data

# 解决方案
# 检查 PostgreSQL 服务状态
sudo systemctl status postgresql
# 检查连接配置
psql -h localhost -p 5432 -U postgres -d ipinsight
```

#### 4. 内存溢出 (OOM)
```bash
# 错误信息
ERROR: runtime: out of memory

# 解决方案
# 1. 确保使用流式处理 (ParseAndUpdate)
# 2. 减少批次大小
# 3. 增加系统内存
```

### 日志分析
```bash
# 查看更新日志
tail -f logs/ipinsight.log | grep "datasource update"

# 查看错误日志
tail -f logs/ipinsight.log | grep "ERROR"

# 查看特定数据源日志
tail -f logs/ipinsight.log | grep "maxmind"
```

## 📈 性能监控

### 更新性能指标
- **下载速度**: 网络带宽和文件大小
- **解析速度**: CPU性能和数据复杂度
- **入库速度**: 数据库性能和批次大小
- **内存使用**: 流式处理 vs 传统处理

### 典型性能数据
| 数据源 | 文件大小 | 记录数 | 下载时间 | 解析时间 | 入库时间 |
|--------|----------|--------|----------|----------|----------|
| MaxMind City | ~100MB | ~300万 | 2-5分钟 | 10-15分钟 | 5-10分钟 |
| IP2Location | ~50MB | ~400万 | 1-3分钟 | 8-12分钟 | 8-15分钟 |
| DB-IP | ~30MB | ~200万 | 1-2分钟 | 5-8分钟 | 3-6分钟 |
| QQWry | ~10MB | ~60万 | 30秒 | 2-3分钟 | 1-2分钟 |

## 🔒 安全考虑

### API 认证
- **JWT Token**: 用户登录后获取
- **API Key**: 配置文件中预设的密钥
- **权限控制**: 仅管理员可执行数据源更新

### 数据安全
- **传输加密**: HTTPS 下载数据文件
- **存储安全**: 本地文件权限控制
- **访问控制**: 数据库连接认证

### 审计日志
- **操作记录**: 记录所有更新操作
- **用户追踪**: 记录操作用户信息
- **时间戳**: 精确的操作时间记录

## 🔗 相关文档

- [API 文档](./api.md)
- [配置指南](../config/README.md)
- [部署指南](./deployment.md)
- [故障排除](./troubleshooting.md)
- [性能优化](./performance.md)
- [安全配置](./security.md)
