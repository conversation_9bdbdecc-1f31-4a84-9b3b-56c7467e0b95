# ipInsight Quick Setup Guide

## Quick Start

### 1. Requirements
- Docker 20.0+
- Docker Compose 2.0+
- 4GB+ Memory

### 2. Start Services

```bash
# Start database services
docker-compose up -d

# Check service status
docker-compose ps
```

## Database Initialization

### Table Structure Overview

The project includes two complete table sets:

**IP Data Tables:**
- `ip_ranges_new` - Main IP address information table

**User Management Tables:**
- `users` - User information (with complete schema including avatar_url, phone, etc.)
- `user_sessions` - Session management
- `user_permissions` - Permission management
- `user_activity_logs` - Activity logs
- `roles` - Role definitions

### SQL Files Available

**For New Projects:**
- `init_minimal.sql` - Complete initialization script (recommended)

**For Existing Projects:**
- `add_missing_columns.sql` - Add missing user table columns
- `reset_admin_password.sql` - Reset admin password if needed

### SQL Execution Order

**✅ RECOMMENDED: Single Command Setup (For all new projects)**

#### One-Step Complete Initialization
```bash
# Method 1: Automated script (handles TLS issues automatically)
docker exec -i ip-insight-postgres /bin/bash -c "cd /scripts/sql && ./init_database.sh"

# Method 2: Manual execution (if automated fails)
docker exec -i ip-insight-postgres psql -U postgres --no-password -c "CREATE DATABASE ipinsight WITH OWNER = postgres ENCODING = 'UTF8' LC_COLLATE = 'C' LC_CTYPE = 'C' CONNECTION LIMIT = -1;" || echo "Database may already exist"
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight < scripts/sql/init_minimal.sql
```

**What it includes:**
- Complete database schema (IP data + User management)
- Only tables actually used by the code
- Essential indexes for optimal performance
- Default admin accounts (admin/admin123, operator/admin123)
- Full idempotency - safe to run multiple times
- Complete user schema with all required fields
- Complete in < 1 minute

## Setup Instructions

### For New Projects (Fresh Installation)

```bash
# 1. Start services
docker-compose up -d

# 2. Initialize database (complete setup)
# Method 1: One-step automated initialization (RECOMMENDED)
docker exec -i ip-insight-postgres /bin/bash -c "cd /scripts/sql && ./init_database.sh"

# Method 2: Manual step-by-step (if automated fails)
docker exec -i ip-insight-postgres psql -U postgres --no-password -c "CREATE DATABASE ipinsight WITH OWNER = postgres ENCODING = 'UTF8' LC_COLLATE = 'C' LC_CTYPE = 'C' CONNECTION LIMIT = -1;" || echo "Database may already exist"
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight < scripts/sql/init_minimal.sql

# 3. Verify
curl http://localhost:8080/health
```

### For Existing Projects (With Historical Data)

If you have existing tables but encounter schema mismatch errors:

```bash
# 1. Add missing columns to existing tables
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight < scripts/sql/add_missing_columns.sql

# 2. Reset admin password if needed
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight < scripts/sql/reset_admin_password.sql

# 3. Verify login works
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## Verify Installation

### Check Tables Created Successfully
```sql
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight -c "
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;"
```

### Check Default Users
```sql
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight -c "
SELECT username, role, status 
FROM users 
ORDER BY created_at;"
```

### Test API Connection
```bash
# Start application service
docker-compose up -d ipinsight

# Test health check
curl http://localhost:8080/health

# Test login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## Configuration

Edit `config/config.yaml`:

```yaml
# Database configuration
database:
  host: postgres
  port: 5432
  user: postgres
  password: postgres
  dbname: ipinsight

# Redis configuration  
cache:
  host: redis
  port: 6379

# Startup configuration
startup:
  auto_update_datasources: false  # Recommended to disable for first startup
```

## Common Maintenance Commands

### Database Maintenance
```bash
# Clean expired sessions
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight -c "SELECT cleanup_expired_sessions();"

# Update statistics
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight -c "ANALYZE;"
```

### View Logs
```bash
# View application logs
docker-compose logs -f ipinsight

# View database logs  
docker-compose logs -f postgres
```

### Data Backup
```bash
# Backup database
docker exec -i ip-insight-postgres pg_dump -U postgres --no-password ipinsight > backup_$(date +%Y%m%d).sql
```

## Important Notes

1. **Choose the right script for your situation:**
   - New projects: Use `init_minimal.sql`
   - Existing projects: Use `add_missing_columns.sql` and `reset_admin_password.sql`
2. **Production environments need to change default passwords** - admin/admin123 is for development only
3. **First startup should disable auto datasource updates** - Avoid long startup times
4. **All scripts are idempotent** - Safe to run multiple times
5. **Schema compatibility** - The user table now includes all required fields (avatar_url, phone, etc.)

## Troubleshooting

### Common Issues

**Container startup failure:**
```bash
# Check port usage
netstat -tlnp | grep :8080

# Reset Docker services
docker-compose down
docker-compose up -d
```

**Database connection failure:**
```bash
# Check database status
docker exec -i ip-insight-postgres pg_isready -U postgres

# View detailed errors
docker-compose logs postgres
```

**Schema mismatch errors (missing columns):**
```bash
# Fix missing user table columns
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight < scripts/sql/add_missing_columns.sql
```

**Password verification failures:**
```bash
# Reset admin password to admin123
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight < scripts/sql/reset_admin_password.sql
```

**TLS connection errors:**
```bash
# Use --no-password flag to avoid TLS issues
docker exec -i ip-insight-postgres psql -U postgres --no-password -d ipinsight -c "SELECT version();"
```

**Table creation failure:**
- Check if SQL is executed in correct order
- Confirm PostgreSQL version supports required features
- Review error messages during execution
- For existing projects, use migration scripts instead of init_minimal.sql

---

*Quick Setup Guide - Version 1.0*